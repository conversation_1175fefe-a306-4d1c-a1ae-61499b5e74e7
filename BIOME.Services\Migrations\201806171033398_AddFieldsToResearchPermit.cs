namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddFieldsToResearchPermit : DbMigration
    {
        public override void Up()
        {
            RenameColumn(table: "dbo.ResearchPermitApplications", name: "AtResearchApplication_Id", newName: "ResearchApplicationId");
            RenameIndex(table: "dbo.ResearchPermitApplications", name: "IX_AtResearchApplication_Id", newName: "IX_ResearchApplicationId");
            AddColumn("dbo.ResearchPermitApplications", "SpecimenSpeciesName", c => c.String());
            AddColumn("dbo.ResearchPermitApplications", "SpecimenQuantity", c => c.String());
            AddColumn("dbo.ResearchPermitApplications", "ResearchTypeId", c => c.String());
            AddColumn("dbo.ResearchPermitApplications", "ResearchTypeName", c => c.String());
        }
        
        public override void Down()
        {
            DropColumn("dbo.ResearchPermitApplications", "ResearchTypeName");
            DropColumn("dbo.ResearchPermitApplications", "ResearchTypeId");
            DropColumn("dbo.ResearchPermitApplications", "SpecimenQuantity");
            DropColumn("dbo.ResearchPermitApplications", "SpecimenSpeciesName");
            RenameIndex(table: "dbo.ResearchPermitApplications", name: "IX_ResearchApplicationId", newName: "IX_AtResearchApplication_Id");
            RenameColumn(table: "dbo.ResearchPermitApplications", name: "ResearchApplicationId", newName: "AtResearchApplication_Id");
        }
    }
}
