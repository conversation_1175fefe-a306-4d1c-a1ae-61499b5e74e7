﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.Spatial;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchPermitApplication : Entity<long>,IDescribableEntity
    {
        [Index]
        [MaxLength(256)]
        public string PermitNumber { get; set; }
        public int NoTimesRejected { get; set; }
        public virtual ICollection<ResearchPermitStatus> Status { get; set; }
        public virtual ICollection<ResearchPermitLetter> Letters { get; set; }
        public virtual ICollection<ResearchPermitPass> Passes { get; set; }
        public virtual ApplicationUser ActionByPermitManager { get; set; }
        public virtual ResearchApprovedDate DateApproved { get; set; }
        public virtual ResearchIssuedDate DateIssued { get; set; }

        [NotMapped]
        public DateTimeOffset? DateExpire
        {
            get
            {
                if (DateApproved != null)
                {
                    return DateApproved.SavedDate.AddYears(1);
                }
                return null;
            }
        }

        public virtual ICollection<ResearchFieldSurveyTeamMember> FieldSurveyTeamMembers { get; set; }
        public string Methodology { get; set; }
        public List<long> StudySubjectIds { get; set; }
        public string SpecimenSpeciesName { get; set; }
        public string SpecimenQuantity { get; set; }
      
        public long? ResearchApplicationId { get; set; }
        public string ResearchTypeId { get; set; }
        public string ResearchTypeName { get; set; }
        public string StudyLocationsDrawn { get; set; }
        public virtual ICollection<ResearchPermitStudyLocation> StudyLocationAccesses { get; set; }
        public string StudyLocationDescription { get; set; }
        public DateTimeOffset? StudyPeriodStart { get; set; }
        public DateTimeOffset? StudyPeriodEnd { get; set; }

        [MaxLength(10)]
        public string SubmittedType { get; set; } //1 - amend, 2 - renew
        public decimal CoralCollectionAmountInCubicMeter { get; set; } //CR3&4 Phase2

        public bool IsIndex { get; set; } //elastic search
        public DateTimeOffset? IndexedDateTime { get; set; }
        public string Describe()
        {
            return "{ PermitNumber : \"" + PermitNumber + "\", NoTimesRejected : \"" + NoTimesRejected + "\", DateExpire : \"" + DateExpire
                + "\", Methodology : \"" + Methodology + "\", SpecimenSpeciesName : \"" + SpecimenSpeciesName
                + "\", SpecimenQuantity : \"" + SpecimenQuantity + "\", ResearchApplicationId : \"" + ResearchApplicationId + "\", ResearchTypeId : \"" + ResearchTypeId + "\", ResearchTypeName : \"" + ResearchTypeName
                + "\", StudyLocationsDrawn : \"" + StudyLocationsDrawn + "\", StudyLocationDescription : \"" + StudyLocationDescription + "\", CoralCollectionAmountInCubicMeter : \"" + CoralCollectionAmountInCubicMeter
                + "}";
        }

        [JsonIgnore]
        public virtual ResearchApplication AtResearchApplication { get; set; }

        [Required]
        public virtual ICollection<ConfSightingCategory> StudySubjects { get; set; }

        public ResearchPermitApplication()
        {
            NoTimesRejected = 0;
            Letters = new List<ResearchPermitLetter>();
            Passes = new List<ResearchPermitPass>();
            Status = new List<ResearchPermitStatus>();
            FieldSurveyTeamMembers = new List<ResearchFieldSurveyTeamMember>();
            StudyLocationAccesses = new List<ResearchPermitStudyLocation>();
            StudySubjects = new List<ConfSightingCategory>();
        }
    }
}
