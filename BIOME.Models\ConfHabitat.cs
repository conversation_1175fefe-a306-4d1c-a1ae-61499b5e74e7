﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ConfHabitat : Entity<long>, ISoftDelete,IDescribableEntity 
    {
        public string Name { get; set; }
        public bool IsEnable { get; set; }
        public string Describe()
        {
            return "{ Name : \"" + Name + "\", IsEnable : \"" + IsEnable + "\", IsDeleted : \"" + IsDeleted + "}";
        }
        [JsonIgnore]
        public virtual ICollection<ResearchApplication> ResearchApplications { get; set; }

        [JsonIgnore]
        public virtual ICollection<ResourceMetaData> ResourceMetaDatas { get; set; }

        public bool IsDeleted { get; set; }

        public ConfHabitat()
        {
            ResearchApplications = new List<ResearchApplication>();
            ResourceMetaDatas = new List<ResourceMetaData>();
        }
    }
}
