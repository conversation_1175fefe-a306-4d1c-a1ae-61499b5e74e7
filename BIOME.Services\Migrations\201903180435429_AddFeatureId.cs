namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddFeatureId : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.EmailOTPs",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        LoginUserId = c.Long(nullable: false),
                        Email = c.String(),
                        OTPCode = c.String(),
                        IsValid = c<PERSON>(nullable: false),
                        OTPExpiredDate = c.DateTime(nullable: false),
                        OTPCreatedDate = c.DateTime(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveyMemberInviteds",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        SurveyId = c.Long(nullable: false),
                        MemberId = c.Long(nullable: false),
                        InviteTimes = c.Int(nullable: false),
                        Email = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Surveys", t => t.SurveyId, cascadeDelete: true)
                .Index(t => t.SurveyId);
            
            AddColumn("dbo.ResearchStudyLocations", "FeatureId", c => c.String());
            AddColumn("dbo.ResearchPermitStudyLocations", "FeatureId", c => c.String());
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.SurveyMemberInviteds", "SurveyId", "dbo.Surveys");
            DropIndex("dbo.SurveyMemberInviteds", new[] { "SurveyId" });
            DropColumn("dbo.ResearchPermitStudyLocations", "FeatureId");
            DropColumn("dbo.ResearchStudyLocations", "FeatureId");
            DropTable("dbo.SurveyMemberInviteds");
            DropTable("dbo.EmailOTPs");
        }
    }
}
