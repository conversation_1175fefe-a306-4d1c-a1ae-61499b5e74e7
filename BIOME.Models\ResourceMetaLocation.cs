﻿//using Nest;
using Nest;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResourceMetaLocation : Entity<long>, IDescribableEntity
    {
        public double Latitude { get; set; }
        public double Longitude { get; set; }

        [NotMapped]
        //[GeoPoint(LatLon =true)] // Commented because Elastic Search libs have been removed.
        [GeoPoint]
        public string MetaLocation
        {
            get { return Latitude.ToString() + "," + Longitude.ToString(); }
            set { }
        }
        [JsonIgnore]
        public virtual ResourceMetaData AtResourceMetaData { get; set; }

        public string Describe()
        {
            return "{ Latitude : \"" + Latitude + "\", Longitude : \"" + Longitude  
                + "\", ResourceMetaData : \"" + AtResourceMetaData?.Id   + "}";
        }
    }
}
