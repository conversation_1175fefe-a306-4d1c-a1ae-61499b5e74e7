﻿using Microsoft.AspNet.Identity.EntityFramework;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class BIOMEUserStore : UserStore<ApplicationUser, BIOME<PERSON><PERSON>, long, BIO<PERSON><PERSON><PERSON>Log<PERSON>, BIOME<PERSON><PERSON>R<PERSON>, BIOMEU<PERSON>Claim>
    {
        public BIOMEUserStore(DbContext context) : base(context)
        {

        }

        public override async Task CreateAsync(ApplicationUser user)
        {
            await base.CreateAsync(user);
            await AddToPreviousPasswordsAsync(user, user.PasswordHash);
        }

        public Task AddToPreviousPasswordsAsync(ApplicationUser user, string password)
        {
            if (string.IsNullOrEmpty(password))
            {
                return Task.FromResult<object>(1);
            }

            user.PreviousUserPasswords.Add(new PreviousPassword() { UserId = user.Id, PasswordHash = password });
            return UpdateAsync(user);
        }
    }
}
