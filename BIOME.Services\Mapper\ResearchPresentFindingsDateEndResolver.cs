﻿using AutoMapper;
using BIOME.Models;
using BIOME.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public class ResearchPresentFindingsDateEndResolver : ValueResolver<PermitViewModel.ApplicationFormViewModelBase, DateTimeOffset?>
    {
        protected override DateTimeOffset? ResolveCore(PermitViewModel.ApplicationFormViewModelBase source)
        {
            if (!source.PresentFindings)
            {
                return null;
            }

            return source.PresentFindingsDateEnd;
        }
    }

    public class ResearchPresentFindingsDateEndDraftResolver : ValueResolver<ResearchApplicationDraft, DateTimeOffset?>
    {
        protected override DateTimeOffset? ResolveCore(ResearchApplicationDraft source)
        {
            if (!source.PresentFindings)
            {
                return null;
            }

            return source.PresentFindingsDateEnd;
        }
    }
}
