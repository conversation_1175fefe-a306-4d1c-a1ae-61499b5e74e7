﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    
    public abstract class Group : Entity<long>, ISoftDelete
    {
        public bool IsDeleted { get; set; }

        public string Name { get; set; }
        [JsonIgnore]
        public virtual IList<ApplicationUser> Users { get; set; }
        [JsonIgnore]
        public virtual IList<ResourceFilePermission> ResourceFilePermission { get; set; }
        [JsonIgnore]
        public virtual IList<ResourceMetaData> ResourceMetaData { get; set; }

       

        public Group()
        {
            Users = new List<ApplicationUser>();
            ResourceFilePermission = new List<ResourceFilePermission>();
            ResourceMetaData = new List<ResourceMetaData>();

        }
    }

    public class MainGroup : Group,IDescribableEntity
    {
        public string Domain { get; set; }

        public string Describe()
        {
            return "{ Name : \"" + Name + "\", IsDeleted : \"" + IsDeleted + "\", Domain : \"" + Domain + "}";
        }
        [Required]
        public virtual IList<SubGroup> SubGroups { get; set; }

        public MainGroup()
        {
            SubGroups = new List<SubGroup>();
        }
    }

    public class SubGroup : Group, IDescribableEntity
    {
        public long MainGroupId { get; set; }
        public string Describe()
        {
            return "{ Name : \"" + Name + "\", IsDeleted : \"" + IsDeleted + "\", MainGroupId : \"" + MainGroupId + "}";
        }
        [ForeignKey("MainGroupId")]
        public virtual MainGroup ParentGroup { get; set; }
        [Required]
        public virtual IList<SubSubGroup> SubSubGroups { get; set; }

        public SubGroup()
        {
            SubSubGroups = new List<SubSubGroup>();
        }
    }

    public class SubSubGroup : Group, IDescribableEntity
    {
        public long SubGroupId { get; set; }
        public string Describe()
        {
            return "{ Name : \"" + Name + "\", IsDeleted : \"" + IsDeleted + "\", SubGroupId : \"" + SubGroupId + "}";
        }
        [ForeignKey("SubGroupId")]
        public virtual SubGroup ParentGroup { get; set; }
    }
    
}
