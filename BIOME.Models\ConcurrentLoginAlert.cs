﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ConcurrentLoginAlert : Entity<long>
    {
        [Required]
        public long UserId { get; set; }
        [Required]
        public int PlatFormID { get; set; }
        [Required]
        public int MessageType { get; set; }
        [Required]
        public string Message { get; set; }
        
    }
}
