﻿//using ActionMailerNext;
//using ActionMailerNext.Standalone;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//using ActionMailerNext.Standalone.Helpers;
using System.IO;
using System.Net.Mail;
using BIOME.Models.EmailTemplates;

namespace BIOME.Services
{
    /*public class EmailController : RazorMailerBase
    {
        public override string GlobalViewPath
        {
            get
            {
                var appDomain = System.AppDomain.CurrentDomain;
                var basePath = appDomain.RelativeSearchPath ?? appDomain.BaseDirectory;
                return Path.Combine(basePath, "Views", "EmailTemplates");
            }
        }

        public override ViewSettings ViewSettings
        {
            get
            {
                return new ViewSettings
                {
                    Hostname = "test.com",
                    Protocol = "http",
                    UrlPattern = "{controller}/{action}/{id}"
                };
            }
        }

        public RazorEmailResult TestEmail(EmailInfoBase model)
        {
            
            // Setting up needed properties
            MailAttributes.From = new MailAddress(model.Sender, model.SenderName);
            MailAttributes.To.AddRange(model.Recipients.Select(r => new MailAddress(r)));
            MailAttributes.Subject = model.Subject;
            
            //Calling the view which form the email body
            return Email("TestEmail", model);
        }

        public RazorEmailResult SendResetPasswordEmail(ResetPasswordEmailInfo model)
        {
            MailAttributes.From = new MailAddress(model.Sender, model.SenderName);
            MailAttributes.To.AddRange(model.Recipients.Select(r => new MailAddress(r)));
            MailAttributes.Subject = model.Subject;

            return Email("ResetPassword", model);
        }

        public RazorEmailResult SendReactivateAccountEmail(ReactivateAccountEmailInfo model)
        {
            MailAttributes.From = new MailAddress(model.Sender, model.SenderName);
            MailAttributes.To.AddRange(model.Recipients.Select(r => new MailAddress(r)));
            MailAttributes.Subject = model.Subject;

            return Email("ReactivateAccount", model);
        }

        public RazorEmailResult SendActivateAccountEmail(ReactivateAccountEmailInfo model)
        {
            MailAttributes.From = new MailAddress(model.Sender, model.SenderName);
            MailAttributes.To.AddRange(model.Recipients.Select(r => new MailAddress(r)));
            MailAttributes.Subject = model.Subject;

            return Email("ActivateAccount", model);
        }

        public RazorEmailResult SendDiscussionNotification(DiscussionNotificationEmailInfo model)
        {
            MailAttributes.From = new MailAddress(model.Sender, model.SenderName);
            MailAttributes.To.AddRange(model.Recipients.Select(r => new MailAddress(r)));
            MailAttributes.Subject = model.Subject;

            return Email("NotificationNewDiscussion", model);

        }

        public RazorEmailResult SendDiscussionAnswerNotification(DiscussionAnaswerNotificationEmailInfo model)
        {
            MailAttributes.From = new MailAddress(model.Sender, model.SenderName);
            MailAttributes.To.AddRange(model.Recipients.Select(r => new MailAddress(r)));
            MailAttributes.Subject = model.Subject;

            return Email("NotificationNewDiscussionAnswer", model);

        }

        public RazorEmailResult SendFeedbackEmail(SendFeedbackEmailInfo model)
        {
            MailAttributes.From = new MailAddress(model.Sender, model.SenderName);
            MailAttributes.To.AddRange(model.Recipients.Select(r => new MailAddress(r)));
            MailAttributes.Subject = model.Subject;

            return Email("SendFeedBack", model);
        }
    }*/
}
