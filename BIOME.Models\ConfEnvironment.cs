﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ConfEnvironment : Entity<long>,IDescribableEntity 
    {
        public string Name { get; set; }
        public bool IsEnable { get; set; }

        public string Describe()
        {
            return "{ Name : \"" + Name + "\", IsEnable : \"" + IsEnable + "}";
        }

        [JsonIgnore]
        public virtual ICollection<ResourceMetaData> ResourceMetaDatas { get; set; }

        public ConfEnvironment()
        {
            ResourceMetaDatas = new List<ResourceMetaData>();
        }
    }
}
