﻿using AutoMapper;
using BIOME.Models;
using BIOME.ViewModels;
using BIOME.ViewModels.API.v1_0;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Utilities.Helpers;

namespace BIOME.Services
{
    public class MaintenanceNoticeService : IMaintenanceNoticeService
    {
        #region Fields

        private readonly ApplicationDbContext dbContext;
        

        private static int CacheSeconds = 3;
        private static MaintenanceNotice cacheMaintenanceNotice;
        private static DateTimeOffset updateMaintenanceNotice = DateTimeOffset.Now;
        

        #endregion

        #region Constructors

        public MaintenanceNoticeService(ApplicationDbContext dbContext)
        {
            this.dbContext = dbContext;
            
            
        }

        #endregion

        #region Public Methods

        public bool GetStatus()
        {
            var maintenanceNotice = GetMaintenanceNoticeInternal();

            return maintenanceNotice.status;
        }
        
        public DateTimeOffset GetStartFrom()
        {
            var maintenanceNotice = GetMaintenanceNoticeInternal();

            return maintenanceNotice.startFrom;
        }

        public DateTimeOffset GetEndAt()
        {
            var maintenanceNotice = GetMaintenanceNoticeInternal();

            return maintenanceNotice.endAt;
        }

        public string GetNoticeTitle()
        {
            var maintenanceNotice = GetMaintenanceNoticeInternal();

            return maintenanceNotice.noticeTitle;
        }

        public string GetNoticeMessage()
        {
            var maintenanceNotice = GetMaintenanceNoticeInternal();

            return maintenanceNotice.noticeMessage;
        }
        

        public MaintenanceNoticeViewModel GetMaintenanceNotice()
        {
            var maintenanceNotice = GetMaintenanceNoticeInternal();

            return Mapper.Map<MaintenanceNoticeViewModel>(maintenanceNotice);
        }

        public ApiModelSystem.ResponseNotice GetAPIMaintenanceNotice()
        {
            var maintenanceNotice = GetMaintenanceNoticeInternal();

            if (maintenanceNotice.status == false || (maintenanceNotice.startFrom > DateTime.Now || maintenanceNotice.endAt < DateTime.Now))
            {
                return null;
            }

            return Mapper.Map<ApiModelSystem.ResponseNotice>(maintenanceNotice);
        }

        

        public async Task<ServiceResult> UpdateAndRefreshMaintenanceNotice(long userId,MaintenanceNoticeViewModel maintenanceNoticeVM)
        {
            var maintenanceNotice = GetMaintenanceNoticeInternal();

            MaintenanceNoticeViewModel log_before = Mapper.Map<MaintenanceNoticeViewModel>(maintenanceNotice);
            string log_json_before = log_before.ToJson();

            maintenanceNotice.startFrom = DateTimeOffset.ParseExact(maintenanceNoticeVM.startFrom, "dd/MM/yyyy HH:mm",
                                      new CultureInfo("en-US"));
            maintenanceNotice.endAt = DateTimeOffset.ParseExact(maintenanceNoticeVM.endAt, "dd/MM/yyyy HH:mm",
                                      new CultureInfo("en-US"));
            maintenanceNotice.noticeMessage = maintenanceNoticeVM.noticeMessage;
            maintenanceNotice.noticeTitle = maintenanceNoticeVM.noticeTitle;
            maintenanceNotice.status = maintenanceNoticeVM.status;

            var updateTask = dbContext.SaveChangesAsync(false, Convert.ToInt32(userId));

            var changesMade = await updateTask;
            if (updateTask.IsFaulted || updateTask.IsCanceled)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 9011 });
            }

            if (changesMade == 0)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 9001 });
            }

            //var type = new List<int>();
            

            maintenanceNoticeVM = Mapper.Map<MaintenanceNoticeViewModel>(GetMaintenanceNoticeInternal());
            string log_json_after = maintenanceNoticeVM.ToJson();

            return ServiceResult.SuccessWithOutput(string.Format(BIOME.Enumerations.Audit.AuditLogAction.BeforeAfterLogFormat, log_json_before, log_json_after));
        }
        #endregion

        #region Private Methods

        private MaintenanceNotice GetMaintenanceNoticeInternal()
        {
            
            if (dbContext.MaintenanceNotices.Count() == 0)
            {
                //Input default
                var parameters = new MaintenanceNotice()
                {
                    status = false,
                    startFrom = DateTime.Now.AddDays(7),
                    endAt = DateTime.Now.AddDays(14),
                    noticeTitle = "Maintenance Notice",
                    noticeMessage = "Revermping to serve you better!"
                };
                dbContext.MaintenanceNotices.Add(parameters);
                dbContext.SaveChanges();                
            }
            

            return dbContext.MaintenanceNotices.FirstOrDefault();
        }
      

        #endregion
    }
}
