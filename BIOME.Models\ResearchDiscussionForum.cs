﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchDiscussionForum : Entity<long>,IDescribableEntity
    {
        public long AtResearchApplicationId { get; set; }
        [ForeignKey("AtResearchApplicationId")]
        [JsonIgnore]
        public virtual ResearchApplication AtResearchApplication { get; set; }

        public string ForumQuestion { get; set; }
        public long QuestionByUserId { get; set; }

        public string AttachDocumentJSON { get; set; }
        

        public virtual IList<ResearchDiscussionAnswer> ResearchDiscussionAnswer { get; set; }
        public string Describe()
        {
            return "{ AtResearchApplicationId : \"" + AtResearchApplicationId + "\", ForumQuestion : \"" + ForumQuestion + "\", QuestionByUserId : \"" + QuestionByUserId
                + "\", AttachDocumentJSON : \"" + AttachDocumentJSON + "\"}";
        }
        public ResearchDiscussionForum()
        {
            ResearchDiscussionAnswer = new List<ResearchDiscussionAnswer>();
        }
    }
}
