﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchPermitStudyLocation :Entity<long>, IDescribableEntity
    {
        public int Access { get; set; }
        public string Remarks { get; set; }
        public string FeatureId { get; set; }

        public string Describe()
        {
            return "{ Access : \"" + Access + "\", Remarks : \"" + Remarks + "\", FeatureId : \"" + FeatureId + "}";
        }
        public virtual GISLocation Location { get; set; }
        [JsonIgnore]
        public virtual ResearchPermitApplication ResearchPermitApplication { get; set; }

        public ResearchPermitStudyLocation()
        {
            Access = 2;
        }
    }
}
