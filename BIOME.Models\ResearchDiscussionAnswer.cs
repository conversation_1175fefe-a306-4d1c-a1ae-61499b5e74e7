﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;

namespace BIOME.Models
{
    public class ResearchDiscussionAnswer : Entity<long>,IDescribableEntity 
    {
        public long AtDiscussionForumId { get; set; }
        [ForeignKey("AtDiscussionForumId")]
        [JsonIgnore]
        public virtual ResearchDiscussionForum AtResearchDiscussionForum { get; set; }


        public string DiscussionAnswer { get; set; }
        public long AnswerByUserId { get; set; }
        public string AttachDocumentJSON { get; set; }

        public string Describe()
        {
            return "{ AtDiscussionForumId : \"" + AtDiscussionForumId + "\", DiscussionAnswer : \"" + DiscussionAnswer + "\", AnswerByUserId : \"" + AnswerByUserId
                + "\", AttachDocumentJSON : \"" + AttachDocumentJSON + "\"}"; 
        }
        public ResearchDiscussionAnswer()
        {
        }
    }
}
