﻿using AutoMapper;
using BIOME.Constants;
using BIOME.Enumerations;
using BIOME.Models;
using BIOME.ViewModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public class ConfigurationService : ServiceBase
    {
        private static ConfigurationService instance;
        private static bool isInternet;
        private static Enumerations.Configuration.Server.Mode mode;
        private static Type errorResourceType;
        private static string errorResourceNamePrefix;
        private static int accountExpiryPeriod;

        private ConfigurationService() { }

        public static ConfigurationService Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ConfigurationService();
                }
                return instance;
            }
        }

        public bool IsInternet
        {
            get
            {
                return isInternet;
            }
        }

        public int AccountExpiryPeriod
        {
            get
            {
                return accountExpiryPeriod;
            }
            set
            {
                accountExpiryPeriod = value;
            }
        }

        public Enumerations.Configuration.Server.Mode Mode
        {
            get
            {
                return mode;
            }
        }

        public void ConfigMapping()
        {
            //var config = new MapperConfiguration(cfg =>
            //{
            //    cfg.AddProfile<MapperProfile.ViewModelProfile>();
            //});

            Mapper.Initialize(cfg =>
            {
                cfg.AddProfile<MapperProfile.ViewModelProfile>();
                cfg.AddProfile<MapperProfile.ModelProfile>();
            });
            

            //Mapper.CreateMap<CustomerCreateViewModel, Customer>()
            //    .ForMember(c => c.DisplayName, opt => opt.MapFrom(c => c.CompanyName))
            //    .ForMember(c => c.Id, opt => opt.Ignore())
            //    .ForMember(c => c.Company, opt => opt.Ignore())
            //    .ForMember(c => c.Address, opt => opt.Ignore())
            //    .AfterMap((s, d) => d.Company.AccountKey = s.AccountKey)
            //    .AfterMap((s, d) => d.Company.RIRKey = s.CompanyRIRHandler)
            //    .AfterMap((s, d) => d.Company.DBA = s.DBA)
            //    .AfterMap((s, d) => d.Company.FEIN = s.FEIN)
            //    .AfterMap((s, d) => d.Company.Phone = s.CompanyPhone)
            //    .AfterMap((s, d) => d.Company.Fax = s.CompanyFax)
            //    .AfterMap((s, d) => d.Company.Email = s.CompanyEmail)
            //    .AfterMap((s, d) => d.Company.Description = s.CompanyDescription)
            //    .AfterMap((s, d) => d.Company.Name = s.CompanyName)
            //    .AfterMap((s, d) => d.Address.Address = s.Address)
            //    .AfterMap((s, d) => d.Address.City = s.City)
            //    .AfterMap((s, d) => d.Address.Country = s.CountrySelected)
            //    .AfterMap((s, d) => d.Address.State = s.State)
            //    .AfterMap((s, d) => d.Address.ZipCode = s.ZipCode);

            try
            {
                Mapper.AssertConfigurationIsValid();
            }
            catch (AutoMapperConfigurationException ex)
            {
                throw new Exception(ex.ToString());
            }
        }

        public void SetInternetState(string siteType)
        {
            if (siteType == Constants.Configuration.Server.SiteType.Internet)
            {
                isInternet = true;
            }
            else
            {
                isInternet = false;
            }
        }

        public void SetServerMode(string modeString)
        {
            if (Constants.Configuration.Server.Mode.Release == modeString)
            {
                mode = Enumerations.Configuration.Server.Mode.Release;
            }
            else if (Constants.Configuration.Server.Mode.Staging == modeString)
            {
                mode = Enumerations.Configuration.Server.Mode.Staging;
            }
            else
            {
                mode = Enumerations.Configuration.Server.Mode.Debug;
            }
        }

        public void SetErrorResourceType(Type type)
        {
            errorResourceType = type;
        }

        public void SetErrorResourceNamePrefix(string prefix)
        {
            errorResourceNamePrefix = prefix;
        }

       /* public void ConfigEmailPickup()
        {
            SmtpClient smtpClient = new SmtpClient();
            if (smtpClient.DeliveryMethod == SmtpDeliveryMethod.SpecifiedPickupDirectory)
            {
                string root = AppDomain.CurrentDomain.BaseDirectory;
                string pickupPath = smtpClient.PickupDirectoryLocation.Replace("~/", root);
                pickupPath = pickupPath.Replace("/", @"\");

                if (!Directory.Exists(pickupPath))
                    Directory.CreateDirectory(pickupPath);
            }
        }*/
    }
}
