﻿using AutoMapper;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace BIOME.Services
{
    public class PagedListConverter<s, d> : ITypeConverter<IPagedList<s>, IPagedList<d>>
    {
        public IPagedList<d> Convert(ResolutionContext context)
        {
            var models = (PagedList<s>)context.SourceValue;
            var viewModels = models.Select(s => Mapper.Map<s, d>(s)).ToList();
            return new PagedList<d>(viewModels, models.PageIndex, models.PageSize, models.TotalItemCount);
        }
    }
}