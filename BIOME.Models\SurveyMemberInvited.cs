﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class SurveyMemberInvited : Entity<long>,IDescribableEntity
    {
        public long SurveyId { get; set; }
        [ForeignKey("SurveyId")]
        public virtual Survey SurveyDetail { get; set; }
        public long MemberId { get; set; }
        public int InviteTimes { get; set; }
        [MaxLength(320)]
        public string Email { get; set; }
        public string Describe()
        {
            return "{ SurveyId : \"" + SurveyId + "\", MemberId : \"" + MemberId + "\", InviteTimes : \"" + InviteTimes + "\", Email : \"" + Email + "}";
        }
        [NotMapped]
        public ApplicationUser Member { get; set; }

        public SurveyMemberInvited()
        {

        }
    }
}
