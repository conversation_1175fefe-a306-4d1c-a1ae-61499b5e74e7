﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class Token : Entity<long>
    {
        [Required]
        public long UserId { get; set; }
        [Required]
        public string DeveloperKey { get; set; }
        [Required]
        public string AuthToken { get; set; }
        [Required]
        public DateTimeOffset IssuedOn { get; set; }
        [Required]
        public DateTimeOffset ExpiresOn { get; set; }
        public int PlatFormID { get; set; }
    }
}
