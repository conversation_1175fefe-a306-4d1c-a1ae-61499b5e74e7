﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BIOME.Enumerations;

namespace BIOME.Models
{
    public class BackgroundJobTask
    {
        public enum TaskType
        {
            ExpiredResearchApplicationSendEmailOnNoReport,
            PasswordExpired,
            PasswordExpiredNotification,
            PasswordResetSuccess,
            //AccountExpired,
            //AccountExpiredNotification,
            AddIndexForResourceDocument,
            CheckFileForIndexResourceDocument,
            AddIndexForMapResourceDocument,
            IndexForSighting,
            IndexForResource,
        }
        public long Id { get; set; }
        public string JobId { get; set; }
        public string Parameters { get; set; }
        public TaskType Task { get; set; }
        public BackgroundJob.Schedule.ScheduleType Schedule { get; set; }
    }
}
