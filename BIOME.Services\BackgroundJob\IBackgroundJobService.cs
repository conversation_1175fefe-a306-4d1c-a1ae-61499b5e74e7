﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static BIOME.Services.BackgroundJobService;

namespace BIOME.Services
{
    public interface IBackgroundJobService
    {
        void ScheduleRecurrsiveAccountManagement();
        //void ScheduleDeallocateIPNetwork(long id, BackgroundJobService.BackgroundJobScheduleOptions options);
        //void ScheduleFreeIPNetwork(long blockId, long id, BackgroundJobService.BackgroundJobScheduleOptions options, long userId);
        //bool DeleteScheduledJob(string jobId);
        //void RemoveScheduledDeallocateIPNetwork(long id);
        //void RemoveScheduledFreeIPNetwork(long id);
        /*
        void ScheduleRecurrsiveUserUpdateFromACE();*/

        void ScheduleRecurrsiveUserExportToACE();
        void ScheduleRecurrsiveReminderToSiteManagerForPermitApplication(string profileUrl, string emailTemplateUrl,string applicationurl, string contactUsUrl);

        void ScheduleRecurrsiveReminderToSiteManagerForPermitApplicationPending(string profileUrl, string emailTemplateUrl, string applicationurl, string contactUsUrl);
        void RemoveCompletedJob(string jobId);

        void ScheduleRecurrsiveReminderDiscussion();
        void ScheduleRecurrsiveIndexSightingRecords();

        void ScheduleResearchApplicationExpire(long id, string permitUrl, string contactUsUrl, BackgroundJobScheduleOptions options);
        void SchedulePasswordExpired(string title, string recipient, DateTimeOffset passwordExpiryDate, Uri passwordResetLink, Uri contactUsLink, BackgroundJobScheduleOptions options);
        void SchedulePasswordExpiredNotification(string title, string recipient, DateTimeOffset passwordExpiryDate, Uri passwordResetLink, Uri contactUsLink, BackgroundJobScheduleOptions options);
        //void ScheduleAccountExpired(string title, string recipient, DateTimeOffset accountExpiryDate, Uri accountResetLink, Uri contactUsLink, BackgroundJobScheduleOptions options);
        //void ScheduleAccountExpiredNotification(string title, string recipient, DateTimeOffset accountExpiryDate, Uri accountResetLink, Uri contactUsLink, BackgroundJobScheduleOptions options);
        void ScheduleAddIndexForResourceDocument(long id, BackgroundJobScheduleOptions options);
        void ScheduleCheckFileForIndexResourceDocument(long id, BackgroundJobScheduleOptions options);
        void ScheduleSightingJob(long id, int jobType, BackgroundJobSceduleDelayed options);
        void ScheduleResourceJob(long id, int jobType,BackgroundJobSceduleDelayed options, int? skip, int? size, int? year);
        void ScheduleAddIndexForMapResourceDocument(long id, BackgroundJobScheduleOptions options); //CR7
        void ScheduleRecurrsiveIndexApplicationRecords();
        void ScheduleRecurrsiveIndexResourceDocumentRecord();
        void ScheduleRecurrsiveIndexMapResourceDocumentRecord();
        bool DeleteScheduledJob(string jobId);
        void ScheduleRecurrsiveGetMavenLayers();
        void ScheduleRecurrsiveGetResourceLayers();
        void ScheduleRecurrsiveCleanUserAuthExpiredTicket();
        void ScheduleRecurrsiveAuditLogMonthlyReport();
        void ScheduleUpdateMember_e_sign_link_expire(); //added for e-sign CR 2022
        void ScheduleSyncFilesApp1(); //GCC
        void ScheduleSyncFilesApp2(); //GCC

        void ScheduleSyncFilesForApp1ByFile(BackgroundJobSceduleDelayed options, params long[] storeId); //GCC
        void ScheduleSyncFilesForApp2ByFile(BackgroundJobSceduleDelayed options, params long[] storeId); //GCC

        
    }
}
