﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResourceFilePermission : Entity<long>, IDescribableEntity 
    {
        public bool IsAllowDownload  { get; set; }

        public bool IsViewReport { get; set; }

        //[JsonIgnore]
        //public virtual IList<ResourceDocument> ResourceDocuments { get; set; }

        [JsonIgnore]
        public ResourceDocument atResourceDocument { get; set; }

        [JsonIgnore]
        public virtual Group AtGroup { get; set; }


        [JsonIgnore]
        public virtual ApplicationUser AtUser { get; set; }

        public string Describe()
        {
            return "{ IsAllowDownload : \"" + IsAllowDownload + "\", IsViewReport : \"" + IsViewReport + "\", ResourceDocument : \"" + atResourceDocument?.Id
                + "\", Group : \"" + AtGroup?.Id + "\", User : \"" + AtUser?.Id + "}";
        }

        public ResourceFilePermission()
        {
            //ResourceDocuments = new List<ResourceDocument>();
        }
    }
}
