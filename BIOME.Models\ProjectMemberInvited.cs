﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class ProjectMemberInvited : Entity<long>,IDescribableEntity
    {
        public long ProjectId { get; set; }
        [ForeignKey("ProjectId")]
        public virtual ProjectDetail ProjectDetail { get; set; }
        public long MemberId { get; set; }
        public int InviteTimes { get; set; }
        [MaxLength(320)]
        public string Email { get; set; }
        public string Describe()
        {
            return "{ ProjectId : \"" + ProjectId + "\", MemberId : \"" + MemberId + "\", InviteTimes : \"" + InviteTimes + "\", Email : \"" + Email + "}";
        }
        [NotMapped]
        public ApplicationUser Member { get; set; }

        public ProjectMemberInvited()
        {

        }
    }
}
