namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class ResourceDocumentFilePermissionAddUserColumn : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.ResourceFilePermissions", "AtUser_Id", c => c.<PERSON>());
            CreateIndex("dbo.ResourceFilePermissions", "AtUser_Id");
            AddForeignKey("dbo.ResourceFilePermissions", "AtUser_Id", "dbo.AspNetUsers", "Id");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.ResourceFilePermissions", "AtUser_Id", "dbo.AspNetUsers");
            DropIndex("dbo.ResourceFilePermissions", new[] { "AtUser_Id" });
            DropColumn("dbo.ResourceFilePermissions", "AtUser_Id");
        }
    }
}
