﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchFieldSurveyTeamMemberDraft : Entity<long>,IDescribableEntity 
    {
        [MaxLength(66)]
        public string Name { get; set; }
        public string Identification { get; set; }
        public DateTime DOB { get; set; }
        public string ProfileImageName { get; set; }
        [MaxLength(320)]
        public string Email { get; set; } //CR3&CR4 Phase1
        public int AcknowledgeSignStatus { get; set; } //eSign CR 2022
        public DateTimeOffset? AcknowledgeSignDate { get; set; } //eSign CR 2022
        public DateTimeOffset? AcknowledgeSignLinkExpiredOn { get; set; } //eSign CR 2022
        public DateTimeOffset? AcknowledgeSignLinkSentOn { get; set; } //eSign CR 2022
        public string AcknowledgeSignLinkCode { get; set; } //eSign CR 2022
        public int AcknowledgeSignTriedCount { get; set; } //eSign CR 2022
        public long ResearchApplicationDraft_Id { get; set; }
        [ForeignKey("ResearchApplicationDraft_Id")]
        public virtual ResearchApplicationDraft ResearchApplicationDraft { get; set; }

        public string Describe()
        {
            return "{ Name : \"" + Name + "\", Identification : \"" + Identification + "\", DOB : \"" + DOB + "\", ProfileImageName : \"" + ProfileImageName + "\", ResearchApplicationDraft_Id : \"" + ResearchApplicationDraft_Id
                + "\", Email : \"" + Email
                + "\", AcknowledgementSignStatus : \"" + AcknowledgeSignStatus
                + "\", AcknowledgementSignDate : \"" + AcknowledgeSignDate
                + "\", AcknowledgeSignTriedCount : \"" + AcknowledgeSignTriedCount
                + "}";
        }
    }
}
