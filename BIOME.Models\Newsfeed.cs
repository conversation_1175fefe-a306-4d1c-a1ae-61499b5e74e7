﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class Newsfeed : Entity<long>,IDescribableEntity
    {
        public int ActionId { get; set; }
        public string CommentString { get; set; }
        public long AtSightingId { get; set; }
        [ForeignKey("AtSightingId")]
        public virtual SightingDetail AtSighting { get; set; }
        public long ByUserId { get; set; }
        [NotMapped]
        public ApplicationUser ByUser { get; set; }

        public string Describe()
        {
            return "{ ActionId : \"" + ActionId + "\", CommentString : \"" + CommentString + "\", AtSightingId : \"" + AtSightingId + "\", AtSighting : \"" + AtSighting
                  + "}";
        }
    }
}
