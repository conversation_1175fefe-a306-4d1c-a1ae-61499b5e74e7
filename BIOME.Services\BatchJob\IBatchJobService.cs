﻿using System.Threading.Tasks;

namespace BIOME.Services
{
    public interface IBatchJobService
    {
        string ElasticsearchServer { get; }

        //ServiceResult MigrateFirstLastName(int? take);
        ServiceResult SyncDataToMaven(int type, int fromday, int today, int month, int year, int skip, int? take);
        ServiceResult CleanUpAccountExpiry();

        //ServiceResult UpdateUserStatusFromACE();
        ServiceResult ExportUserToACE();
        ServiceResult SendReminderEmailForUnanswerDiscussion();
        ServiceResult ClearAllIndexFromElasticsearch();
        ServiceResult ESjob(int type,int? skip, int? size);
        ServiceResult IndexSightingRecords();
        ServiceResult IndexSightingRecords(long ID);
        ServiceResult RemoveSightingsfromES(long id);
        ServiceResult UpdateSightingsToES(long id);
        ServiceResult IndexApplicationRecords();
        ServiceResult IndexApplicationRecords(long ID);
        ServiceResult IndexResourceDocumentRecords();
        ServiceResult IndexResourceDocumentRecords(long ID);
        ServiceResult Remove_And_ReAdd_ResourceDocToES(long id);
        ServiceResult ReSyncResources_ES(int? skip, int? size, int? year);
        ServiceResult IndexMapResourceDocumentRecords();
        ServiceResult IndexMapResourceDocumentRecordsByID(long ID);
        ServiceResult GetMavenLayers();
        ServiceResult GetResourceLayers();

        ServiceResult SendRemiderEmailForSiteManagerPermitApplication(string profileUrl,string emailTemplateUrl,string applicaitonurl,string contactUsUrl);

        ServiceResult SendRemiderEmailForSiteManagerPermitApplicationPending(string profileUrl,string emailTemplateUrl, string applicationurl, string contactUsUrl);
        ServiceResult CleanUserAuthExpiredTicket();
        ServiceResult testUpdate();
        ServiceResult SendAuditLogMonthlyReport(bool isIncludeCurrentMonth);
        ServiceResult UpdateMember_e_sign_link_expire();
        ServiceResult SyncFilesApp1();
        ServiceResult SyncFilesApp2();
        //ServiceResult SyncFilesForApp1ByFileName(params string[] fileName);
        ServiceResult SyncFilesForApp1ByFile(params long[] storeId);
        ServiceResult SyncFilesForApp2ByFile(params long[] storeId);
    }
}