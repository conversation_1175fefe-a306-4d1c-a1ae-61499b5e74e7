﻿using AutoMapper;
using BIOME.Models;
using BIOME.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Utilities.Helpers;

namespace BIOME.Services
{
    public class GroupService : ServiceBase, IGroupService
    {
        #region Fields

        private readonly ApplicationDbContext dbContext;

        #endregion

        #region Constructors

        public GroupService(ApplicationDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        #endregion

        #region Public Methods

        public Group GetGroupById(long id)
        {
            return dbContext.Groups.Find(id);
        }

        public Group GetHighestGroupFromList(IEnumerable<Group> groups)
        {
            Group groupSelected = null;
            if (groups.Any(g => g is MainGroup))
            {
                groupSelected = groups.First(g => g is MainGroup);
            }
            else if (groups.Any(g => g is SubGroup))
            {
                groupSelected = groups.First(g => g is SubGroup);
            }
            else if (groups.Any(g => g is SubSubGroup))
            {
                groupSelected = groups.First(g => g is SubSubGroup);
            }

            return groupSelected;
        }

        public IEnumerable<UserGroupViewModel.SelectedUserGroupViewModel> GetGroupsHierarchy()
        {
            var listing = new List<UserGroupViewModel.SelectedUserGroupViewModel>();

            var mainGroups = GetAllMainGroups();

            var all = GetAllChildrenGroups(mainGroups);

            foreach (var group in all)
            {
                if (IsMainGroup(group))
                {
                    MainGroup mainGroup = (MainGroup)group;
                    listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(mainGroup));
                }
                if (IsSubGroup(group))
                {
                    SubGroup subGroup = (SubGroup)group;
                    listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subGroup));
                }
                if (IsSubSubGroup(group))
                {
                    SubSubGroup subSubGroup = (SubSubGroup)group;
                    listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subSubGroup));
                }
            }

            return listing;
        }

        public async Task<IEnumerable<UserGroupViewModel.SelectedUserGroupViewModel>> GetGroupsHierarchyForGroupAsync(long groupId)
        {
            var listing = new List<UserGroupViewModel.SelectedUserGroupViewModel>();

            var group = await dbContext.Groups.FindAsync(groupId);
            if (group == null)
            {
                return listing;
            }

            if (IsSubSubGroup(group))
            {
                SubSubGroup subSubGroup = (SubSubGroup)group;
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subSubGroup));
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subSubGroup.ParentGroup));
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subSubGroup.ParentGroup.ParentGroup));
            }
            if (IsSubGroup(group))
            {
                SubGroup subGroup = (SubGroup)group;
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subGroup));
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subGroup.ParentGroup));
            }
            if (IsMainGroup(group))
            {
                MainGroup mainGroup = (MainGroup)group;
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(mainGroup));
            }

            listing.Reverse();

            return listing;
        }

        public IEnumerable<UserGroupViewModel.SelectedUserGroupViewModel> GetGroupsHierarchyForGroup(Group group)
        {
            var listing = new List<UserGroupViewModel.SelectedUserGroupViewModel>();

            if (group == null)
            {
                return listing;
            }

            if (IsSubSubGroup(group))
            {
                SubSubGroup subSubGroup = (SubSubGroup)group;
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subSubGroup));
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subSubGroup.ParentGroup));
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subSubGroup.ParentGroup.ParentGroup));
            }
            if (IsSubGroup(group))
            {
                SubGroup subGroup = (SubGroup)group;
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subGroup));
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(subGroup.ParentGroup));
            }
            if (IsMainGroup(group))
            {
                MainGroup mainGroup = (MainGroup)group;
                listing.Add(Mapper.Map<UserGroupViewModel.SelectedUserGroupViewModel>(mainGroup));
            }

            listing.Reverse();

            return listing;
        }

        public IEnumerable<Group> GetAllChildrenGroups(IEnumerable<Group> groups)
        {
            var groupsWithChildren = new List<Group>();
            foreach (var group in groups)
            {
                groupsWithChildren.AddRange(GetAllChildrenGroups(group));
            }
            return groupsWithChildren;
        }

        public IEnumerable<Group> GetAllChildrenGroups(Group group)
        {
            var groups = new List<Group>()
            {
                group
            };
            if (group is MainGroup)
            {
                MainGroup mainGroup = (MainGroup)group;
                groups.AddRange(mainGroup.SubGroups.Flatten<Group>(g =>
                {
                    if (g is MainGroup)
                    {
                        return ((MainGroup)g).SubGroups;
                    }
                    else if (g is SubGroup)
                    {
                        return ((SubGroup)g).SubSubGroups;
                    }
                    return null;
                }));
            }
            else if (group is SubGroup)
            {
                SubGroup subGroup = (SubGroup)group;
                groups.AddRange(subGroup.SubSubGroups.Flatten<Group>(g =>
                {
                    if (g is MainGroup)
                    {
                        return ((MainGroup)g).SubGroups;
                    }
                    else if (g is SubGroup)
                    {
                        return ((SubGroup)g).SubSubGroups;
                    }
                    return null;
                }));
            }

            return groups;
        } 

        //public bool IsGroupBInGroupA(Group groupA, Group groupB)
        //{
        //    if (groupA.Equals(groupB))
        //    {
        //        return true;
        //    }
        //    Group grpToTest = groupB;
        //    do
        //    {
        //        Group parentGroup = grpToTest;
        //        if (grpToTest is SubSubGroup)
        //        {
        //            parentGroup = ((SubSubGroup)grpToTest).ParentGroup;
        //        }
        //        else if (grpToTest is SubGroup)
        //        {
        //            parentGroup = ((SubGroup)grpToTest).ParentGroup;
        //        }
        //        if (parentGroup.Equals(groupA))
        //        {
        //            return true;
        //        }
        //        grpToTest = parentGroup;
        //    } while (!(grpToTest is MainGroup));

        //    return false;
        //}

        public bool IsMainGroup(Group group)
        {
            return group is MainGroup;
        }

        public bool IsSubGroup(Group group)
        {
            return group is SubGroup;
        }

        public bool IsSubSubGroup(Group group)
        {
            return group is SubSubGroup;
        }

        public List<MainGroup> GetAllMainGroups()
        {
            var mainGroups = dbContext.Groups.Where(g => g is MainGroup).ToList();
            List<MainGroup> result = new List<MainGroup>();

            foreach (var item in mainGroups)
            {
                if (item is MainGroup)
                {
                    result.Add((MainGroup)item);
                }
            }
           
            return result;
        }

        public List<SubGroup> GetAllSubGroups()
        {
            var subGroups = dbContext.Groups.Where(g => g is SubGroup).ToList();
            List<SubGroup> result = new List<SubGroup>();

            foreach (var item in subGroups)
            {
                if (item is SubGroup)
                {
                    result.Add((SubGroup)item);
                }
            }

            return result;
        }

        public List<SubSubGroup> GetAllSubSubGroups()
        {
            var subGroups = dbContext.Groups.Where(g => g is SubSubGroup).ToList();
            List<SubSubGroup> result = new List<SubSubGroup>();

            foreach (var item in subGroups)
            {
                if (item is SubSubGroup)
                {
                    result.Add((SubSubGroup)item);
                }
            }

            return result;
        }

        public Group GetGroupByName(string groupName)
        {
            return dbContext.Groups.Where(g=>g.Name.Equals(groupName)).FirstOrDefault();
        }
        public MainGroup GetGroupByDomain(string domain)
        {
            var mainGroups = dbContext.Groups.Where(g => g is MainGroup).ToList();
            List<MainGroup> result = new List<MainGroup>();

            foreach (var item in mainGroups)
            {
                if (item is MainGroup)
                {
                    if (((MainGroup)item).Domain != null)

                        result.Add((MainGroup)item);
                }
            }

            if (result.Count > 0)
                return result.Where(g => g.Domain.Equals(domain,StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
            else
                return null;

        }

        public async Task<ServiceResult> AddParentGroupAsync(long userId, UserGroupViewModel.UserGroupMainCreateViewModel createVM)
        {
            var newMainGroup = Mapper.Map<MainGroup>(createVM);
            dbContext.Groups.Add(newMainGroup);
            int maxid = Convert.ToInt32(dbContext.Groups.Max(x => x.Id));
            var success = await dbContext.SaveChangesAsync(false, Convert.ToInt32(userId),maxid) > 0;
            if (!success)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> AddSubGroupAsync(long userId, UserGroupViewModel.UserGroupSubCreateViewModel createVM)
        {
            var mainGroup = (MainGroup)GetGroupById(createVM.ParentId);
            if (mainGroup == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

            var newSubGroup = Mapper.Map<SubGroup>(createVM);
            mainGroup.SubGroups.Add(newSubGroup);
            int maxid = Convert.ToInt32(dbContext.Groups.Max(x => x.Id));
            var success = await dbContext.SaveChangesAsync(false, Convert.ToInt32(userId),maxid) > 0;
            if (!success)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> AddSubSubGroupAsync(long userId, UserGroupViewModel.UserGroupSubSubCreateViewModel createVM)
        {
            var subGroup = (SubGroup)GetGroupById(createVM.ParentId);
            if (subGroup == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

            var newSubSubGroup = Mapper.Map<SubSubGroup>(createVM);
            subGroup.SubSubGroups.Add(newSubSubGroup);
            int maxid = Convert.ToInt32(dbContext.Groups.Max(x => x.Id));
            var success = await dbContext.SaveChangesAsync(false, Convert.ToInt32(userId),maxid) > 0;
            if (!success)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

            return ServiceResult.SuccessWithOutput(newSubSubGroup.Id);
        }

        public async Task<ServiceResult> EditParentGroupAsync(long userId, UserGroupViewModel.UserGroupMainEditViewModel editVM)
        {
            var mainGroup = (MainGroup)GetGroupById(editVM.Id);
            if (mainGroup == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }
            UserGroupViewModel.UserGroupMainEditViewModel log_obj = new UserGroupViewModel.UserGroupMainEditViewModel();

            log_obj.Id = mainGroup.Id;
            log_obj.GroupName = mainGroup.Name;
            log_obj.Domain = mainGroup.Domain;
            string log_json_before = log_obj.ToJson();


            log_obj = new UserGroupViewModel.UserGroupMainEditViewModel();
            log_obj.Id = editVM.Id;
            log_obj.GroupName = editVM.GroupName;
            log_obj.Domain = editVM.Domain;
            string log_json_after = log_obj.ToJson();

            mainGroup.Name = editVM.GroupName;
            mainGroup.Domain = editVM.Domain;

            var success = await dbContext.SaveChangesAsync(false, Convert.ToInt32(userId)) > 0;
            if (!success)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

            string log_json = string.Format(BIOME.Enumerations.Audit.AuditLogAction.BeforeAfterLogFormat, log_json_before, log_json_after);
            return ServiceResult.SuccessWithOutput(log_json);
        }

        public async Task<ServiceResult> EditSubGroupAsync(long userId, UserGroupViewModel.UserGroupSubEditViewModel editVM)
        {
            var subGroup = (SubGroup)GetGroupById(editVM.Id);
            if (subGroup == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }
            string beforeName = "Name: " +  subGroup.Name + "";
            subGroup.Name = editVM.Name;

            var success = await dbContext.SaveChangesAsync(false, Convert.ToInt32(userId)) > 0;
            if (!success)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

            string log_json = string.Format(BIOME.Enumerations.Audit.AuditLogAction.BeforeAfterLogFormat, beforeName, "Name: " + editVM.Name);
            return ServiceResult.SuccessWithOutput(log_json);
        }

        public async Task<ServiceResult> EditSubSubGroupAsync(long userId, UserGroupViewModel.UserGroupSubSubEditViewModel editVM)
        {
            var subSubGroup = (SubSubGroup)GetGroupById(editVM.Id);
            if (subSubGroup == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }
            string beforeName = "Name: " + subSubGroup.Name + "";
            subSubGroup.Name = editVM.Name;

            var success = await dbContext.SaveChangesAsync(false,Convert.ToInt32( userId)) > 0;
            if (!success)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }
            string log_json = string.Format(BIOME.Enumerations.Audit.AuditLogAction.BeforeAfterLogFormat, beforeName, "Name: " + editVM.Name);
            return ServiceResult.SuccessWithOutput(log_json);
        }

        #endregion

        #region Private Methods

        #endregion
    }

    public static class GroupServiceExtensions
    {
        public static ServiceResult AddSubSubGroup(this IGroupService groupService, long userId, UserGroupViewModel.UserGroupSubSubCreateViewModel createVM)
        {
            if (groupService == null)
            {
                throw new ArgumentNullException(nameof(groupService));
            }
            return AsyncHelper.RunSync(() => groupService.AddSubSubGroupAsync(userId,createVM));
        }

        public static ServiceResult EditSubGroup(this IGroupService groupService, long userId, UserGroupViewModel.UserGroupSubEditViewModel editVM)
        {
            if (groupService == null)
            {
                throw new ArgumentNullException(nameof(groupService));
            }
            return AsyncHelper.RunSync(() => groupService.EditSubGroupAsync(userId,editVM));
        }

        public static ServiceResult EditSubSubGroup(this IGroupService groupService, long userId, UserGroupViewModel.UserGroupSubSubEditViewModel editVM)
        {
            if (groupService == null)
            {
                throw new ArgumentNullException(nameof(groupService));
            }
            return AsyncHelper.RunSync(() => groupService.EditSubSubGroupAsync(userId,editVM));
        }
    }
}
