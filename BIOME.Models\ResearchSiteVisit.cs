﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchSiteVisit : Entity<long>, ISoftDelete,IDescribableEntity
    {
        public long AtResearchApplicationId { get; set; }
        [ForeignKey("AtResearchApplicationId")]
        [JsonIgnore]
        public virtual ResearchApplication AtResearchApplication { get; set; }

        public DateTimeOffset SiteVisitDateStart { get; set; }

        public DateTimeOffset SiteVisitDateEnd { get; set; }

        public long SiteId { get; set; }

        public int NoOfPeople { get; set; }

        public string ResearcherRemark { get; set; }

        public string SiteManagerRemark { get; set; }

        public bool IsApprove { get; set; }

        public bool IsPending { get; set; }

        public bool IsDeleted { get; set; }

        public string Describe()
        {
            return "{ AtResearchApplicationId : \"" + AtResearchApplicationId + "\", SiteVisitDateStart : \"" + SiteVisitDateStart + "\", SiteVisitDateEnd : \"" + SiteVisitDateEnd
                + "\", SiteId : \"" + SiteId + "\", NoOfPeople : \"" + NoOfPeople
                + "\", ResearcherRemark : \"" + ResearcherRemark + "\", SiteManagerRemark : \"" + SiteManagerRemark + "\", IsApprove : \"" + IsApprove
                + "\", IsPending : \"" + IsPending + "\", IsDeleted : \"" + IsDeleted + "}";
        }

        public static implicit operator List<object>(ResearchSiteVisit v)
        {
            throw new NotImplementedException();
        }
    }
}
