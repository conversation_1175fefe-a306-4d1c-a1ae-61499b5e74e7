// <auto-generated />
namespace BIOME.Services
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class StudyPeriodDatesInPermitApplication : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(StudyPeriodDatesInPermitApplication));
        
        string IMigrationMetadata.Id
        {
            get { return "202004140439205_StudyPeriodDatesInPermitApplication"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return Resources.GetString("Source"); }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
