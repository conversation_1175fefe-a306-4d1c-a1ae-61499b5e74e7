﻿using BIOME.Constants;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchInstitutional : Entity<long>, IDescribableEntity 
    {
        [Key, ForeignKey("Application")]
        public override long Id { get; set; }
        [Required]
        public string InstitutionName { get; set; }
        //[Required]
        public string InstitutionAddress { get; set; }

        [Required]
        [EmailAddress]
        [MaxLength(320)]
        public string PrincipalInvestigatorEmail { get; set; }
        [Required]
        [MaxLength(66)]
        public string PrincipalInvestigatorName { get; set; }
        //[Required]
        [RegularExpression(Configuration.ModelValidator.RegexTester.PhoneDigits)]
        public string PrincipalInvestigatorNumber { get; set; }

        [MaxLength(10)]
        public string InstitutionAddressBlockHouseNumber { get; set; }
        [MaxLength(32)]
        public string InstitutionAddressStreetName { get; set; }
        [MaxLength(3)]
        public string InstitutionAddressFloorNumber { get; set; }
        [MaxLength(5)]
        public string InstitutionAddressUnitNumber { get; set; }
        [MaxLength(65)]
        public string InstitutionAddressBuildingName { get; set; }
        [MaxLength(6)]
        public string InstitutionAddressPostalCode { get; set; }
        [MaxLength(4)]
        public string PIPhonePrefixCountryCode { get; set; }
        [MaxLength(3)]
        public string PIPhoneAreaCode { get; set; }
        [MaxLength(12)]
        public string PIPhoneNumber { get; set; }
        public string Describe()
        {
            return "{ InstitutionName : \"" + InstitutionName + "\", InstitutionAddress : \"" + InstitutionAddress + 
                "\", PrincipalInvestigatorEmail : \"" + PrincipalInvestigatorEmail + 
                "\", PrincipalInvestigatorName : \"" + PrincipalInvestigatorName + 
                "\", PrincipalInvestigatorNumber : \"" + PrincipalInvestigatorNumber +

                "\", InstitutionAddressBlockHouseNumber : \"" + InstitutionAddressBlockHouseNumber +
                "\", InstitutionAddressStreetName : \"" + InstitutionAddressStreetName +
                "\", InstitutionAddressFloorNumber : \"" + InstitutionAddressFloorNumber +
                "\", InstitutionAddressUnitNumber : \"" + InstitutionAddressUnitNumber +
                "\", InstitutionAddressBuildingName : \"" + InstitutionAddressBuildingName +
                "\", InstitutionAddressPostalCode : \"" + InstitutionAddressPostalCode +

                "\", PIPhonePrefixCountryCode : \"" + PIPhonePrefixCountryCode +
                "\", PIPhoneAreaCode : \"" + PIPhoneAreaCode +
                "\", PIPhoneNumber : \"" + PIPhoneNumber +
                "}";
        }

        [NotMapped]
        public string getFullAddress
        {
            get
            {
                StringBuilder builder = new StringBuilder();

                if (!string.IsNullOrEmpty(InstitutionAddressBlockHouseNumber))
                    builder.Append(InstitutionAddressBlockHouseNumber + " ");

                if (!string.IsNullOrEmpty(InstitutionAddressStreetName))
                    builder.Append(InstitutionAddressStreetName + " ");

                if (!string.IsNullOrEmpty(InstitutionAddressFloorNumber))
                    builder.Append("#" + InstitutionAddressFloorNumber);

                if (!string.IsNullOrEmpty(InstitutionAddressUnitNumber))
                    builder.Append("-" + InstitutionAddressUnitNumber + " ");

                if (!string.IsNullOrEmpty(InstitutionAddressBuildingName))
                    builder.Append(InstitutionAddressBuildingName + " ");

                if (!string.IsNullOrEmpty(InstitutionAddressPostalCode))
                    builder.Append("Singapore " + InstitutionAddressPostalCode + " ");

                return builder.ToString();
            }
        }

        [JsonIgnore]
        public virtual ResearchApplication Application { get; set; }
    }
}
