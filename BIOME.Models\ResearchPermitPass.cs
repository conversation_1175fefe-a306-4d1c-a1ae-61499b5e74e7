﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchPermitPass : Entity<long>,IDescribableEntity
    {
        public string PermitNumber { get; set; }
        public string PermitExpiryDate { get; set; }
        public string Name { get; set; }
        public string PassportNumber { get; set; }

        public string DOB { get; set;}
        public string Locality { get; set; }
        public string ProfileImageName { get; set; }
        public string PassFilename { get; set; }
        public string Describe()
        {
            return "{ PermitNumber : \"" + PermitNumber + "\", PermitExpiryDate : \"" + PermitExpiryDate + "\", Name : \"" + Name
                + "\", PassportNumber : \"" + PassportNumber + "\", DOB : \"" + DOB
                + "\", Locality : \"" + Locality + "\", ProfileImageName : \"" + ProfileImageName + "\", PassFilename : \"" + PassFilename  
                + "}";
        }

        [JsonIgnore]
        public virtual ICollection<ResearchPermitStatus> Status { get; set; }
        [JsonIgnore]
        public virtual ResearchPermitApplication AtResearchPermitApplication { get; set; }

        public ResearchPermitPass()
        {
            Status = new List<ResearchPermitStatus>();
        }
    }
}
