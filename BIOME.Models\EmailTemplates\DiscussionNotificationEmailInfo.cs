﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models.EmailTemplates
{
    public class DiscussionNotificationEmailInfo : EmailInfoBase
    {
        public string NewQuestionLink { get; set; }
        public string Question { get; set; }

        public DiscussionNotificationEmailInfo(string senderName, string senderEmail, string subject, List<string> recipients, string title) : base(senderName, senderEmail, subject, recipients, title)
        {

        }
    }
}
