﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
   public class SurveySpecies:Entity<long>
    {

        public long SurveyId { get; set; }
        [ForeignKey("SurveyId")]
        public virtual Survey Survey { get; set; }
        public long SpeciesId { get; set; }
        public bool IsRemoved { get; set; }
        [ForeignKey("SpeciesId")]
        public virtual SurveyMasterSpecies SurveyMasterSpecies { get; set; }


        public string Describe()
        {
            return "{ SurveyId : \"" + SurveyId + "\", SpeciesId : \"" + SpeciesId + "}";
        }
    }
}
