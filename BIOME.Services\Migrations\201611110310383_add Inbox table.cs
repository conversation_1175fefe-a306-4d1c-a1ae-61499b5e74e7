namespace BIOME.Services
{
    using System;
    using System.Collections.Generic;
    using System.Data.Entity.Infrastructure.Annotations;
    using System.Data.Entity.Migrations;
    
    public partial class addInboxtable : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.Inboxes",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Title = c.String(),
                        Sender = c.String(),
                        Description = c.String(),
                        IsDeleted = c<PERSON>(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    },
                annotations: new Dictionary<string, object>
                {
                    { "DynamicFilter_Inbox_IsDeleted", "EntityFramework.DynamicFilters.DynamicFilterDefinition" },
                })
                .PrimaryKey(t => t.Id);
            
            AddColumn("dbo.EmailTemplates", "IsInboxable", c => c<PERSON>(nullable: false));
        }
        
        public override void Down()
        {
            DropColumn("dbo.EmailTemplates", "IsInboxable");
            DropTable("dbo.Inboxes",
                removedAnnotations: new Dictionary<string, object>
                {
                    { "DynamicFilter_Inbox_IsDeleted", "EntityFramework.DynamicFilters.DynamicFilterDefinition" },
                });
        }
    }
}
