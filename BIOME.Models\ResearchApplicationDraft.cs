﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.Spatial;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchApplicationDraft : Entity<long>, IDescribableEntity 
    {
        public long? ResearchPermitApplicationId { get; set; }
        [Required]
        public virtual ApplicationUser Researcher { get; set; }
        public bool CollaboratingWithNParks { get; set; }

        [MaxLength(64)]
        public string NParksContactPersonEmailWithoutDomain { get; set; }
        public bool IsIndependentResearcher { get; set; }
        public string PhoneNumber { get; set; }

        public string Address { get; set; }

        public string InstitutionName { get; set; }
        public string InstitutionAddress { get; set; }

        [MaxLength(320)]
        public string PrincipalInvestigatorEmail { get; set; }
        [MaxLength(66)]
        public string PrincipalInvestigatorName { get; set; }
        public string PrincipalInvestigatorNumber { get; set; }

        public virtual ICollection<ResearchFieldSurveyTeamMemberDraft> FieldSurveyTeamMembers { get; set; }


        public string Title { get; set; }
        public string Purpose { get; set; }
        public string Methodology { get; set; }
        public string SpecimenSpeciesName { get; set; }
        public string SpecimenQuantity { get; set; }

        public long ResearchTypeId { get; set; }

        public virtual ICollection<long> StudySubjectIds
        {
            get
            {
                if (string.IsNullOrEmpty(StudySubjectIdsStr))
                {
                    return new List<long>();
                }
                return StudySubjectIdsStr.Split(new char[] { ',' }).Select(s => long.Parse(s)).ToList();
            }
            set
            {
                StudySubjectIdsStr = string.Join(",", value);
            }
        }
        public string StudySubjectIdsStr { get; private set; }

        public virtual long HabitatId { get; set; }
        public DateTimeOffset StudyPeriodStart { get; set; }
        public bool IsCommercial { get; set; }
        public DateTimeOffset StudyPeriodEnd { get; set; }

        public string StudyLocationsDrawn { get; set; }

        //public virtual ICollection<GISLocation> StudyLocations { get; set; }
        public string StudyLocationDescription { get; set; }
        public bool PresentFindings { get; set; }
        public DateTimeOffset? PresentFindingsDateStart { get; set; }
        public DateTimeOffset? PresentFindingsDateEnd { get; set; }

        public string IndemnityFormFileName { get; set; }
        public string MiscFileName { get; set; }
        public string RealMiscFileName { get; set; }
        public decimal CoralCollectionAmountInCubicMeter { get; set; } //CR3&4 Phase2

        [MaxLength(10)]
        public string InstitutionAddressBlockHouseNumber { get; set; }
        [MaxLength(32)]
        public string InstitutionAddressStreetName { get; set; }
        [MaxLength(3)]
        public string InstitutionAddressFloorNumber { get; set; }
        [MaxLength(5)]
        public string InstitutionAddressUnitNumber { get; set; }
        [MaxLength(65)]
        public string InstitutionAddressBuildingName { get; set; }
        [MaxLength(6)]
        public string InstitutionAddressPostalCode { get; set; }

        [MaxLength(10)]
        public string ApplicantAddressBlockHouseNumber { get; set; }
        [MaxLength(32)]
        public string ApplicantAddressStreetName { get; set; }
        [MaxLength(3)]
        public string ApplicantAddressFloorNumber { get; set; }
        [MaxLength(5)]
        public string ApplicantAddressUnitNumber { get; set; }
        [MaxLength(65)]
        public string ApplicantAddressBuildingName { get; set; }
        [MaxLength(6)]
        public string ApplicantAddressPostalCode { get; set; }
        [MaxLength(4)]
        public string PIPhonePrefixCountryCode { get; set; }
        [MaxLength(3)]
        public string PIPhoneAreaCode { get; set; }
        [MaxLength(12)]
        public string PIPhoneNumber { get; set; }
        [MaxLength(4)]
        public string ApplicantPhonePrefixCountryCode { get; set; }
        [MaxLength(3)]
        public string ApplicantPhoneAreaCode { get; set; }
        [MaxLength(12)]
        public string ApplicantPhoneNumber { get; set; }
        public string Describe()
        {
            return "{ ResearchPermitApplicationId : \"" + ResearchPermitApplicationId + "\", CollaboratingWithNParks : \"" + CollaboratingWithNParks + "\", NParksContactPersonEmailWithoutDomain : \"" + NParksContactPersonEmailWithoutDomain + "\", IsIndependentResearcher : \"" + IsIndependentResearcher
                + "\", PhoneNumber : \"" + PhoneNumber + "\", Address : \"" + Address + "\", InstitutionName : \"" + InstitutionName + "\", InstitutionAddress : \"" + InstitutionAddress
                + "\", PrincipalInvestigatorEmail : \"" + PrincipalInvestigatorEmail + "\", PrincipalInvestigatorName : \"" + PrincipalInvestigatorName + "\", PrincipalInvestigatorNumber : \"" + PrincipalInvestigatorNumber
                + "\", Title : \"" + Title + "\", Purpose : \"" + Purpose + "\", Methodology : \"" + Methodology + "\", SpecimenSpeciesName : \"" + SpecimenSpeciesName
                + "\", SpecimenQuantity : \"" + SpecimenQuantity + "\", ResearchTypeId : \"" + ResearchTypeId + "\", StudySubjectIdsStr : \"" + StudySubjectIdsStr
                + "\", HabitatId : \"" + HabitatId + "\", StudyPeriodStart : \"" + StudyPeriodStart + "\", IsCommercial : \"" + IsCommercial + "\", StudyPeriodEnd : \"" + StudyPeriodEnd
                + "\", StudyLocationsDrawn : \"" + StudyLocationsDrawn + "\", StudyLocationDescription : \"" + StudyLocationDescription + "\", PresentFindings : \"" + PresentFindings
                  + "\", PresentFindingsDateStart : \"" + PresentFindingsDateStart + "\", PresentFindingsDateEnd : \"" + PresentFindingsDateEnd + "\", IndemnityFormFileName : \"" + IndemnityFormFileName
                + "\", MiscFileName : \"" + MiscFileName + "\", RealMiscFileName : \"" + RealMiscFileName  
                + "\", InstitutionAddressBlockHouseNumber : \"" + InstitutionAddressBlockHouseNumber 
                + "\", InstitutionAddressStreetName : \"" + InstitutionAddressStreetName 
                + "\", InstitutionAddressFloorNumber : \"" + InstitutionAddressFloorNumber 
                + "\", InstitutionAddressUnitNumber : \"" + InstitutionAddressUnitNumber 
                + "\", InstitutionAddressBuildingName : \"" + InstitutionAddressBuildingName 
                + "\", InstitutionAddressPostalCode : \"" + InstitutionAddressPostalCode

                + "\", ApplicantAddressBlockHouseNumber : \"" + ApplicantAddressBlockHouseNumber
                + "\", ApplicantAddressStreetName : \"" + ApplicantAddressStreetName
                + "\", ApplicantAddressFloorNumber : \"" + ApplicantAddressFloorNumber
                + "\", ApplicantAddressUnitNumber : \"" + ApplicantAddressUnitNumber
                + "\", ApplicantAddressBuildingName : \"" + ApplicantAddressBuildingName
                + "\", ApplicantAddressPostalCode : \"" + ApplicantAddressPostalCode

                + "\", PIPhonePrefixCountryCode : \"" + PIPhonePrefixCountryCode
                + "\", PIPhoneAreaCode : \"" + PIPhoneAreaCode
                + "\", PIPhoneNumber : \"" + PIPhoneNumber

                + "\", ApplicantPhonePrefixCountryCode : \"" + ApplicantPhonePrefixCountryCode
                + "\", ApplicantPhoneAreaCode : \"" + ApplicantPhoneAreaCode
                + "\", ApplicantPhoneNumber : \"" + ApplicantPhoneNumber
                  + "}";
        }

        [NotMapped]
        public string RealMiscFileNameNotEmpty
        {
            get
            {
                if (!String.IsNullOrEmpty(RealMiscFileName))
                    return RealMiscFileName;
                else
                    return MiscFileName;
            }
        }

        public ResearchApplicationDraft()
        {
            FieldSurveyTeamMembers = new List<ResearchFieldSurveyTeamMemberDraft>();
            //StudyLocations = new List<GISLocation>();
            StudySubjectIds = new List<long>();
        }
    }
}
