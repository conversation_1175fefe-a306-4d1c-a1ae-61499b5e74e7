﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchPermitStatus : Entity<long>, IDescribableEntity 
    {
        public string StatusName { get; set; }

        public string Describe()
        {
            return "{ StatusName : \"" + StatusName + "}";
        }

        [JsonIgnore]
        public virtual ICollection<ResearchPermitApplication> PermitApplications { get; set; }
        [JsonIgnore]
        public virtual ICollection<ResearchPermitLetter> Letters { get; set; }
        [JsonIgnore]
        public virtual ICollection<ResearchPermitPass> Passes { get; set; }

        public ResearchPermitStatus()
        {

        }

        public ResearchPermitStatus(string statusName)
        {
            StatusName = statusName;
        }
    }
}
