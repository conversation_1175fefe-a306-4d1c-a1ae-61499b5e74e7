namespace BIOME.Services
{
    using System;
    using System.Collections.Generic;
    using System.Data.Entity.Infrastructure.Annotations;
    using System.Data.Entity.Migrations;
    
    public partial class softdeletetoresourcemeta : DbMigration
    {
        public override void Up()
        {
            AlterTableAnnotations(
                "dbo.ResourceMetaDatas",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Title = c.String(),
                        PreiodOfStudyFrom = c.DateTimeOffset(nullable: false, precision: 7),
                        PeriodOfStudyTo = c.DateTimeOffset(nullable: false, precision: 7),
                        Description = c.String(),
                        Organization = c.String(),
                        LocationType = c.String(),
                        UploaderId = c.Long(nullable: false),
                        IsDeleted = c.<PERSON>(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_ResourceMetaData_IsDeleted",
                        new AnnotationValues(oldValue: null, newValue: "EntityFramework.DynamicFilters.DynamicFilterDefinition")
                    },
                });
            
            AddColumn("dbo.ResourceMetaDatas", "IsDeleted", c => c.Boolean(nullable: false));
        }
        
        public override void Down()
        {
            DropColumn("dbo.ResourceMetaDatas", "IsDeleted");
            AlterTableAnnotations(
                "dbo.ResourceMetaDatas",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Title = c.String(),
                        PreiodOfStudyFrom = c.DateTimeOffset(nullable: false, precision: 7),
                        PeriodOfStudyTo = c.DateTimeOffset(nullable: false, precision: 7),
                        Description = c.String(),
                        Organization = c.String(),
                        LocationType = c.String(),
                        UploaderId = c.Long(nullable: false),
                        IsDeleted = c.Boolean(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_ResourceMetaData_IsDeleted",
                        new AnnotationValues(oldValue: "EntityFramework.DynamicFilters.DynamicFilterDefinition", newValue: null)
                    },
                });
            
        }
    }
}
