namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class ChangeProjectInvite : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.ProjectMemberInviteds", "MemberId", "dbo.AspNetUsers");
            DropIndex("dbo.ProjectMemberInviteds", new[] { "MemberId" });
            AddColumn("dbo.ProjectMemberInviteds", "Email", c => c.String());
        }
        
        public override void Down()
        {
            DropColumn("dbo.ProjectMemberInviteds", "Email");
            CreateIndex("dbo.ProjectMemberInviteds", "MemberId");
            AddForeignKey("dbo.ProjectMemberInviteds", "MemberId", "dbo.AspNetUsers", "Id", cascadeDelete: true);
        }
    }
}
