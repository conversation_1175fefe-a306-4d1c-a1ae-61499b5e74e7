﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace BIOME.Models
{
    public class SurveyMembers : Entity<long>,IDescribableEntity
    {
        public long SurveyId { get; set; }
        [ForeignKey("SurveyId")]
        public virtual Survey Survey { get; set; }
        public long MemberId { get; set; } //need index
        [ForeignKey("MemberId")]
        public virtual ApplicationUser Member { get; set; }
        public int MemberRank { get; set; }
        public string Describe()
        {
            return "{ SurveyId : \"" + SurveyId + "\", MemberId : \"" + MemberId + "\", MemberRank : \"" + MemberRank   + "}";
        }
        public SurveyMembers()
        {

        }
    }
}
