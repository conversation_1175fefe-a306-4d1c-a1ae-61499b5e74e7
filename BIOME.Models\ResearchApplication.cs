﻿using BIOME.Constants;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.Spatial;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static BIOME.Constants.Sites;

namespace BIOME.Models
{
    public class ResearchApplication : Entity<long>,IDescribableEntity 
    {
        public virtual ICollection<ResearchPermitApplication> PermitApplications { get; set; }
        public virtual ICollection<ResearchReport> Reports { get; set; }
        public virtual ICollection<ResearchDiscussionForum> Forums { get; set; }
        public virtual ICollection<ResearchInbox> Inboxes { get; set; }
        public virtual ApplicationUser Researcher { get; set; }

        [Required]
        public bool CollaboratingWithNParks { get; set; }

        [MaxLength(64)]
        public string NParksContactPersonEmailWithoutDomain { get; set; }
        [Required]
        public bool IsIndependentResearcher { get; set; }
        //[Required]
        [RegularExpression(Configuration.ModelValidator.RegexTester.PhoneDigits)]
        public string PhoneNumber { get; set; }
        public string Address { get; set; }

        public virtual ResearchInstitutional Institutional { get; set; }

        [Required]
        public virtual ICollection<ResearchFieldSurveyTeamMember> FieldSurveyTeamMembers { get; set; }

        [Required]
        public string Title { get; set; }
        [Required]
        public string Purpose { get; set; }
        [Required]
        public string Methodology { get; set; }
        public string SpecimenSpeciesName { get; set; }
        public string SpecimenQuantity { get; set; }

        [Required]
        public virtual ResearchType ResearchType { get; set; }

        [Required]
        public virtual ICollection<ConfSightingCategory> StudySubjects { get; set; }

        [Required]
        public virtual ConfHabitat Habitat { get; set; }
        [Required]
        public DateTimeOffset StudyPeriodStart { get; set; }
        public bool IsCommercial { get; set; }
        [Required]
        public DateTimeOffset StudyPeriodEnd { get; set; }

        public string StudyLocationsDrawn { get; set; }
        

        [Required]
        //public virtual ICollection<GISLocation> StudyLocations { get; set; }
        public virtual ICollection<ResearchStudyLocation> StudyLocationAccesses { get; set; }
        public string StudyLocationDescription { get; set; }
        [Required]
        public bool PresentFindings { get; set; }
        public DateTimeOffset? PresentFindingsDateStart { get; set; }
        public DateTimeOffset? PresentFindingsDateEnd { get; set; }

        public string IndemnityFormFileName { get; set; }
        public string MiscFileName { get; set; }
        public string RealMiscFileName { get; set; }

        [NotMapped]
        public string IndemnityFormFileToken { get; set; }
        [NotMapped]
        public string MiscFileToken { get; set; }
        public string PassCombinedFileName { get; set; }
        //CR3&CR4 Phase1
        public string AssignedMainApplicantHistJson { get; set; }

        public decimal CoralCollectionAmountInCubicMeter { get; set; } //CR3&4 Phase2
        [MaxLength(10)]
        public string ApplicantAddressBlockHouseNumber { get; set; }
        [MaxLength(32)]
        public string ApplicantAddressStreetName { get; set; }
        [MaxLength(3)]
        public string ApplicantAddressFloorNumber { get; set; }
        [MaxLength(5)]
        public string ApplicantAddressUnitNumber { get; set; }
        [MaxLength(65)]
        public string ApplicantAddressBuildingName { get; set; }
        [MaxLength(6)]
        public string ApplicantAddressPostalCode { get; set; }
        [MaxLength(4)]
        public string ApplicantPhonePrefixCountryCode { get; set; }
        [MaxLength(3)]
        public string ApplicantPhoneAreaCode { get; set; }
        [MaxLength(12)]
        public string ApplicantPhoneNumber { get; set; }
        public string Describe()
        {
            return "{ CollaboratingWithNParks : \"" + CollaboratingWithNParks + "\", NParksContactPersonEmailWithoutDomain : \"" + NParksContactPersonEmailWithoutDomain + "\", IsIndependentResearcher : \"" + IsIndependentResearcher
                + "\", PhoneNumber : \"" + PhoneNumber + "\", Address : \"" + Address 
                + "\", Title : \"" + Title + "\", Purpose : \"" + Purpose + "\", Methodology : \"" + Methodology + "\", SpecimenSpeciesName : \"" + SpecimenSpeciesName
                + "\", SpecimenQuantity : \"" + SpecimenQuantity + "\", ResearchTypeId : \"" + ResearchType?.Id  
                + "\", Habitat : \"" + Habitat?.Id  + "\", StudyPeriodStart : \"" + StudyPeriodStart + "\", IsCommercial : \"" + IsCommercial + "\", StudyPeriodEnd : \"" + StudyPeriodEnd
                + "\", StudyLocationsDrawn : \"" + StudyLocationsDrawn + "\", StudyLocationDescription : \"" + StudyLocationDescription + "\", PresentFindings : \"" + PresentFindings
                  + "\", PresentFindingsDateStart : \"" + PresentFindingsDateStart + "\", PresentFindingsDateEnd : \"" + PresentFindingsDateEnd + "\", IndemnityFormFileName : \"" + IndemnityFormFileName
                + "\", MiscFileName : \"" + MiscFileName + "\", RealMiscFileName : \"" + RealMiscFileName + "\", IndemnityFormFileToken : \"" + IndemnityFormFileToken + "\", MiscFileToken : \"" + MiscFileToken
                + "\", PassCombinedFileName : \"" + PassCombinedFileName + "\", CoralCollectionAmountInCubicMeter : \"" + CoralCollectionAmountInCubicMeter
                + "\", ApplicantAddressBlockHouseNumber : \"" + ApplicantAddressBlockHouseNumber
                + "\", ApplicantAddressStreetName : \"" + ApplicantAddressStreetName
                + "\", ApplicantAddressFloorNumber : \"" + ApplicantAddressFloorNumber
                + "\", ApplicantAddressUnitNumber : \"" + ApplicantAddressUnitNumber
                + "\", ApplicantAddressBuildingName : \"" + ApplicantAddressBuildingName
                + "\", ApplicantAddressPostalCode : \"" + ApplicantAddressPostalCode

                + "\", ApplicantPhonePrefixCountryCode : \"" + ApplicantPhonePrefixCountryCode
                + "\", ApplicantPhoneAreaCode : \"" + ApplicantPhoneAreaCode
                + "\", ApplicantPhoneNumber : \"" + ApplicantPhoneNumber
                  + "}";
        }


        [NotMapped]
        public string RealMiscFileNameNotEmpty
        {
            get
            {
                if (!String.IsNullOrEmpty(RealMiscFileName))
                    return RealMiscFileName;
                else
                    return MiscFileName;
            }
        }

        [NotMapped]
        public string getFullAddress
        {
            get
            {
                StringBuilder builder = new StringBuilder();

                if (!string.IsNullOrEmpty(ApplicantAddressBlockHouseNumber))
                    builder.Append(ApplicantAddressBlockHouseNumber + " ");

                if (!string.IsNullOrEmpty(ApplicantAddressStreetName))
                    builder.Append(ApplicantAddressStreetName + " ");

                if (!string.IsNullOrEmpty(ApplicantAddressFloorNumber))
                    builder.Append("#" + ApplicantAddressFloorNumber);

                if (!string.IsNullOrEmpty(ApplicantAddressUnitNumber))
                    builder.Append("-" + ApplicantAddressUnitNumber + " ");

                if (!string.IsNullOrEmpty(ApplicantAddressBuildingName))
                    builder.Append(ApplicantAddressBuildingName + " ");

                if (!string.IsNullOrEmpty(ApplicantAddressPostalCode))
                    builder.Append("Singapore " + ApplicantAddressPostalCode + " ");

                return builder.ToString();
            }
        }

        public ResearchApplication()
        {
            PermitApplications = new List<ResearchPermitApplication>();
            Reports = new List<ResearchReport>();
            Forums = new List<ResearchDiscussionForum>();
            FieldSurveyTeamMembers = new List<ResearchFieldSurveyTeamMember>();
            StudySubjects = new List<ConfSightingCategory>();
            //StudyLocations = new List<GISLocation>();
            StudyLocationAccesses = new List<ResearchStudyLocation>();
        }
    }
}
