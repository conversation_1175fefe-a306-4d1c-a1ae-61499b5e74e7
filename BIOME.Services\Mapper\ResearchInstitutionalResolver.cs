﻿using AutoMapper;
using BIOME.Models;
using BIOME.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public class ResearchInstitutionalResolver : ValueResolver<PermitViewModel.ApplicationFormViewModelBase, ResearchInstitutional>
    {
        protected override ResearchInstitutional ResolveCore(PermitViewModel.ApplicationFormViewModelBase source)
        {
            if (source.IsIndependentResearcher)
            {
                return null;
            }

            return new ResearchInstitutional()
            {
                InstitutionAddress = source.InstitutionAddress,
                InstitutionName = source.InstitutionName,
                PrincipalInvestigatorEmail = source.PrincipalInvestigatorEmail,
                PrincipalInvestigatorName = source.PrincipalInvestigatorName,
                PrincipalInvestigatorNumber = source.PrincipalInvestigatorNumber,
                InstitutionAddressBlockHouseNumber = source.InstitutionAddressBlockHouseNumber,
                InstitutionAddressBuildingName = source.InstitutionAddressBuildingName,
                InstitutionAddressFloorNumber = source.InstitutionAddressFloorNumber,
                InstitutionAddressPostalCode = source.InstitutionAddressPostalCode,
                InstitutionAddressStreetName = source.InstitutionAddressStreetName,
                InstitutionAddressUnitNumber = source.InstitutionAddressUnitNumber,
                PIPhoneAreaCode = source.PIPhoneAreaCode,
                PIPhoneNumber = source.PIPhoneNumber,
                PIPhonePrefixCountryCode= source.PIPhonePrefixCountryCode
            };
        }
    }
}
