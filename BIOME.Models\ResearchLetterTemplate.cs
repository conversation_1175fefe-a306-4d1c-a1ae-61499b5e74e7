﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchLetterTemplate : Entity<long>, ISoftDelete,IDescribableEntity 
    {
        public string Part1 { get; set; }
        public string Part2 { get; set; }
        public bool IsDeleted { get; set; }

        public string Describe()
        {
            return "{ Part1 : \"" + Part1 + "\", Part2 : \"" + Part2 + "\", IsDeleted : \"" + IsDeleted + "}";
        }
    }
}
