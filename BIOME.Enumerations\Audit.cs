﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Enumerations
{
    public class Audit
    {
        public enum Action
        {
            Login = 10,
            Logout = 20,
            AccessResourcesUnsuccessful = 30,
            AccessViolationsRequests = 40,
            SystemStartUpShutDown = 50,
            SystemBackupRecovery = 60,
            SecurityProfileChanges = 70,
            SystemMaintenance = 80,
            Transactions = 90,
            EditDocumentType=100,
            AddNewStructuredDataTemplate=110,
            EditStructuredDataTemplate=120,
            EditApplication =130,
            PostOnForum=140,
            EditPermit=150,
            EditPass = 160,
            IssuePermit = 170,
            //EditTypeOfResearch = 180, //No need. It is under Miscellaneous-> Edit Type of Research
            EditSiteAssignments = 190,
            EditPermitLetterTemplate=200,
            EditHeaderAndFooterTemplates =210,
            EditTermsAndConditionsTemplate=220,
            AddResourceDocument=230
        }

        public static class Module
        {
            public const string Home = "Home";
            public const string Homepage = "Homepage";
            public const string Sightings = "Sightings";
            public const string Projects = "Projects";
            public const string Surveys = "Surveys";
            public const string Map = "Map";
            public const string Research = "Research";
            public const string Resources = "Resources";
            public const string WhoWeAre = "Who We Are";
            public const string Help = "Help";
            public const string Search = "Search";
            public const string Miscellaneous = "Miscellaneous";
            public const string FileDownload = "File Download";
            public const string System = "System";
            public const string ResourceUpload = "Resource Upload";
            public const string Reports = "Reports";
            public const string Users = "User";
            public const string Guide = "Guide";
            public const string Badge = "Badge";
            public const string Config = "Config";
            public const string MavenSighting = "Sighting-Maven";
            public const string MavenResource = "Resource-Maven";
            public const string MavenResearch = "Research-Maven";
            //public const string ResearchPermit = "Research Permit Application"; removed cos same as "Research"
            public const string List = "List";
            public const string AuditTrail = "Audit Trail";
        }

        public static class AuditLogAction
        {
            public const string View_Home = "View Home";
            public const string View_MyProfile = "View My Profile";
            public const string View_ContactUs = "View Contact Us";
            public const string View_Sitemap = "View Sitemap";
            public const string View_Feedback = "View Feedback page";
            public const string Submit_Feedback = "Submit Feedback";
            public const string View_Highlight = "View Highlight";

            #region Sightings
            public const string View_AllSightings = "View All Sightings";
            public const string View_MySightings = "View My Sightings";
            public const string View_SightingDetail = "View Sighting Detail";
            public const string Download_SightingCSV = "Download CSV";
            public const string Download_SightingPhoto = "Download Photos";
            public const string Check_SightingNotificaion = "Check/Uncheck Do not send me notifications related to sightings";
            public const string Add_SightingVerification = "Verify Sighting";
            public const string Follow_Sighting = "Follow Sighting";
            public const string Flag_Inappropriate = "Flag as inappropriate";
            public const string Submit_Comment = "Submit Comment";
            public const string Delete_Comment = "Delete Comment";
            public const string Like_Sighting = "Like Sighting";
            public const string FBShare_Sighting = "FB share sighting";
            public const string Sighting_BulkUpload = "Submit Sighting - Bulk upload";
            public const string Sighting_IndividualUpload = "Submit Sighting - Individual Upload";

            public const string Download_Sightings_Admin = "Download Sightings - Admin Portal";
            public const string Download_Sightings_AdminError = "Download Sightings Error - Admin Portal";
            public const string Download_Threatened_Species_Admin = "Download Threatened Species - Admin Portal";
            public const string Download_Threatened_Species_AdminError = "Download Threatened Species Error - Admin Portal";
            //--------------API------------
            public const string API_GetSightings = "Get Sightings";
            public const string API_GetSightingDetail = "Get Sighting Detail";
            public const string API_GetSightingDetailError = "Get Sighting Detail Error";
            public const string API_SubmitSighting = "Submit Sighting";
            public const string API_SubmitSightingError = "Submit Sighting Error";
            public const string API_DeleteSighting = "Delete Sighting";
            public const string API_DeleteSightingError = "Delete Sighting Error";
            public const string API_ToggleLike = "Toggle Like";
            public const string API_ShareSighting = "Share Sighting";
            public const string API_SightingCount = "Sighting Count";
            //public const string API_SightingImage = "Sighting Image";
            //--------------Admin-----------------------
            public const string View_FeaturedSightings = "View Featured Sightings";
            public const string View_FlaggedSightings = "View Flagged Sightings";
            public const string View_InappropriateSightings = "View Inappropriate Sightings";
            public const string View_ThreatenedSpeciesSightings = "View Threatened Species Sightings";
            public const string View_BlacklistedSightings = "View Blacklisted Sightings";
            public const string View_SensitiveSightings = "View Sensitive Sightings";
            public const string View_ThreatenedSpecies = "View Threatened Species";
            public const string View_ManageMaskedSites = "View Manage Masked Sites";
            public const string View_SightingParameters = "View Sighting Parameters";
            public const string View_FieldGuides = "View Field Guides";
            public const string View_Projects = "View Projects";
            public const string View_Surveys = "View Surveys";

            #endregion

            #region Projects
            public const string View_MyProjects = "View My Projects";
            public const string View_ProjectListing = "View Project Listing";
            public const string Join_Projct = "Join Project";
            public const string Leave_Project = "Leave Project";
            public const string Request_Project = "Request Project";
            //-------Admin----------
            public const string Project_MarkAdmin = "Mark as admin";
            public const string Project_UnassignAdmin = "Unassign as admin";
            //--------------API------------
            public const string API_GetProjects = "Get Projects";
            //public const string API_GetProjectLogo = "Get Project Logo";
            //public const string API_GetProjectLogoError = "Get Project Logo Error";
            public const string API_JoinProject = "Join Project";
            public const string API_JoinProjectError = "Join Project Error";
            public const string API_LeaveProject = "Leave Project";
            public const string API_LeaveProjectError = "Leave Project Error";
            #endregion

            #region Surveys
            public const string View_MySurveys = "View my surveys";
            public const string View_PublicSurveys = "View public surveys";
            public const string Join_Survey = "Join Survey";
            public const string Leave_Survey = "Leave Survey";
            public const string Submit_Request_Survey = "Request Survey - Submit";
            public const string Save_Request_Survey = "Request Survey - Save as Draft";
            public const string Submit_Request_SurveyError = "Request Survey - Submit Error";
            public const string Save_Request_SurveyError = "Request Survey - Save as Draft Error";
            public const string Save_Survey_Draft = "Submit Survey - Save as Draft";
            public const string Submit_Survey = "Submit Survey";
            public const string Survey_Submission_Download = "Download Survey Submission";
            public const string Survey_Submission_Download_Error = "Download Survey Submission Error";

            public const string Submit_Request_Survey_Admin = "Request Survey - Submit (Admin)";
            public const string Save_Request_Survey_Admin = "Request Survey - Save as Draft (Admin)";
            public const string Submit_Request_SurveyError_Admin = "Request Survey - Submit Error (Admin)";
            public const string Save_Request_SurveyError_Admin = "Request Survey - Save as Draft Error (Admin)";

            public const string Copy_Request_Survey = "Request Survey - Copy";
            public const string Delete_Draft_Request_Survey = "Request Survey - Delete Draft";



            //-------Admin----------
            public const string Survey_UpdateConfig = "Update configuration";
            public const string Survey_MarkAdmin = "Mark as admin";
            public const string Survey_UnassignAdmin = "Unassign as admin";
            //--------------API------------
            public const string API_GetSurveyCategory = "Get Survey Categories";
            public const string API_GetSurveyList = "Get Survey List";
            public const string API_GetMySurvey = "Get My Surveys";
            public const string API_DeleteSurveySubmission = "Delete Survey Submission";
            public const string API_DeleteSurveySubmissionError = "Delete Survey Submission Error";
            public const string API_JoinSurvey = "Join/Leave Survey";
            public const string API_JoinSurveyError = "Join/Leave Survey Error";
            public const string API_GetSurveyDetail = "Get Survey Details";
            public const string API_GetSurveyDetailError = "Get Survey Details Error";
            public const string API_SubmitSurvey = "Submit Survey";
            public const string API_SubmitSurveyError = "Submit Survey Error";
            //public const string API_SurveyImage = "Survey Image";
            //public const string API_SurveyImageError = "Survey Image Error";
            #endregion

            #region Map
            public const string View_Map = "View map";
            public const string Search_Map = "Search map";
            public const string Take_Snapshot = "Take snapshot";
            #endregion

            #region Research
            public const string View_PermitAppStatus = "View application status";
            public const string View_PermitListing = "View Research Permit listings";
            public const string Search_PermitListing = "Search research permit listing";
            public const string View_PermitTermsAndCond = "View terms & condition";
            public const string View_PermitFAQ = "View FAQ/Help";
            public const string View_PermitContactInfo = "View Contact Information";
            public const string Submit_Permit = "Submit Research Permit";
            public const string Submit_PermitError = "Submit Research Permit Error";
            public const string Save_Permit = "Save as Draft Research Permit";
            public const string Save_PermitError = "Save as Draft Research Permit Error";
            public const string Submit_PermitRenewal = "Submit Research Permit Renewal";
            public const string Submit_PermitRenewalError = "Submit Research Permit Renewal Error";
            public const string Save_PermitRenewal = "Save as Draft Research Permit Renewal";
            public const string Save_PermitRenewalError = "Save as Draft Research Permit Renewal Error";
            public const string Submit_PermitAmend = "Submit Research Permit Amendment";
            public const string Submit_PermitAmendError = "Submit Research Permit Amendment Error";
            public const string Save_PermitAmend = "Save as Draft Research Permit Amendment";
            public const string Save_PermitAmendError = "Save as Draft Research Permit Amendment Error";
            public const string View_DiscussionForum = "View Discussion Forum";
            public const string Delete_DiscussionQuestion = "Delete Discussion Question";
            public const string Delete_DiscussionQuestionError = "Delete Discussion Question Error";
            public const string Post_Question = "Post Question";
            public const string Post_QuestionError = "Post Question Error";
            public const string Post_Answer = "Post Answer";
            public const string Post_AnswerError = "Post Answer Error";
            public const string PermitReport_DownloadFile = "Download Permit Report File";
            public const string PermitReport_DownloadFileError = "Download Permit Report File Error";
            public const string PermitApplicatoin_DownloadFile = "Download Permit Application File";
            public const string PermitApplication_DownloadFileError = "Download Permit Application File Error";
            public const string PermitApplicatoinMemberInfo_DownloadFile = "Download Permit Application members File";
            public const string PermitApplicatoinMemberInfo_DownloadFileError = "Download Permit Application members File Error";

            //e-sign CR 2022
            public const string Submit_Permit_pending_e_sign = "Submit Research Permit - Pending eSign";
            public const string Submit_Permit_pending_e_signError = "Submit Research Permit Error-Pending eSign";

            public const string Submit_PermitRenewal_e_sign = "Submit Research Permit Renewal - Pending eSign";
            public const string Submit_PermitRenewalError_e_sign = "Submit Research Permit Renewal Error - Pending eSign";
            public const string Save_PermitRenewal_e_sign = "Save as Draft Research Permit Renewal - Pending eSign";
            public const string Save_PermitRenewalError_e_sign = "Save as Draft Research Permit Renewal Error - Pending eSign";
            public const string Submit_PermitAmend_e_sign = "Submit Research Permit Amendment - Pending eSign";
            public const string Submit_PermitAmendError_e_sign = "Submit Research Permit Amendment Error - Pending eSign";

            public const string Research_Permit_Member_e_signed = "eSigned Research Permit Member";
            public const string Research_Permit_Member_e_signed_err = "eSigned Research Permit Member Error";


            //-------Admin----------
            public const string Subscribe_DiscussionForum = "Subscribe Discussion Forum";
            public const string Unsubscribe_DiscussionForum = "Unsubscribe Discussion Forum";
            public const string ViewResearch_ApplicationListing = "View Application Listing";
            public const string ViewResearch_TypeOfResearch = "View Type of Research";
            public const string ViewResearch_SiteAssignments = "View Site Assignments";
            public const string ViewResearch_PermitLetterTemplate = "View Permit Letter Template";
            public const string ViewResearch_HeaderFooterTemplate = "View Header and Footer Template";
            public const string ViewResearch_TermsCondTemplate = "View Terms and Conditions Template";
            #endregion

            #region Resources
            public const string View_ResourceListing = "View Resource Listing Page";
            public const string Search_ResourceListing = "Search Resource Listing";
            public const string View_ResourceMetaData = "View Meta Data of resource";
            public const string Resource_DownloadFile = "Download Resource File";
            public const string Resource_DownloadFileError = "Download Resource File Error";
            public const string Request_ResourceUpload = "Request for resource upload permission";
            //--------------Admin------------
            public const string UploadResource_Step1 = "Add Resource Step 1";
            public const string UploadResource_Step2 = "Add Resource Step 2";
            public const string UploadResource_Step2_Error = "Add Resource Step 2 Error";
            public const string UploadResource_Step3 = "Add Resource Step 3";
            public const string UploadResource_Step3_Error = "Add Resource Step 3 Error";
            public const string UploadResource_AddResource = "Add Resource Step 4";
            public const string UploadResource_AddResource_Error = "Add Resource Step 4 Error";
            public const string Delete_MetaData = "Delete Metadata";
            public const string Add_ResourceDocType = "Add Resource Document Type";
            public const string Add_ResourceDocType_Error = "Add Resource Document Type Error";
            public const string Edit_ResourceDocType = "Edit Resource Document Type";
            public const string View_UploadResource = "View Upload Resources";
            public const string View_MyResource = "View My Resources";
            public const string ViewResource_TypeOfDocument = "View Type Of Document";
            public const string View_StructuredDataTemplates = "View Structured Data Templates";
            #endregion

            #region Who We Are
            public const string View_Biome = "View biome";
            public const string View_Partners = "View partners";
            public const string View_Links = "View related links";
            #endregion

            #region Homepage
            //Intranet
            public const string View_Header = "View Header";
            public const string View_MainPageTitle = "View Main Page Title";
            public const string View_SGBioAtlasRow = "View SGBioAtlas Row";
            public const string View_BackgroundImage = "View Background Image";
            public const string View_HighlightsRow = "View Highlights Row";
            public const string View_IntranetHighlightsRow = "View Intranet Highlights Row";
            public const string View_Footer = "View Footer";
            //Intranet
            #endregion

            #region Help
            public const string View_FAQ = "View FAQ";
            public const string View_TermsAndCond = "View Terms and Conditions";
            #endregion

            #region Reports
            //--------------Admin------------
            public const string Download_Reports = "Download reports";
            #endregion

            #region Users
            //--------------Admin------------
            public const string Export_Users = "Export users";
            public const string View_UserList = "View List of Users";
            public const string View_ManageUserGroup = "View Manage User Groups";

            //--Website User

            public const string Website_Register = "Register BIOME";
            public const string Website_RegisterError = "Register Error BIOME";
            public const string Website_ChangePwd = "Change Password BIOME";
            public const string Website_ChangePwdError = "Change Password Error BIOME";
            public const string Website_ResetPwd = "Reset Password BIOME";
            public const string Website_ResetPwdError = "Reset Password Error BIOME";
            public const string Website_ResendActivation = "Resend Activation BIOME";
            public const string Website_ResendActivationError = "Resend Activation Error BIOME";
            public const string Website_ReActivateAccount = "Reactivate Account BIOME";
            public const string Website_ReActivateAccountError = "Reactivate Account Error BIOME";
            public const string Website_RegisterViaFB = "Register via FB BIOME";
            public const string Website_RegisterViaFBError = "Register via FB Error BIOME";
            public const string Website_RegisterViaADFS = "Register via ADFS";
            public const string Website_RegisterViaADFSError = "Register via ADFS Error";
            public const string Website_RegisterViaAD = "Register via AD";
            public const string Website_RegisterViaADError = "Register via AD Error";

            //Search
            public const string Search_Keyword = "Keyword Search";
            public const string Search_Advanced = "Advanced Search";

            //
            //--------------API------------
            public const string API_Register = "Register";
            public const string API_RegisterError = "Register Error";
            public const string API_Logout = "Logout";
            public const string API_LogoutError = "Logout Error";
            public const string API_ChangePwd = "Change Password";
            public const string API_ChangePwdError = "Change Password Error";
            public const string API_ResetPwd = "Reset Password";
            public const string API_ResetPwdError = "Reset Password Error";
            public const string API_RegisterViaFB = "Register via FB";
            public const string API_RegisterViaFBError = "Register via FB Error";
            public const string API_SendFeedback = "Send Feedback";
            public const string API_SendFeedbackError = "Send Feedback Error";
            public const string API_GetProfile = "Get Profile";
            public const string API_GetProfileError = "Get Profile Error";
            //public const string API_GetUserList = "Get User List"; //unused API
            //public const string API_GetUserListError = "Get User List Error";
            public const string API_GetToken = "Get Token";
            public const string API_GetTokenError = "Get Token Error";
            #endregion

            #region System
            //--------------API------------
            public const string API_GetMaintenanceNotice = "Get Maintenance Notice";
            public const string API_GetMaintenanceNotice_Error = "Get Maintenance Notice Error";

            public const string API_UpdateCheck = "Get Mobile new version check";
            public const string API_UpdateCheck_Error = "Get Mobile new version check Error";
            #endregion
            public const string RequestValidationError = "Request Validation Error";
            #region Guide
            //--------------API------------
            public const string API_GetGuideContent = "Get Content";
            public const string API_GetGuideReport = "Get Report";
            public const string API_GetGuideDetails = "Get Guide Details";
            #endregion

            #region Badge
            //--------------API------------
            public const string API_GetBadges = "Get Badges";
            public const string API_GetBadgeDetail = "Get Badge Detail";
            #endregion

            #region Config
            //--------------API------------
            public const string API_SightingCategories = "Get Sighting Categories";
            #endregion

            #region Maven
            //--------------API------------
            public const string MavenAPI_GetSighting = "Get Sighting";
            public const string MavenAPI_GetSightingError = "Get Sighting Error";
            public const string MavenAPI_GetDocument = "Get Document";
            public const string MavenAPI_GetDocumentError = "Get Document Error";
            public const string MavenAPI_GetMetaData = "Get Meta Data";
            public const string MavenAPI_GetMetaDataError = "Get Meta Data Error";
            public const string MavenAPI_GetResearchPermit = "Get Research Permit";
            public const string MavenAPI_GetResearchPermitError = "Get Research Permit Error";
            #endregion

            #region List
            //--------------Admin------------
            public const string View_Taxonomy = "View Taxonomy";
            public const string View_ProjectCategory = "View Project Category";
            public const string View_SurveyCategory = "View Survey Category";
            public const string View_Habitats = "View Habitats";
            public const string View_Environment = "View Environment";
            public const string View_Others = "View Others";
            #endregion

            #region Audit
            //--------------Admin------------
            public const string View_Login = "View Login";
            public const string View_Logout = "View Logout";
            public const string View_AccessResources = "View Access Resources";
            public const string View_Startup = "View Startup and Shutdown";
            public const string View_Transactions = "View Transactions";
            public const string View_ResourceDocument = "View Resource Document";
            public const string View_AuditDetails = "View Detail Audit Transactions";
            public const string View_AuditLogListing = "View Audit Log Listing";
            public const string MonthlyReport_Error = "Monthly Report Error";
            public const string MonthlyReport_Job = "Monthly Report Job";
            #endregion

            #region Miscellaneous
            //--------------Admin------------
            public const string View_EmailTemplates = "View Email Templates";
            public const string View_SystemParameters = "View System Parameters";
            public const string View_MaintenanceNotice = "View Maintenance Notice";
            public const string View_FeedbackListing = "View Feedback Listing";
            #endregion

            #region Report
            //--------------Admin------------
            public const string View_UsersReport = "View User Report";
            public const string View_SightingsReport = "View Sighting Report";
            public const string View_ProjectReport = "View Project Report";
            public const string View_ResourceReport = "View Resource Report";
            public const string View_PermitReport = "View Research Permit Report";
            public const string View_CoralCollectionDataReport = "View Coral Collection Data Report";
            #endregion


            #region ToDoList
            public const string View_ToDoList = "View Todo List";
            #endregion

            #region CAM API
            public const string CamAPI_GetUserInfo = "CamAPI GetUserinfo";
            public const string CamAPI_GetUserInfoError = "CamAPI GetUserinfo Error";
            public const string CamAPI_GetUserFindByCriteria = "CamAPI GetUserFindByCriteria";
            public const string CamAPI_GetUserFindByCriteriaError = "CamAPI GetUserFindByCriteria Error";
            public const string CamAPI_UpdateUser = "CamAPI UpdateUser";
            public const string CamAPI_UpdateUserError = "CamAPI UpdateUser Error";
            public const string CamAPI_RemoveUser = "CamAPI RemoveUser";
            public const string CamAPI_RemoveUserError = "CamAPI RemoveUser Error";
            public const string CamAPI_GetGroupInfo = "CamAPI GetGroupInfo";
            public const string CamAPI_GetGroupInfoError = "CamAPI GetGroupInfo Error";
            public const string CamAPI_GetGroupFindByCriteria = "CamAPI GetGroupFindByCriteria";
            public const string CamAPI_GetGroupFindByCriteriaError = "CamAPI GetGroupFindByCriteria Error";
            public const string CamAPI_UpdateGroup = "CamAPI UpdateGroup";
            public const string CamAPI_UpdateGroupError = "CamAPI UpdateGroup Error";

            #endregion

            public const string ErrorTitle = "Error: ";
            public const string BeforeAfterLogFormat = "Before: {0} After: {1}";
        }
    }
}

