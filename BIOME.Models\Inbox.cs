﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class Inbox : Entity<long>,ISoftDelete
    {
        public string ReceiverEmail { get; set; }
        public string Title { get; set; }
        public string SenderEmail { get; set; }
        public string Description { get; set; }
        public bool IsRead { get; set; }
        public bool IsDeleted { get; set; }

       
    }
}
