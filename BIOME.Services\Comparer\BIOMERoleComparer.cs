﻿using BIOME.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public class BIOMERoleComparer : IEqualityComparer<BIOMERole>
    {
        public bool Equals(BIOMERole item1, BIOMERole item2)
        {
            if (item1 == null && item2 == null)
                return true;
            else if ((item1 != null && item2 == null) ||
                    (item1 == null && item2 != null))
                return false;

            return item1.Id.Equals(item2.Id);
        }

        public int GetHashCode(BIOMERole obj)
        {
            return obj.Id.GetHashCode();
        }
    }
}
