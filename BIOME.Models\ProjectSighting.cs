﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class ProjectSighting : Entity<long>,IDescribableEntity
    {
        public long ProjectId { get; set; }
        [ForeignKey("ProjectId")]
        public virtual ProjectDetail Project { get; set; }
        public long SightingId { get; set; }
        [ForeignKey("SightingId")]
        public virtual SightingDetail Sighting { get; set; }
        public string Describe()
        {
            return "{ ProjectId : \"" + ProjectId + "\", SightingId : \"" + SightingId +   "}";
        }
        public ProjectSighting()
        {

        }
    }
}
