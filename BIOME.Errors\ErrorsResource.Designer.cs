﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace BIOME.Errors {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ErrorsResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ErrorsResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("BIOME.Errors.ErrorsResource", typeof(ErrorsResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error occured..
        /// </summary>
        public static string ErrorCode_1000 {
            get {
                return ResourceManager.GetString("ErrorCode_1000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid login. Please try again. If you are a new user, please register for an account..
        /// </summary>
        public static string ErrorCode_1001 {
            get {
                return ResourceManager.GetString("ErrorCode_1001", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password expired..
        /// </summary>
        public static string ErrorCode_1002 {
            get {
                return ResourceManager.GetString("ErrorCode_1002", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account has expired. Please check your email to reactivate your account..
        /// </summary>
        public static string ErrorCode_1003 {
            get {
                return ResourceManager.GetString("ErrorCode_1003", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email &apos;{0}&apos; is invalid..
        /// </summary>
        public static string ErrorCode_1004 {
            get {
                return ResourceManager.GetString("ErrorCode_1004", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email &apos;{0}&apos; is already taken..
        /// </summary>
        public static string ErrorCode_1005 {
            get {
                return ResourceManager.GetString("ErrorCode_1005", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User name {0} is already taken..
        /// </summary>
        public static string ErrorCode_1006 {
            get {
                return ResourceManager.GetString("ErrorCode_1006", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User name {0} is invalid, can only contain letters or digits..
        /// </summary>
        public static string ErrorCode_1007 {
            get {
                return ResourceManager.GetString("ErrorCode_1007", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email is required..
        /// </summary>
        public static string ErrorCode_1008 {
            get {
                return ResourceManager.GetString("ErrorCode_1008", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password is required..
        /// </summary>
        public static string ErrorCode_1009 {
            get {
                return ResourceManager.GetString("ErrorCode_1009", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Password must have at least 1 uppercase letter, 1 lowercase letter, 1 number and 1 special character as shown here &quot;@$!%*?&amp;&quot; and must have at least 12 characters.
        /// </summary>
        public static string ErrorCode_1010 {
            get {
                return ResourceManager.GetString("ErrorCode_1010", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect password..
        /// </summary>
        public static string ErrorCode_1011 {
            get {
                return ResourceManager.GetString("ErrorCode_1011", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords must have at least one digit (&apos;0&apos;-&apos;9&apos;)..
        /// </summary>
        public static string ErrorCode_1012 {
            get {
                return ResourceManager.GetString("ErrorCode_1012", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords must have at least one lowercase (&apos;a&apos;-&apos;z&apos;)..
        /// </summary>
        public static string ErrorCode_1013 {
            get {
                return ResourceManager.GetString("ErrorCode_1013", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords must have at least one non letter or digit character..
        /// </summary>
        public static string ErrorCode_1014 {
            get {
                return ResourceManager.GetString("ErrorCode_1014", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords must have at least one uppercase (&apos;A&apos;-&apos;Z&apos;)..
        /// </summary>
        public static string ErrorCode_1015 {
            get {
                return ResourceManager.GetString("ErrorCode_1015", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords must be at least 12 characters..
        /// </summary>
        public static string ErrorCode_1016 {
            get {
                return ResourceManager.GetString("ErrorCode_1016", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User already in role..
        /// </summary>
        public static string ErrorCode_1017 {
            get {
                return ResourceManager.GetString("ErrorCode_1017", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role {0} is already taken..
        /// </summary>
        public static string ErrorCode_1018 {
            get {
                return ResourceManager.GetString("ErrorCode_1018", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} cannot be null or empty..
        /// </summary>
        public static string ErrorCode_1019 {
            get {
                return ResourceManager.GetString("ErrorCode_1019", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name is required..
        /// </summary>
        public static string ErrorCode_1020 {
            get {
                return ResourceManager.GetString("ErrorCode_1020", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name is required..
        /// </summary>
        public static string ErrorCode_1021 {
            get {
                return ResourceManager.GetString("ErrorCode_1021", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} must be agreed before proceeding..
        /// </summary>
        public static string ErrorCode_1022 {
            get {
                return ResourceManager.GetString("ErrorCode_1022", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If your account registration is successful, you will receive an activation email with further instructions. If your account registration is not successful, your email address may have already been registered. Kindly proceed to login with your email and password..
        /// </summary>
        public static string ErrorCode_1023 {
            get {
                return ResourceManager.GetString("ErrorCode_1023", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account has been reactivated..
        /// </summary>
        public static string ErrorCode_1024 {
            get {
                return ResourceManager.GetString("ErrorCode_1024", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password has been used within the previous {0} times. Please choose another password that has not been used previously..
        /// </summary>
        public static string ErrorCode_1025 {
            get {
                return ResourceManager.GetString("ErrorCode_1025", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If your email address is registered in our system, you will receive an email with instructions on how to reset your password. Do check your spam/junk folder as well..
        /// </summary>
        public static string ErrorCode_1026 {
            get {
                return ResourceManager.GetString("ErrorCode_1026", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Address or SOE ID is required..
        /// </summary>
        public static string ErrorCode_1027 {
            get {
                return ResourceManager.GetString("ErrorCode_1027", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos; and &apos;{1}&apos; cannot be the same..
        /// </summary>
        public static string ErrorCode_1028 {
            get {
                return ResourceManager.GetString("ErrorCode_1028", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change password successfully. For security reasons, you are required to login again..
        /// </summary>
        public static string ErrorCode_1029 {
            get {
                return ResourceManager.GetString("ErrorCode_1029", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account already activated..
        /// </summary>
        public static string ErrorCode_1030 {
            get {
                return ResourceManager.GetString("ErrorCode_1030", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If your email address is registered in our system, you will receive an email with instructions on how to activate your account. Do check your spam/junk folder as well..
        /// </summary>
        public static string ErrorCode_1031 {
            get {
                return ResourceManager.GetString("ErrorCode_1031", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activation successful..
        /// </summary>
        public static string ErrorCode_1032 {
            get {
                return ResourceManager.GetString("ErrorCode_1032", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This link had expired..
        /// </summary>
        public static string ErrorCode_1033 {
            get {
                return ResourceManager.GetString("ErrorCode_1033", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password enter limit reached. Please reset your password..
        /// </summary>
        public static string ErrorCode_1034 {
            get {
                return ResourceManager.GetString("ErrorCode_1034", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Login OTP. Please try again..
        /// </summary>
        public static string ErrorCode_1035 {
            get {
                return ResourceManager.GetString("ErrorCode_1035", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email address and/or password is not correct..
        /// </summary>
        public static string ErrorCode_1036 {
            get {
                return ResourceManager.GetString("ErrorCode_1036", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot find user &apos;{0}&apos;..
        /// </summary>
        public static string ErrorCode_1101 {
            get {
                return ResourceManager.GetString("ErrorCode_1101", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid token..
        /// </summary>
        public static string ErrorCode_1102 {
            get {
                return ResourceManager.GetString("ErrorCode_1102", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to activate user &apos;{0}&apos;.
        /// </summary>
        public static string ErrorCode_1103 {
            get {
                return ResourceManager.GetString("ErrorCode_1103", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activate user failed, token expired.
        /// </summary>
        public static string ErrorCode_1104 {
            get {
                return ResourceManager.GetString("ErrorCode_1104", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to External logged in. Needs Create account..
        /// </summary>
        public static string ErrorCode_1106 {
            get {
                return ResourceManager.GetString("ErrorCode_1106", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to link facebook to existing account &apos;{0}&apos;..
        /// </summary>
        public static string ErrorCode_1107 {
            get {
                return ResourceManager.GetString("ErrorCode_1107", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account is not activated. Please request for a new email to activate your account here - https://biome.nparks.gov.sg/Account/ResendActivation.
        /// </summary>
        public static string ErrorCode_1108 {
            get {
                return ResourceManager.GetString("ErrorCode_1108", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, currently you do not have any roles assigned in BIOME. <NAME_EMAIL> for assistance..
        /// </summary>
        public static string ErrorCode_1109 {
            get {
                return ResourceManager.GetString("ErrorCode_1109", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot follow self..
        /// </summary>
        public static string ErrorCode_1201 {
            get {
                return ResourceManager.GetString("ErrorCode_1201", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to update accepted terms and condition. Please try again..
        /// </summary>
        public static string ErrorCode_3101 {
            get {
                return ResourceManager.GetString("ErrorCode_3101", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Updated successfully..
        /// </summary>
        public static string ErrorCode_9000 {
            get {
                return ResourceManager.GetString("ErrorCode_9000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No change made..
        /// </summary>
        public static string ErrorCode_9001 {
            get {
                return ResourceManager.GetString("ErrorCode_9001", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NParks Logo failed to update..
        /// </summary>
        public static string ErrorCode_9002 {
            get {
                return ResourceManager.GetString("ErrorCode_9002", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goverment Logo failed to update..
        /// </summary>
        public static string ErrorCode_9003 {
            get {
                return ResourceManager.GetString("ErrorCode_9003", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title Logo failed to update..
        /// </summary>
        public static string ErrorCode_9004 {
            get {
                return ResourceManager.GetString("ErrorCode_9004", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SGBioAtlas Row information failed to update..
        /// </summary>
        public static string ErrorCode_9005 {
            get {
                return ResourceManager.GetString("ErrorCode_9005", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Footer failed to update..
        /// </summary>
        public static string ErrorCode_9006 {
            get {
                return ResourceManager.GetString("ErrorCode_9006", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Background Image failed to update..
        /// </summary>
        public static string ErrorCode_9007 {
            get {
                return ResourceManager.GetString("ErrorCode_9007", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fail to add new highlight..
        /// </summary>
        public static string ErrorCode_9008 {
            get {
                return ResourceManager.GetString("ErrorCode_9008", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Highlight failed to update..
        /// </summary>
        public static string ErrorCode_9009 {
            get {
                return ResourceManager.GetString("ErrorCode_9009", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Highlight not found..
        /// </summary>
        public static string ErrorCode_9010 {
            get {
                return ResourceManager.GetString("ErrorCode_9010", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to update system parameters..
        /// </summary>
        public static string ErrorCode_9011 {
            get {
                return ResourceManager.GetString("ErrorCode_9011", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Url format invalid. (URL should starts with http:// or https://).
        /// </summary>
        public static string ErrorCode_9100 {
            get {
                return ResourceManager.GetString("ErrorCode_9100", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone format invalid..
        /// </summary>
        public static string ErrorCode_9101 {
            get {
                return ResourceManager.GetString("ErrorCode_9101", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} and {1} does not match..
        /// </summary>
        public static string ErrorCode_9102 {
            get {
                return ResourceManager.GetString("ErrorCode_9102", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The file size for {0} should not exceed {1}..
        /// </summary>
        public static string ErrorCode_9103 {
            get {
                return ResourceManager.GetString("ErrorCode_9103", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum {2} characters..
        /// </summary>
        public static string ErrorCode_9104 {
            get {
                return ResourceManager.GetString("ErrorCode_9104", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please choose any of the status..
        /// </summary>
        public static string ErrorCode_9105 {
            get {
                return ResourceManager.GetString("ErrorCode_9105", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recipient is required..
        /// </summary>
        public static string ErrorCode_9106 {
            get {
                return ResourceManager.GetString("ErrorCode_9106", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name can only contain (lowercase character, uppercase character, comma, hyphen, dot and single quote).
        /// </summary>
        public static string ErrorCode_9107 {
            get {
                return ResourceManager.GetString("ErrorCode_9107", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name can not exceed {1} characters.
        /// </summary>
        public static string ErrorCode_9108 {
            get {
                return ResourceManager.GetString("ErrorCode_9108", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please tick the reCAPTCHA (I&apos;m not a robot) before proceeding.
        /// </summary>
        public static string ErrorCode_9109 {
            get {
                return ResourceManager.GetString("ErrorCode_9109", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to connect to reCAPTCHA verification server at the moment. Please try again in 5 minutes. If the problem persists, you may wish to contact &lt;a href=&apos;https://biome.nparks.gov.sg/Contact/&apos;&gt;technical support&lt;/a&gt; for further assistance.
        /// </summary>
        public static string ErrorCode_9200 {
            get {
                return ResourceManager.GetString("ErrorCode_9200", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to communicate to server at the moment. Please try again..
        /// </summary>
        public static string ErrorCode_9201 {
            get {
                return ResourceManager.GetString("ErrorCode_9201", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email address and/or Date of Birth is incorrect..
        /// </summary>
        public static string ErrorCode_9202 {
            get {
                return ResourceManager.GetString("ErrorCode_9202", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your access has been locked after 6 tries. Please contact Permit Applicant to resend e-Acknowlegement form..
        /// </summary>
        public static string ErrorCode_9203 {
            get {
                return ResourceManager.GetString("ErrorCode_9203", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have successfully signed e-Acknowledgement form..
        /// </summary>
        public static string ErrorCode_9204 {
            get {
                return ResourceManager.GetString("ErrorCode_9204", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The link is not available. You may contact research permit applicant or &lt;contact_us&gt; for further assistance..
        /// </summary>
        public static string ErrorCode_9205 {
            get {
                return ResourceManager.GetString("ErrorCode_9205", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email can not exceed {1} characters.
        /// </summary>
        public static string ErrorCode_9206 {
            get {
                return ResourceManager.GetString("ErrorCode_9206", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name is required..
        /// </summary>
        public static string ErrorCode_9207 {
            get {
                return ResourceManager.GetString("ErrorCode_9207", resourceCulture);
            }
        }
    }
}
