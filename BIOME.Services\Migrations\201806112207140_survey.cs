namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class survey : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.Surveys",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Title = c.String(),
                        Description = c.String(),
                        ViewPermission = c.Int(),
                        DownloadPermission = c.Int(),
                        IsAutoApproveM = c<PERSON>(nullable: false),
                        IsSearchable = c<PERSON>(nullable: false),
                        IsActive = c<PERSON>(nullable: false),
                        SurveyStatus = c.Int(),
                        ApprovedById = c.Long(),
                        Duration = c.Int(),
                        StartDate = c.DateTime(),
                        EndDate = c.DateTime(),
                        OwnerId = c.Long(nullable: false),
                        SurveyCategoryId = c.Long(),
                        LogoName = c.String(),
                        LogoPath = c.String(),
                        MemberCount = c.Int(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveyLocations",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Remarks = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        Location_Id = c.Long(),
                        Survey_Id = c.Long(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.GISLocations", t => t.Location_Id)
                .ForeignKey("dbo.Surveys", t => t.Survey_Id)
                .Index(t => t.Location_Id)
                .Index(t => t.Survey_Id);
            
            CreateTable(
                "dbo.SurveyMembers",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        SurveyId = c.Long(nullable: false),
                        MemberId = c.Long(nullable: false),
                        MemberRank = c.Int(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.AspNetUsers", t => t.MemberId, cascadeDelete: true)
                .ForeignKey("dbo.Surveys", t => t.SurveyId, cascadeDelete: true)
                .Index(t => t.SurveyId)
                .Index(t => t.MemberId);
            
            CreateTable(
                "dbo.SurveyQuestions",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Title = c.String(),
                        IsBefore = c.Boolean(nullable: false),
                        QuestionTypeID = c.Long(nullable: false),
                        IsCompulsory = c.Boolean(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        Survey_Id = c.Long(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Surveys", t => t.Survey_Id)
                .Index(t => t.Survey_Id);
            
            CreateTable(
                "dbo.SurveyQuestionDetails",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Description = c.String(),
                        isAnswer = c.Boolean(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        SurveyQuestion_Id = c.Long(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.SurveyQuestions", t => t.SurveyQuestion_Id)
                .Index(t => t.SurveyQuestion_Id);
            
            CreateTable(
                "dbo.SurveySpecies",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        SurveyId = c.Long(nullable: false),
                        SpeciesId = c.Long(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Surveys", t => t.SurveyId, cascadeDelete: true)
                .ForeignKey("dbo.SurveyMasterSpecies", t => t.SpeciesId, cascadeDelete: true)
                .Index(t => t.SurveyId)
                .Index(t => t.SpeciesId);
            
            CreateTable(
                "dbo.SurveyMasterSpecies",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        FamilySuborder = c.String(),
                        Category = c.String(),
                        CategoryName = c.String(),
                        Grouping = c.String(),
                        Species = c.String(),
                        CommonName = c.String(),
                        Description = c.String(),
                        Photo1 = c.String(),
                        Photo2 = c.String(),
                        Photo3 = c.String(),
                        Photo4 = c.String(),
                        Attributes = c.String(),
                        Others = c.String(),
                        ImagePath = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveySubmissions",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        UserId = c.Long(nullable: false),
                        SurveyId = c.Long(nullable: false),
                        SubmissionDate = c.DateTime(nullable: false),
                        HasAdditionalSpecies = c.Boolean(nullable: false),
                        SubmissionStatus = c.Int(nullable: false),
                        SurveyLocationID = c.Long(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Surveys", t => t.SurveyId, cascadeDelete: true)
                .ForeignKey("dbo.SurveyLocations", t => t.SurveyLocationID, cascadeDelete: true)
                .ForeignKey("dbo.AspNetUsers", t => t.UserId, cascadeDelete: true)
                .Index(t => t.UserId)
                .Index(t => t.SurveyId)
                .Index(t => t.SurveyLocationID);
            
            CreateTable(
                "dbo.SurveyQuestionsAnswers",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        SurveySubmissionId = c.Long(nullable: false),
                        QuestionId = c.Long(nullable: false),
                        QuestionDetailId = c.Long(),
                        AnswerText = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.SurveyQuestionDetails", t => t.QuestionDetailId)
                .ForeignKey("dbo.SurveyQuestions", t => t.QuestionId, cascadeDelete: true)
                .ForeignKey("dbo.SurveySubmissions", t => t.SurveySubmissionId, cascadeDelete: true)
                .Index(t => t.SurveySubmissionId)
                .Index(t => t.QuestionId)
                .Index(t => t.QuestionDetailId);
            
            CreateTable(
                "dbo.SurveySpeciesAnswers",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        SurveySubmissionId = c.Long(),
                        SurveySpeciesId = c.Long(),
                        Quantity = c.Int(nullable: false),
                        Species = c.String(),
                        CommonName = c.String(),
                        Description = c.String(),
                        Photo1 = c.String(),
                        ImagePath = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.SurveySpecies", t => t.SurveySpeciesId)
                .ForeignKey("dbo.SurveySubmissions", t => t.SurveySubmissionId)
                .Index(t => t.SurveySubmissionId)
                .Index(t => t.SurveySpeciesId);
            
            CreateTable(
                "dbo.SurveyCategories",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Name = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveyQuestionTypes",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Name = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.SurveySubmissions", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.SurveySpeciesAnswers", "SurveySubmissionId", "dbo.SurveySubmissions");
            DropForeignKey("dbo.SurveySpeciesAnswers", "SurveySpeciesId", "dbo.SurveySpecies");
            DropForeignKey("dbo.SurveyQuestionsAnswers", "SurveySubmissionId", "dbo.SurveySubmissions");
            DropForeignKey("dbo.SurveyQuestionsAnswers", "QuestionId", "dbo.SurveyQuestions");
            DropForeignKey("dbo.SurveyQuestionsAnswers", "QuestionDetailId", "dbo.SurveyQuestionDetails");
            DropForeignKey("dbo.SurveySubmissions", "SurveyLocationID", "dbo.SurveyLocations");
            DropForeignKey("dbo.SurveySubmissions", "SurveyId", "dbo.Surveys");
            DropForeignKey("dbo.SurveySpecies", "SpeciesId", "dbo.SurveyMasterSpecies");
            DropForeignKey("dbo.SurveySpecies", "SurveyId", "dbo.Surveys");
            DropForeignKey("dbo.SurveyQuestionDetails", "SurveyQuestion_Id", "dbo.SurveyQuestions");
            DropForeignKey("dbo.SurveyQuestions", "Survey_Id", "dbo.Surveys");
            DropForeignKey("dbo.SurveyMembers", "SurveyId", "dbo.Surveys");
            DropForeignKey("dbo.SurveyMembers", "MemberId", "dbo.AspNetUsers");
            DropForeignKey("dbo.SurveyLocations", "Survey_Id", "dbo.Surveys");
            DropForeignKey("dbo.SurveyLocations", "Location_Id", "dbo.GISLocations");
            DropIndex("dbo.SurveySpeciesAnswers", new[] { "SurveySpeciesId" });
            DropIndex("dbo.SurveySpeciesAnswers", new[] { "SurveySubmissionId" });
            DropIndex("dbo.SurveyQuestionsAnswers", new[] { "QuestionDetailId" });
            DropIndex("dbo.SurveyQuestionsAnswers", new[] { "QuestionId" });
            DropIndex("dbo.SurveyQuestionsAnswers", new[] { "SurveySubmissionId" });
            DropIndex("dbo.SurveySubmissions", new[] { "SurveyLocationID" });
            DropIndex("dbo.SurveySubmissions", new[] { "SurveyId" });
            DropIndex("dbo.SurveySubmissions", new[] { "UserId" });
            DropIndex("dbo.SurveySpecies", new[] { "SpeciesId" });
            DropIndex("dbo.SurveySpecies", new[] { "SurveyId" });
            DropIndex("dbo.SurveyQuestionDetails", new[] { "SurveyQuestion_Id" });
            DropIndex("dbo.SurveyQuestions", new[] { "Survey_Id" });
            DropIndex("dbo.SurveyMembers", new[] { "MemberId" });
            DropIndex("dbo.SurveyMembers", new[] { "SurveyId" });
            DropIndex("dbo.SurveyLocations", new[] { "Survey_Id" });
            DropIndex("dbo.SurveyLocations", new[] { "Location_Id" });
            DropTable("dbo.SurveyQuestionTypes");
            DropTable("dbo.SurveyCategories");
            DropTable("dbo.SurveySpeciesAnswers");
            DropTable("dbo.SurveyQuestionsAnswers");
            DropTable("dbo.SurveySubmissions");
            DropTable("dbo.SurveyMasterSpecies");
            DropTable("dbo.SurveySpecies");
            DropTable("dbo.SurveyQuestionDetails");
            DropTable("dbo.SurveyQuestions");
            DropTable("dbo.SurveyMembers");
            DropTable("dbo.SurveyLocations");
            DropTable("dbo.Surveys");
        }
    }
}
