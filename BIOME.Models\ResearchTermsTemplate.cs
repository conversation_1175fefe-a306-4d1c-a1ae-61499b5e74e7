﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchTermsTemplate : Entity<long>, ISoftDelete,IDescribableEntity
    {
        public string TermsFileName { get; set; }
        public bool IsDeleted { get; set; }
        public string Describe()
        {
            return "{ TermsFileName : \"" + TermsFileName + "\", IsDeleted : \"" + IsDeleted   + "}";
        }
    }
}
