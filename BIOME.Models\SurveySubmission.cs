﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations.Schema;
namespace BIOME.Models
{
  public  class SurveySubmission:Entity<long>,IDescribableEntity
    {
        public long UserId { get; set; } //need index
        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; }

        public long SurveyId { get; set; } //need index
        [ForeignKey("SurveyId")]
        public virtual Survey Survey { get; set; }
        public DateTime SubmissionDate { get; set; }
        public bool HasAdditionalSpecies { get; set; }
        public int SubmissionStatus { get; set; }

        public virtual List<SurveySpeciesAnswer> SurveySpeciesAnswer { get; set; }
        public long SurveyLocationID { get; set; } //need index
        [ForeignKey("SurveyLocationID")]
        public virtual SurveyLocation SurveyLocation { get; set; }
        public virtual List<SurveyQuestionsAnswer> SurveyQuestionsAnswer { get; set; }


        public string Describe()
        {
            return "{ SurveyId : \"" + SurveyId + "\", SubmissionDate : \"" + SubmissionDate + "\", UserId : \"" + UserId + "\", HasAdditionalSpecies : \"" + HasAdditionalSpecies
                + "\", SubmissionStatus : \"" + SubmissionStatus + "\", SurveyLocationID : \"" + SurveyLocationID   + "}";
        }

        public SurveySubmission()
        {
            SurveySpeciesAnswer = new List<Models.SurveySpeciesAnswer>();
            SurveyQuestionsAnswer = new List<SurveyQuestionsAnswer>();
             
        }

    }
}
