﻿using AutoMapper;
using BIOME.Constants;
using BIOME.Models;
using BIOME.ViewModels;
using BIOME.ViewModels.API.v1_0;
using Flurl;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Data.Entity.Spatial;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;   
using Utilities.Helpers;
using System.Globalization;
using BIOME.Services.User;
using BIOME.ViewModels.CamAPI.Common;
using static BIOME.Enumerations.User;
using CsQuery.ExtensionMethods.Internal;

namespace BIOME.Services
{
    public class MapperProfile
    {
        public class ViewModelProfile : Profile
        {
            protected override void Configure()
            {
                CreateMap<PageHeader, PageViewModel.HeaderViewModel>()
                    .ForMember(h => h.TitleLogoPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Page.Header, h.TitleLogoName)))
                    .ForMember(h => h.NParkLogoPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Page.Header, h.NParkLogoName)))
                    .ForMember(h => h.GovLogoPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Page.Header, h.GovLogoName)))
                    .ForMember(h => h.BackgroundImgPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Page.Header, h.BackgroundImgName)));

                CreateMap<PageHeader, PageViewModel.HeaderCMSViewModel>()
                    .ForMember(h => h.TitleLogoPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Page.Header, h.TitleLogoName)))
                    .ForMember(h => h.NParkLogoPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Page.Header, h.NParkLogoName)));

                CreateMap<PageFooter, PageViewModel.FooterViewModel>()
                    //fixes for NPARK/BIOME/NCODE/2020_0162
                    .ForMember(i => i.PrivacyLink, opt => opt.MapFrom(i => string.IsNullOrEmpty(i.PrivacyLink) ? null : new Uri(i.PrivacyLink)))
                    .ForMember(i => i.TermsLink, opt => opt.MapFrom(i => string.IsNullOrEmpty(i.TermsLink) ? null : new Uri(i.TermsLink)))
                    .ForMember(i => i.TwitterLink, opt => opt.MapFrom(i => string.IsNullOrEmpty(i.TwitterLink) ? null : new Uri(i.TwitterLink)))
                    .ForMember(i => i.InstagramLink, opt => opt.MapFrom(i => string.IsNullOrEmpty(i.InstagramLink) ? null : new Uri(i.InstagramLink)))
                    .ForMember(i => i.FacebookLink, opt => opt.MapFrom(i => string.IsNullOrEmpty(i.FacebookLink) ? null : new Uri(i.FacebookLink)));

                CreateMap<PageFooter, PageViewModel.FooterCMSViewModel>()
                    //fixes for NPARK/BIOME/NCODE/2020_0162
                    .ForMember(i => i.PrivacyLink, opt => opt.MapFrom(i => string.IsNullOrEmpty(i.PrivacyLink) ? null : new Uri(i.PrivacyLink)))
                    .ForMember(i => i.TermsLink, opt => opt.MapFrom(i => string.IsNullOrEmpty(i.TermsLink) ? null : new Uri(i.TermsLink)));

                CreateMap<HomepageContact, PageViewModel.HomePageContactViewModel>();
                CreateMap<HomepageAppInfo, PageViewModel.AppViewModel>()
                    .ForMember(i => i.LogoPath, opt => opt.MapFrom(i => Path.Combine(Constants.Configuration.ImagePath.Contents.Page.App, i.LogoImgName)))
                    .ForMember(i => i.PlayStoreLink, opt => opt.MapFrom(i => string.IsNullOrEmpty(i.PlayStoreLink) ? null : new Uri(i.PlayStoreLink)))
                    .ForMember(i => i.AppStoreLink, opt => opt.MapFrom(i => string.IsNullOrEmpty(i.AppStoreLink) ? null : new Uri(i.AppStoreLink)));

                CreateMap<PageHeader, AdminHomepageViewModel.HeaderLogoViewModel>()
                    .ForMember(h => h.HeaderCorporateLogoPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Page.Header, h.NParkLogoName)))
                    .ForMember(i => i.cFile, opt => opt.Ignore())
                    .ForMember(h => h.HeaderGovernmentLogoPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Page.Header, h.GovLogoName)))
                    .ForMember(i => i.gFile, opt => opt.Ignore());

                CreateMap<HomepageAppInfo, AdminHomepageViewModel.SGBioAtlasRowViewModel>()
                    .ForMember(h => h.LogoImgPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Page.App, h.LogoImgName)))
                    .ForMember(i => i.File, opt => opt.Ignore());


                CreateMap<HomepageAppInfo, AdminHomepageViewModel.SGBioAtlasRowViewModelLog>();



                CreateMap<PageHeader, AdminHomepageViewModel.BackgroundViewModel>()
                    .ForMember(h => h.BackgroundImgPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Page.Header, h.BackgroundImgName)))
                    .ForMember(i => i.File, opt => opt.Ignore());

                CreateMap<PageHeader, AdminHomepageViewModel.MainPageTitleViewModel>()
                    .ForMember(h => h.MainPageTitleLogoPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Page.Header, h.TitleLogoName)))
                    .ForMember(i => i.File, opt => opt.Ignore());

                CreateMap<PageFooter, AdminHomepageViewModel.FooterViewModel>();
                CreateMap<HomepageContact, AdminHomepageViewModel.ContactViewModel>();

                CreateMap<InternetHighlight, HomepageViewModel.ResearchHighlightViewModel>()
                    .ForMember(h => h.ImagePath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Highlights.Internet, h.BackgroundImgName)))
                    .ForMember(h => h.FullUrl, opt => opt.MapFrom(h => h.Url));

                CreateMap<InternetHighlight, AdminHomepageViewModel.HighlightViewModelLog>();
                CreateMap<IntranetHighlight, AdminHomepageViewModel.HighlightViewModelLog>();

                CreateMap<IntranetHighlight, HomepageViewModel.ResearchHighlightViewModel>()
                    .ForMember(h => h.ImagePath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Highlights.Intranet, h.BackgroundImgName)))
                    .ForMember(h => h.FullUrl, opt => opt.MapFrom(h => h.Url));

                CreateMap<InternetHighlight, AdminHomepageViewModel.HighlightViewModel>()
                    .ForMember(h => h.BackgroundImgPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Highlights.Internet, h.BackgroundImgName)));

                CreateMap<IntranetHighlight, AdminHomepageViewModel.HighlightViewModel>()
                    .ForMember(h => h.BackgroundImgPath, opt => opt.MapFrom(h => Url.Combine(Constants.Configuration.ImagePath.Contents.Highlights.Intranet, h.BackgroundImgName)));

                CreateMap<PagedList<InternetHighlight>, IPagedList<AdminHomepageViewModel.HighlightViewModel>>()
                    .ConvertUsing<PagedListConverter<InternetHighlight, AdminHomepageViewModel.HighlightViewModel>>();

                CreateMap<PagedList<IntranetHighlight>, IPagedList<AdminHomepageViewModel.HighlightViewModel>>()
                    .ConvertUsing<PagedListConverter<IntranetHighlight, AdminHomepageViewModel.HighlightViewModel>>();

                CreateMap<InternetHighlight, AdminHomepageViewModel.HighlightEditUploadViewModel>()
                    .ForMember(i => i.File, opt => opt.Ignore());

                CreateMap<IntranetHighlight, AdminHomepageViewModel.HighlightEditUploadViewModel>()
                    .ForMember(i => i.File, opt => opt.Ignore());

                CreateMap<SightingDetail, AdminHomepageViewModel.SightingsSelectedItem>()
                    .ForMember(s => s.SightingId, opt => opt.MapFrom(s => s.Id))
                    .ForMember(s => s.Selected, opt => opt.Ignore())
                    .ForMember(s => s.SpeciesName, opt => opt.MapFrom(s => s.CommonName))
                    .ForMember(s => s.ScientificName, opt => opt.MapFrom(s => s.ScientificName))
                    .ForMember(s => s.Category, opt => opt.MapFrom(s => s.CategoryName))
                    .ForMember(s => s.DateSpotted, opt => opt.MapFrom(s => s.DateSpotted))
                    .ForMember(s => s.Location, opt => opt.MapFrom(s => s.Location))
                    .ForMember(s => s.PostedBy, opt => opt.MapFrom(s => s.Owner.PersonName))
                    .ForMember(s => s.IsShownOnHomePage, opt => opt.Ignore());

                CreateMap<PagedList<SightingDetail>, IPagedList<AdminHomepageViewModel.SightingsSelectedItem>>()
                    .ConvertUsing<PagedListConverter<SightingDetail, AdminHomepageViewModel.SightingsSelectedItem>>();

                CreateMap<ApplicationUser, AccountViewModel.ProfileViewModel>()
                    .ForMember(p => p.UserGroupTree, opt => opt.Ignore())
                    .ForMember(p => p.AccountValidFrom, opt => opt.MapFrom(u => u.DateLastLogin))
                    .ForMember(p => p.AccountValidTo, opt => opt.MapFrom(u => u.DateLastLoginOrActivate.AddDays(ConfigurationService.Instance.AccountExpiryPeriod)))
                    .ForMember(h => h.ProfilePicImgPath, opt => opt.MapFrom(h => h.ProfilePicImgName != null ? Url.Combine(Constants.Configuration.ImagePath.Contents.User.ProfilePic, h.ProfilePicImgName) : "/Content/images/profile-default.jpg"))
                    .ForMember(p => p.Achievements, opt => opt.Ignore())
                    .ForMember(p => p.FollowersCount, opt => opt.MapFrom(u => u.Followers.Count));

                CreateMap<AccountViewModel.ProfileEditViewModel, AccountViewModel.ProfileViewModel>()
                    .ForMember(p => p.FollowersCount, opt => opt.Ignore())
                    .ForMember(p => p.Id, opt => opt.Ignore())
                    .ForMember(p => p.IsActive, opt => opt.Ignore())
                    .ForMember(p => p.Email, opt => opt.Ignore())
                    .ForMember(p => p.CreatedAt, opt => opt.Ignore())
                    .ForMember(p => p.DateLastLogin, opt => opt.Ignore())
                    .ForMember(p => p.UserGroupTree, opt => opt.Ignore())
                    .ForMember(p => p.AccountValidFrom, opt => opt.Ignore())
                    .ForMember(p => p.AccountValidTo, opt => opt.Ignore())
                    .ForMember(p => p.Blacklisted, opt => opt.Ignore())
                    .ForMember(p => p.Achievements, opt => opt.Ignore())
                    .ForMember(p => p.ProfilePicImgPath, opt => opt.Ignore());

                CreateMap<NameValueCollection, AccountViewModel.ActivateAccountExtViewModel>()
                    .ForMember(m => m.Email, opt => opt.MapFrom(c => c["Email"]))
                    .ForMember(m => m.ExternalUserId, opt => opt.MapFrom(c => c["ExternalUserId"]))
                    .ForMember(m => m.FirstName, opt => opt.MapFrom(c => c["FirstName"]))
                    .ForMember(m => m.LastName, opt => opt.MapFrom(c => c["LastName"]))
                    .ForMember(m => m.PersonName, opt => opt.MapFrom(c => c["PersonName"]))
                    .ForMember(m => m.Password, opt => opt.MapFrom(c => c["Password"]))
                    .ForMember(m => m.ConfirmPassword, opt => opt.MapFrom(c => c["ConfirmPassword"]))
                    .ForMember(m => m.HasAgreedTnC, opt => opt.MapFrom(c => bool.Parse(c["HasAgreedTnC"] ?? bool.FalseString)))
             
                    .ForMember(m => m.isFB, opt => opt.Ignore());

                CreateMap<NameValueCollection, AccountViewModel.ActivateAccountViewModel>()
                    .ForMember(m => m.Email, opt => opt.MapFrom(c => c["Email"]))
                    .ForMember(m => m.Code, opt => opt.MapFrom(c => c["Code"]));

                CreateMap<SystemParameters, SystemParametersViewModel>()
                    .ForMember(m => m.IsEditingAccountInactive, opt => opt.Ignore())
                    .ForMember(m => m.IsEditingPasswordExpiry, opt => opt.Ignore())
                    .ForMember(m => m.IsEditingAuditLogs, opt => opt.Ignore())
                    .ForMember(m => m.IsEditingSightingsMinVote, opt => opt.Ignore())
                    .ForMember(m => m.IsEditingSightingsMinPercent, opt => opt.Ignore())
                    .ForMember(m => m.IsEditingSightingsFeaturedPeriod, opt => opt.Ignore())
                    .ForMember(m => m.IsEditingSightingsFeaturedLike, opt => opt.Ignore())
                    .ForMember(m => m.IsEditingPageSize, opt => opt.Ignore())
                    .ForMember(m => m.IsEditingAuditLogMonthlyReportRecipients, opt => opt.Ignore())
                    .ForMember(m => m.IsEditingRenewPermitBeforeExpiryPeriod, opt => opt.Ignore())
                    .ForMember(m => m.IsEditingLatestVerCodeiOS, opt => opt.Ignore())
                    .ForMember(m => m.IsEditingLatestVerCodeAndroid, opt => opt.Ignore());

                CreateMap<SystemParametersViewModel, SystemParametersViewModelLog>();

                CreateMap<MaintenanceNotice, MaintenanceNoticeViewModel>()
                    .ForMember(m => m.startFrom, opt => opt.MapFrom(s => s.startFrom.ToString("dd/MM/yyyy HH:mm")))
                    .ForMember(m => m.endAt, opt => opt.MapFrom(s => s.endAt.ToString("dd/MM/yyyy HH:mm")));

                CreateMap<ApplicationUser, ProfileViewModel>()
                    .ForMember(p => p.FollowersCount, opt => opt.MapFrom(u => u.Followers.Count))
                    .ForMember(p => p.ProfilePicImgPath, opt => opt.MapFrom(u => u.ProfilePicImgName != null ? Url.Combine(Constants.Configuration.ImagePath.Contents.User.ProfilePic, u.ProfilePicImgName) : "/Content/images/profile-default.jpg"))
                    .ForMember(p => p.Achievements, opt => opt.Ignore())
                    .ForMember(p => p.HasFollowed, opt => opt.Ignore())
                    .ForMember(p => p.IsSelf, opt => opt.Ignore());

                CreateMap<ApplicationUser, PermitViewModel.TermsViewModel>()
                    .ForMember(t => t.HasAgreedTnC, opt => opt.MapFrom(u => u.HasAgreedPermitApplicationTnC));

                CreateMap<SightingDetail, HomepageViewModel.SelectedSightingViewModel>()
                    .ForMember(vm => vm.Id, opt => opt.MapFrom(s => s.Id))
                    .ForMember(vm => vm.Title, opt => opt.MapFrom(s => s.CommonName))
                    .ForMember(vm => vm.Category, opt => opt.MapFrom(s => s.CategoryName))
                    .ForMember(vm => vm.DateSpotted, opt => opt.MapFrom(s => s.DateSpotted.ToString("dd MMMM yyyy")))
                    .ForMember(vm => vm.Latitude, opt => opt.MapFrom(s => s.Latitude))
                    .ForMember(vm => vm.Longitude, opt => opt.MapFrom(s => s.Longitude))
                    .ForMember(vm => vm.RecordedBy, opt => opt.MapFrom(s => s.Owner.PersonName))
                    .ForMember(vm => vm.ImagePath, opt => opt.MapFrom(s => s.GetCroppedImage()));

                CreateMap<MainGroup, UserGroupViewModel.SelectedUserGroupViewModel>()
                    .ForMember(g => g.GroupName, opt => opt.MapFrom(g => g.Name))
                    .ForMember(g => g.GroupType, opt => opt.UseValue<Enumerations.User.GroupType>(Enumerations.User.GroupType.Main))
                    .ForMember(g => g.ParentId, opt => opt.UseValue<long?>(null))
                    .ForMember(g => g.Domain, opt => opt.MapFrom(g => g.Domain));

                CreateMap<SubGroup, UserGroupViewModel.SelectedUserGroupViewModel>()
                    .ForMember(g => g.GroupName, opt => opt.MapFrom(g => g.Name))
                    .ForMember(g => g.GroupType, opt => opt.UseValue<Enumerations.User.GroupType>(Enumerations.User.GroupType.Sub))
                    .ForMember(g => g.ParentId, opt => opt.MapFrom(g => g.ParentGroup.Id))
                    .ForMember(g => g.Domain, opt => opt.UseValue(null));

                CreateMap<SubSubGroup, UserGroupViewModel.SelectedUserGroupViewModel>()
                    .ForMember(g => g.GroupName, opt => opt.MapFrom(g => g.Name))
                    .ForMember(g => g.GroupType, opt => opt.UseValue<Enumerations.User.GroupType>(Enumerations.User.GroupType.SubSub))
                    .ForMember(g => g.ParentId, opt => opt.MapFrom(g => g.ParentGroup.Id))
                    .ForMember(g => g.Domain, opt => opt.UseValue(null));

                //CreateMap<PermitViewModel.ApplicationFormViewModelBase, ResearchApplication>()
                //    .ForMember(a => a.Id, opt => opt.Ignore())
                //    .ForMember(a => a.CreatedAt, opt => opt.Ignore())
                //    .ForMember(a => a.UpdatedAt, opt => opt.Ignore())
                //    .ForMember(a => a.Forums, opt => opt.Ignore())
                //    .ForMember(a => a.Inboxes, opt => opt.Ignore())
                //    .ForMember(a => a.PermitApplications, opt => opt.Ignore())
                //    .ForMember(a => a.Reports, opt => opt.Ignore())
                //    .ForMember(a => a.Researcher, opt => opt.Ignore());

                //CreateMap<PermitViewModel.ApplicationFormSubmitViewModel, ResearchInstitutional>()
                //    .ForMember(a => a.Id, opt => opt.Ignore())
                //    .ForMember(a => a.CreatedAt, opt => opt.Ignore())
                //    .ForMember(a => a.UpdatedAt, opt => opt.Ignore())
                //    .ForMember(a => a.Application, opt => opt.Ignore());

                CreateMap<ResearchApplicationDraft, PermitViewModel.ApplicationFormSubmitDraftViewModel>()
                    .ForMember(a => a.DraftId, opt => opt.MapFrom(d => d.Id))
                    .ForMember(a => a.IndemnityFormFile, opt => opt.Ignore())
                    .ForMember(a => a.MiscFile, opt => opt.Ignore())
                    .ForMember(a => a.PresentFindingsDateStartYear, opt => opt.Ignore())
                    .ForMember(a => a.PresentFindingsDateStartMonth, opt => opt.Ignore())
                    .ForMember(a => a.PresentFindingsDateStartDay, opt => opt.Ignore())
                    .ForMember(a => a.PresentFindingsDateEndYear, opt => opt.Ignore())
                    .ForMember(a => a.PresentFindingsDateEndMonth, opt => opt.Ignore())
                    .ForMember(a => a.PresentFindingsDateEndDay, opt => opt.Ignore())
                    .ForMember(a => a.isPending_eSign_NewPermit, opt => opt.Ignore())
                    .ForMember(a => a.ResearcherId, opt => opt.MapFrom(d => d.Researcher.Id))
                    .ForMember(a => a.ResearcherName, opt => opt.MapFrom(d => d.Researcher.PersonName))
                    .ForMember(a => a.ResearcherEmail, opt => opt.MapFrom(d => d.Researcher.Email))
                    .ForMember(a => a.RealMiscFileName, opt => opt.MapFrom(d => d.RealMiscFileNameNotEmpty))
                    .ForMember(a => a.PresentFindingsDateStart, opt => opt.ResolveUsing<ResearchPresentFindingsDateStartDraftResolver>())
                    .ForMember(a => a.PresentFindingsDateEnd, opt => opt.ResolveUsing<ResearchPresentFindingsDateEndDraftResolver>())
                    .ForMember(a => a.NParksContactPersonEmailWithoutDomain, opt => opt.ResolveUsing<ResearchContactPersonDraftResolver>());

                //Audit log
                CreateMap<PermitViewModel.ApplicationFormSubmitDraftViewModel, PermitViewModel.ApplicationFormViewModelLog>();
                CreateMap<PermitViewModel.ApplicationFormSubmitViewModel, PermitViewModel.ApplicationFormViewModelLog>()
                    .ForMember(a => a.DraftId, opt => opt.Ignore());
                
                CreateMap<PermitViewModel.FieldSurveyTeamEditViewModel, PermitViewModel.FieldSurveyTeamViewModelLog>()
                    .ForMember(a => a.PreviousMemberID, opt => opt.Ignore()); 
                CreateMap<PermitViewModel.FieldSurveyTeamCreateViewModel, PermitViewModel.FieldSurveyTeamViewModelLog>()
                    .ForMember(a => a.PreviousMemberID, opt => opt.Ignore())
                    .ForMember(a => a.Id, opt => opt.Ignore());
                

                CreateMap<ApplicationStatusViewModel.EditViewModel, ApplicationStatusViewModel.EditViewModelLog>();
                CreateMap<ApplicationStatusViewModel.FieldSurveyTeamEditViewModel, ApplicationStatusViewModel.FieldSurveyTeamViewModelLog>();

                CreateMap<ApiModelUser.RequestRegister, ApiModelUser.RequestRegisterLog>();
                CreateMap< SurveyViewModel.SurveyInforViewModel, SurveyViewModel.SurveyInforViewModelLog>()
                    .ForMember(a => a.Id, opt => opt.Ignore())
                    .ForMember(a => a.LogoFileName, opt => opt.Ignore())
                    .ForMember(a => a.SurveyQuestion, opt => opt.Ignore());

                CreateMap<ApiModelSighting.RequestSubmitSighting, ApiModelSighting.RequestSubmitSightingLog>()
                    .ForMember(a => a.SightingImage, opt => opt.Ignore());
                CreateMap<ApiModelSighting.RequestSightings, ApiModelSighting.RequestSightingsLog>();
                CreateMap<ResourceViewModel.ResourceStepFourViewModel, ResourceViewModel.ResourceUploadLog>()
                    .ForMember(a => a.GroupPermissions, opt => opt.Ignore())
                    .ForMember(a => a.isAddToExistingResource, opt => opt.Ignore())
                    .ForMember(a => a.Documents, opt => opt.Ignore());

                CreateMap<Models.SightingDetail, SightingsViewModel.SubmitSightingLog>()
                    .ForMember(a => a.ProjectID, opt => opt.Ignore());

                CreateMap<SightingsViewModel.SightingsInforViewModel, SightingsViewModel.SubmitSightingLog>()
                    .ForMember(a => a.Id, opt => opt.Ignore())
                    .ForMember(a => a.ProjectID, opt => opt.Ignore())
                    .ForMember(a => a.CategoryName, opt => opt.Ignore())
                    .ForMember(a => a.SightingImage, opt => opt.Ignore())
                    .ForMember(a => a.DateSpotted, opt => opt.Ignore());

                CreateMap<ProjectViewModel.ProjectInforViewModel, ProjectViewModel.ProjectInforViewModelLog>()
                    .ForMember(a => a.FileName, opt => opt.Ignore()); ;

                CreateMap<SurveyViewModel.SurveyAdminViewModel, SurveyViewModel.SurveyAdminViewModelLog>();
                CreateMap<SurveyViewModel.SurveyInforViewModel, SurveyViewModel.SurveyAdminViewModelLog>();

                CreateMap<SurveySubmissionViewModel, SurveySubmissionViewModelLog>();
                CreateMap<SpeciesByCategoryViewModel, SpeciesByCategoryViewModelLog>();
                CreateMap<SurveySpeciesViewModel, SurveySpeciesViewModelLog>();
                CreateMap<SurveyQuestionAnswerByTypeViewModel, SurveyQuestionAnswerByTypeViewModelLog>();
                CreateMap<AdditionalSpecies, AdditionalSpeciesLog>();

                //--Audit log


                CreateMap<ResearchFieldSurveyTeamMemberDraft, PermitViewModel.FieldSurveyTeamEditViewModel>()
                    .ForMember(d => d.DOB, opt => opt.MapFrom(d => d.DOB.ToString("MM/dd/yyyy")))
                    .ForMember(d => d.ProfileImageFile, opt => opt.Ignore());

                CreateMap<GISLocation, PermitViewModel.GISLocationViewModel>();

                CreateMap<ResearchApplicationDraft, ApplicationStatusViewModel.HistoryViewModel>()
                    .ForMember(h => h.Id, opt => opt.MapFrom(d => d.Id))
                    .ForMember(h => h.ApplicationId, opt => opt.MapFrom(d => "Draft " + d.Id))
                    .ForMember(h => h.PermitNo, opt => opt.UseValue(""))
                    .ForMember(h => h.PermitId, opt => opt.UseValue(0))
                    .ForMember(h => h.CreationDate, opt => opt.MapFrom(d => d.CreatedAt))
                    .ForMember(h => h.PermitApplication_Or_Draft_CreationDate, opt => opt.MapFrom(d => d.CreatedAt))
                    .ForMember(h => h.Status, opt => opt.UseValue("Draft"))
                    .ForMember(h => h.IssuedDate, opt => opt.UseValue<DateTimeOffset?>(null))
                    .ForMember(h => h.ExpiryDate, opt => opt.UseValue<DateTimeOffset?>(null))
                    .ForMember(h => h.SubmittedInterimReport, opt => opt.UseValue(false))
                    .ForMember(h => h.IsDraft, opt => opt.UseValue(true))
					.ForMember(h => h.SubmittedType, opt=> opt.Ignore())
                    .ForMember(h => h.PreviewModal, opt => opt.Ignore())
                    //.ForMember(h => h.isPending_eSign, opt => opt.Ignore())
                    .ForMember(h => h.statusList, opt => opt.Ignore())
                    .ForMember(h => h.isPending_eSign, opt => opt.MapFrom(d=>d.FieldSurveyTeamMembers.Any(m=>m.AcknowledgeSignStatus> (int)BIOME.Enumerations.Research.Permit.AcknowledgeSignStatus.NoStatus && m.AcknowledgeSignStatus!= (int)BIOME.Enumerations.Research.Permit.AcknowledgeSignStatus.Signed)))
                    .ForMember(h => h.PermitReportTotal, opt => opt.Ignore())
                    .ForMember(h => h.ApplicationReportTotal, opt => opt.Ignore())
                    .ForMember(h => h.ApprovedDate, opt => opt.Ignore())
                    .ForMember(h => h.IdStr, opt => opt.Ignore());          //add to fix VDP -NParks - VDP-200426-003 (MEDIUM)

                
                CreateMap<ResearchPermitApplication, ApplicationStatusViewModel.HistoryViewModel>()
                    .ForMember(h => h.Id, opt => opt.MapFrom(pa => pa.AtResearchApplication.Id))
                    .ForMember(h => h.ApplicationId, opt => opt.MapFrom(pa => pa.AtResearchApplication.Id))
                    .ForMember(h => h.PermitNo, opt => opt.MapFrom(pa => pa.PermitNumber))
                    .ForMember(h => h.PermitId, opt => opt.MapFrom(pa => pa.Id))
                    .ForMember(h => h.Title, opt => opt.MapFrom(pa => pa.AtResearchApplication.Title))
                    .ForMember(h => h.CreationDate, opt => opt.MapFrom(pa => pa.CreatedAt))
                    .ForMember(h => h.PermitApplication_Or_Draft_CreationDate, opt => opt.MapFrom(pa => pa.AtResearchApplication.CreatedAt))

                    .ForMember(h => h.Status, opt => opt.MapFrom(pa => ResearchHelper.GetPermitApplicationProperStatus(pa, false).ToDisplayStatusString()))
                    //.ForMember(h => h.IssuedDate, opt => opt.UseValue<DateTimeOffset?>(null))
                    .ForMember(h => h.IssuedDate, opt => opt.MapFrom(pa => pa.DateIssued != null ? new DateTimeOffset?(pa.DateIssued.SavedDate) : new DateTimeOffset?()))
                    .ForMember(h => h.ExpiryDate, opt => opt.MapFrom<DateTimeOffset?>(pa => pa.DateExpire))
                    //.ForMember(h => h.SubmittedInterimReport, opt => opt.MapFrom(pa => pa.AtResearchApplication.Reports.Count > 0))
                    .ForMember(h => h.SubmittedInterimReport, opt => opt.MapFrom(pa => (pa.CreatedAt < BIOME.Constants.Research.Permit.CR3_EffectedDate && pa.AtResearchApplication.Reports.Count > 0) || pa.AtResearchApplication.Reports.Where(a => a.ResearchPermitApplication_Id == pa.Id).Count() > 0)) //CR3&CR4 Phase1
                    .ForMember(h => h.IsDraft, opt => opt.UseValue(false))
                    //.ForMember(h => h.isPending_eSign, opt => opt.Ignore())
                    .ForMember(h => h.isPending_eSign, opt => opt.MapFrom(d => d.FieldSurveyTeamMembers.Any(m => m.AcknowledgeSignStatus > (int)BIOME.Enumerations.Research.Permit.AcknowledgeSignStatus.NoStatus && m.AcknowledgeSignStatus != (int)BIOME.Enumerations.Research.Permit.AcknowledgeSignStatus.Signed)))
                    .ForMember(h => h.PermitReportTotal, opt => opt.Ignore())
                    .ForMember(h => h.ApplicationReportTotal, opt => opt.Ignore())
                    .ForMember(h => h.ApprovedDate, opt => opt.Ignore())
                    .ForMember(h => h.IdStr, opt => opt.Ignore())   //add to fix VDP -NParks - VDP-200426-003 (MEDIUM)
                    .ForMember(h => h.statusList, opt => opt.Ignore())
                    .ForMember(h => h.PreviewModal, opt => opt.MapFrom(pa => new ApplicationStatusViewModel.DetailsModalViewModel()
                    {
                        PermitNumber = pa.PermitNumber,
                        PreviewItems = new List<ApplicationStatusViewModel.PreviewItemViewModel>()
                    }))
                    .AfterMap((pa, hVM) =>
                    {
                        string protectedFilePath = ConfigurationManager.AppSettings[BIOME.Constants.Configuration.Server.AppSettings.ProtectedFilePath];

                        //if (!string.IsNullOrEmpty(pa.AtResearchApplication.PassCombinedFileName))
                        //{
                        //    hVM.PreviewModal.PreviewItems.Add(new ApplicationStatusViewModel.PreviewItemViewModel()
                        //    {
                        //        //DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.PassLetterFile, pa.AtResearchApplication.PassCombinedFileName)
                        //        DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.PassLetterFile, pa.Passes.First().PassFilename)
                        //    });
                        //}
                        if (pa.Passes.Count > 0)
                        {

                            //foreach (var pass in pa.Passes)
                            //{
                            hVM.PreviewModal.PreviewItems.Add(new ApplicationStatusViewModel.PreviewItemViewModel()
                            {
                                //DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.PassLetterFile, "PermitPass_" + pa.PermitNumber.Replace('/', '_') + ".pdf")
                                DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.PermitFileURLRoute + "?ftype=0&id=" + pa.Id)

                                // DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.PassLetterFile, pass.PassFilename)
                            });
                            // }

                        }

                        if (pa.Letters.Count > 0)
                        {
                            hVM.PreviewModal.PreviewItems.Add(new ApplicationStatusViewModel.PreviewItemViewModel()
                            {
                                //DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.PassLetterFile, pa.Letters.First().LetterFileName)
                                DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.PermitFileURLRoute + "?ftype=1023&id=" + pa.Id)
                            });
                        }

                        hVM.PreviewModal.PreviewItems.Add(new ApplicationStatusViewModel.PreviewItemViewModel()
                        {
                            //DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.PassLetterFile, "PermitApplication" + pa.Id.ToString() + ".pdf")
                            DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.PermitApplicationFileURLRoute + pa.ResearchApplicationId + "/?permitId=" + pa.Id + "&inlineview=true")
                            //DocFilePath = Url.RouteUrl(ApplicationStatusControllerRoute.GetDownloadApplicationPDF, new { applicationId = existingModel.ResearchApplicationId, permitId = existingModel.PermitId })
                        });
                        //foreach (var report in pa.AtResearchApplication.Reports)
                        //{
                        //    hVM.PreviewModal.PreviewItems.Add(new ApplicationStatusViewModel.PreviewItemViewModel()
                        //    {
                        //        DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.ReportFile, report.FileName)
                        //    });
                        //}
                        //hVM.PreviewModal.PreviewItems.Add(new ApplicationStatusViewModel.PreviewItemViewModel()
                        //{
                        //    DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.IndemnityFile, pa.AtResearchApplication.IndemnityFormFileName)
                        //});
                        //if (!string.IsNullOrEmpty(pa.AtResearchApplication.MiscFileName))
                        //{
                        //    hVM.PreviewModal.PreviewItems.Add(new ApplicationStatusViewModel.PreviewItemViewModel()
                        //    {
                        //        DocFilePath = Url.Combine(Constants.Configuration.FilePath.Contents.Permit.IndemnityFile, pa.AtResearchApplication.MiscFileName)
                        //    });
                        //}
                    });

                CreateMap<PagedList<ResearchApplicationDraft>, IPagedList<ApplicationStatusViewModel.HistoryViewModel>>()
                    .ConvertUsing<PagedListConverter<ResearchApplicationDraft, ApplicationStatusViewModel.HistoryViewModel>>();

                CreateMap<PagedList<ResearchPermitApplication>, IPagedList<ApplicationStatusViewModel.HistoryViewModel>>()
                    .ConvertUsing<PagedListConverter<ResearchPermitApplication, ApplicationStatusViewModel.HistoryViewModel>>();

                CreateMap<ResearchApplication, ApplicationStatusViewModel.DetailsViewModel>()
                    .ForMember(d => d.ResearchApplicationId, opt => opt.MapFrom(a => a.Id))
                    .ForMember(a => a.IsOriginal, opt => opt.Ignore())
                    .ForMember(a => a.IsExpired, opt => opt.Ignore())
                    .ForMember(a => a.IsApplicationExpired, opt => opt.Ignore())
                    .ForMember(a => a.DateApproved, opt => opt.Ignore())
                    .ForMember(a => a.DateIssued, opt => opt.Ignore())
                    .ForMember(a => a.DateExpire, opt => opt.Ignore())
                    .ForMember(a => a.CanRenewPermit, opt => opt.Ignore())
                    .ForMember(a => a.CanEarlyRenewPermit, opt => opt.Ignore()) //cr2 -- thazin 9 May 2020
                    .ForMember(a => a.CanAmendPermit, opt => opt.Ignore())
                    .ForMember(a => a.CanViewPass, opt => opt.Ignore())
                    .ForMember(a => a.CanViewPermit, opt => opt.Ignore())
                    .ForMember(a => a.CanUploadReport, opt => opt.Ignore())
                    .ForMember(a => a.CanAssignMainApplicant, opt => opt.Ignore())
                    .ForMember(a => a.CanViewDicussionForum, opt => opt.Ignore())
                    .ForMember(a => a.CanRequestSiteVisit, opt => opt.Ignore())
                    .ForMember(a => a.StudySubjectIds, opt => opt.MapFrom(a => a.StudySubjects.Select(s => s.Id)))
                    .ForMember(a => a.ResearchTypeId, opt => opt.MapFrom(a => a.ResearchType.Id))
                    .ForMember(a => a.ResearchTypeName, opt => opt.MapFrom(a => a.ResearchType.Name))
                    .ForMember(a => a.HabitatName, opt => opt.MapFrom(a => a.Habitat.Name))
                    .ForMember(a => a.HabitatId, opt => opt.MapFrom(a => a.Habitat.Id))
                    .ForMember(a => a.ResearcherId, opt => opt.MapFrom(a => a.Researcher.Id))
                    .ForMember(a => a.ResearcherEmail, opt => opt.MapFrom(a => a.Researcher.Email))
                    .ForMember(a => a.ResearcherName, opt => opt.MapFrom(a => a.Researcher.PersonName))
                    .ForMember(a => a.PermitNumber, opt => opt.Ignore())
                    .ForMember(a => a.PermitId, opt => opt.Ignore())
                    .ForMember(a => a.PermitDateCreated, opt => opt.Ignore()) //CR3&CR4 Phase1
                    .ForMember(a => a.Status, opt => opt.Ignore())
                    //.ForMember(a => a.HasUploadedReports, opt => opt.MapFrom(a => a.Reports.Count > 0))
                    .ForMember(a => a.InstitutionName, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.InstitutionName : ""))
                    .ForMember(a => a.InstitutionAddress, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.InstitutionAddress : ""))

                    .ForMember(a => a.InstitutionAddressBlockHouseNumber, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.InstitutionAddressBlockHouseNumber : ""))
                    .ForMember(a => a.InstitutionAddressStreetName, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.InstitutionAddressStreetName : ""))
                    .ForMember(a => a.InstitutionAddressFloorNumber, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.InstitutionAddressFloorNumber : ""))
                    .ForMember(a => a.InstitutionAddressUnitNumber, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.InstitutionAddressUnitNumber : ""))
                    .ForMember(a => a.InstitutionAddressBuildingName, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.InstitutionAddressBuildingName : ""))
                    .ForMember(a => a.InstitutionAddressPostalCode, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.InstitutionAddressPostalCode : ""))

                    .ForMember(a => a.PIPhonePrefixCountryCode, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.PIPhonePrefixCountryCode : ""))
                    .ForMember(a => a.PIPhoneAreaCode, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.PIPhoneAreaCode : ""))
                    .ForMember(a => a.PIPhoneNumber, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.PIPhoneNumber : ""))

                    .ForMember(a => a.PrincipalInvestigatorEmail, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.PrincipalInvestigatorEmail : ""))
                    .ForMember(a => a.PrincipalInvestigatorName, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.PrincipalInvestigatorName : ""))
                    .ForMember(a => a.PrincipalInvestigatorNumber, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.PrincipalInvestigatorNumber : ""))
                    .ForMember(a => a.DetailsReport, opt => opt.ResolveUsing<ResearchReportResolver>())
                    .ForMember(a => a.SiteVisit, opt => opt.Ignore())
                    .ForMember(a => a.SiteVisitHistoryList, opt => opt.Ignore())
                    .ForMember(a => a.Passes, opt => opt.Ignore())
                    .ForMember(a => a.Letter, opt => opt.Ignore())
                    .ForMember(a => a.StudyLocationsAccesses, opt => opt.Ignore())
                    .ForMember(a => a.SubmittedType, opt => opt.Ignore())
                    .ForMember(a => a.PassesCombined, opt => opt.MapFrom(pc => new PermitViewModel.PassCombinedViewModel() { PassFileName = pc.PassCombinedFileName }));

                CreateMap<ResearchApplicationDraft, ApplicationStatusViewModel.DetailsViewModel>()
                    .ForMember(d => d.ResearchApplicationId, opt => opt.MapFrom(a => a.Id))
                    .ForMember(a => a.IsOriginal, opt => opt.Ignore())
                    .ForMember(a => a.IsExpired, opt => opt.Ignore())
                    .ForMember(a => a.IsApplicationExpired, opt => opt.Ignore())
                    .ForMember(a => a.DateApproved, opt => opt.Ignore())
                    .ForMember(a => a.DateIssued, opt => opt.Ignore())
                    .ForMember(a => a.DateExpire, opt => opt.Ignore())
                    .ForMember(a => a.CanRenewPermit, opt => opt.Ignore())
                    .ForMember(a => a.CanEarlyRenewPermit, opt => opt.Ignore()) //cr2 -- thazin 9 May 2020
                    .ForMember(a => a.CanAmendPermit, opt => opt.Ignore())
                    .ForMember(a => a.CanViewPass, opt => opt.Ignore())
                    .ForMember(a => a.CanViewPermit, opt => opt.Ignore())
                    .ForMember(a => a.CanUploadReport, opt => opt.Ignore())
                    .ForMember(a => a.CanAssignMainApplicant, opt => opt.Ignore())
                    .ForMember(a => a.CanViewDicussionForum, opt => opt.Ignore())
                    .ForMember(a => a.CanRequestSiteVisit, opt => opt.Ignore())
                    .ForMember(a => a.StudySubjectIds, opt => opt.MapFrom(a => a.StudySubjectIds))
                    .ForMember(a => a.ResearchTypeId, opt => opt.MapFrom(a => a.ResearchTypeId))
                    .ForMember(a => a.ResearchTypeName, opt => opt.Ignore())
                    .ForMember(a => a.HabitatName, opt => opt.Ignore())
                    .ForMember(a => a.HabitatId, opt => opt.MapFrom(a => a.HabitatId))
                    .ForMember(a => a.ResearcherId, opt => opt.MapFrom(a => a.Researcher.Id))
                    .ForMember(a => a.ResearcherEmail, opt => opt.MapFrom(a => a.Researcher.Email))
                    .ForMember(a => a.ResearcherName, opt => opt.MapFrom(a => a.Researcher.PersonName))
                    .ForMember(a => a.PermitNumber, opt => opt.Ignore())
                    .ForMember(a => a.PermitId, opt => opt.Ignore())
                    .ForMember(a => a.PermitDateCreated, opt => opt.Ignore()) //CR3&CR4 Phase1
                    .ForMember(a => a.Status, opt => opt.Ignore())
                    //.ForMember(a => a.HasUploadedReports, opt => opt.MapFrom(a => a.Reports.Count > 0))
                    .ForMember(a => a.InstitutionName, opt => opt.MapFrom(a => a.InstitutionName))
                    .ForMember(a => a.InstitutionAddress, opt => opt.MapFrom(a => a.InstitutionAddress))
                    .ForMember(a => a.PrincipalInvestigatorEmail, opt => opt.MapFrom(a => a.PrincipalInvestigatorEmail))
                    .ForMember(a => a.PrincipalInvestigatorName, opt => opt.MapFrom(a => a.PrincipalInvestigatorName))
                    .ForMember(a => a.PrincipalInvestigatorNumber, opt => opt.MapFrom(a => a.PrincipalInvestigatorNumber))
                    .ForMember(a => a.DetailsReport, opt => opt.Ignore())
                    .ForMember(a => a.SiteVisit, opt => opt.Ignore())
                    .ForMember(a => a.SiteVisitHistoryList, opt => opt.Ignore())
                    .ForMember(a => a.Passes, opt => opt.Ignore())
                    .ForMember(a => a.Letter, opt => opt.Ignore())
                    .ForMember(a => a.StudyLocationsAccesses, opt => opt.Ignore())
                    .ForMember(a => a.SubmittedType, opt => opt.Ignore())
                    .ForMember(a => a.PassesCombined, opt => opt.Ignore());


                CreateMap<ResearchApplication, ApplicationStatusViewModel.ResearchApplicationES>()
                    .ForMember(a => a.ResearchTypeId, opt => opt.MapFrom(a => a.ResearchType.Id))
                    .ForMember(a => a.ResearchTypeName, opt => opt.MapFrom(a => a.ResearchType.Name))
                    .ForMember(a => a.HabitatName, opt => opt.MapFrom(a => a.Habitat.Name))
                    .ForMember(a => a.StudyLocationsDrawnGeo, opt => opt.Ignore())
                    .ForMember(a => a.HabitatId, opt => opt.MapFrom(a => a.Habitat.Id))
                    .ForMember(a => a.ResearcherId, opt => opt.MapFrom(a => a.Researcher.Id))
                    .ForMember(a => a.ResearcherEmail, opt => opt.MapFrom(a => a.Researcher.Email))
                    .ForMember(a => a.ResearcherName, opt => opt.MapFrom(a => a.Researcher.PersonName))
                    .ForMember(a => a.InstitutionName, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.InstitutionName : ""))
                    .ForMember(a => a.InstitutionAddress, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.InstitutionAddress : ""))
                    .ForMember(a => a.PrincipalInvestigatorEmail, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.PrincipalInvestigatorEmail : ""))
                    .ForMember(a => a.PrincipalInvestigatorName, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.PrincipalInvestigatorName : ""))
                    .ForMember(a => a.PrincipalInvestigatorNumber, opt => opt.MapFrom(a => a.Institutional != null ? a.Institutional.PrincipalInvestigatorNumber : ""))
                    .ForMember(a => a.SubmittedType, opt => opt.Ignore());


                CreateMap<ResearchPermitApplication, ApplicationStatusViewModel.PermitApplicationES>()
                    .ForMember(a => a.HasIssuedStatus, opt => opt.MapFrom(a => a.Status.Count(b=>b.StatusName== Research.Permit.Status.Issued)>0));

                CreateMap<SightingDetail, SightingsViewModel.SightingsES>()
                    .ForMember(a => a.OwnerName, opt => opt.MapFrom(a => a.Owner != null ? a.Owner.PersonName : ""))
                    .ForMember(a => a.SightingLocation, opt => opt.Ignore());

                CreateMap<SightingComment, SightingsViewModel.SightingCommentES>();
                CreateMap<SightingTaxonomy, SightingsViewModel.SightingTaxonomyES>();
                CreateMap<SightingTaxonomyClassification, SightingsViewModel.SightingTaxonomyClassificationES>();

                CreateMap<ProjectSighting, SightingsViewModel.ProjectSightingsES>();
                CreateMap<ProjectDetail, SightingsViewModel.ProjectDetailES>();
                CreateMap<ProjectDetail, ProjectViewModel.ProjectAdminViewModelLog>();

                CreateMap<ProjectMember, SightingsViewModel.ProjectMemberES>()
                    .ForMember(a => a.PersonName, opt => opt.MapFrom(a => a.Member != null ? a.Member.PersonName : ""));

                CreateMap<ResourceDocument, ResourceViewModel.ResourceDocumentES>()
                    .ForMember(a => a.StructureDataList, opt => opt.Ignore())
                    .ForMember(a => a.FileAttach, opt => opt.Ignore())
                    .ForMember(a => a.FileContent, opt => opt.Ignore())
                    .ForMember(a => a.MultipileLocationDrawnGeo, opt => opt.Ignore());
                CreateMap<ResourceMetaData, ResourceViewModel.ResourceMetaDataES>();
                CreateMap<ResourceMetaDataAuthor, ResourceViewModel.ResourceMetaDataAuthorES>();
                CreateMap<Group, ResourceViewModel.GroupES>();




                CreateMap<ResearchReport, ApplicationStatusViewModel.DetailsReportItemViewModel>()
                    .ForMember(ri => ri.ReportFileName, opt => opt.MapFrom(r => r.FileName))
                    .ForMember(ri => ri.RealReportFileName, opt => opt.MapFrom(r => r.RealFileNameNotEmpty))
                    .ForMember(ri => ri.ResearchPermitApplication_Id, opt => opt.MapFrom(r => r.ResearchPermitApplication_Id)) //CR3&CR4 Phase1
                    .ForMember(ri => ri.ReportFilePath, opt => opt.MapFrom(r => Url.Combine(BIOME.Constants.Configuration.FilePath.Contents.Permit.ReportFile, r.FileName)))
                    .ForMember(ri => ri.Id, opt => opt.MapFrom(r => r.Id));


                CreateMap<ResearchFieldSurveyTeamMember, ApplicationStatusViewModel.FieldSurveyTeamViewModel>()
                  .ForMember(d => d.DOB, opt => opt.MapFrom(d => d.DOB.ToString("MM/dd/yyyy")));
                //CreateMap<GISLocation, ApplicationStatusViewModel.StudyLocationAccessViewModel>()
                //    .ForMember(l => l.Access, opt => opt.Ignore())
                //    .ForMember(l => l.SiteManagerIds, opt => opt.Ignore());

                CreateMap<ResearchFieldSurveyTeamMemberDraft, ApplicationStatusViewModel.FieldSurveyTeamViewModel>()
                  .ForMember(d => d.PreviousMemberID, opt => opt.Ignore())
                  .ForMember(d => d.DOB, opt => opt.MapFrom(d => d.DOB.ToString("MM/dd/yyyy")));

                CreateMap<ResearchFieldSurveyTeamMember, ApplicationStatusViewModel.FieldSurveyTeamEditViewModel>()
                    .ForMember(vm => vm.ProfileImageFile, opt => opt.Ignore())
                .ForMember(d => d.DOB, opt => opt.MapFrom(d => d.DOB.ToString("MM/dd/yyyy")));

                CreateMap<ApplicationStatusViewModel.FieldSurveyTeamViewModel, ApplicationStatusViewModel.FieldSurveyTeamEditViewModel>()
                .ForMember(vm => vm.ProfileImageFile, opt => opt.Ignore())
                .ForMember(vm => vm.Id, opt => opt.MapFrom(a => a.Id));
                CreateMap<ResearchPermitApplication, ApplicationStatusViewModel.EditViewModel>()
                    .ForMember(e => e.PermitApplicationId, opt => opt.MapFrom(a => a.Id))
                    .ForMember(e => e.StudyLocations, opt => opt.Ignore())
                    .ForMember(e => e.IsReissue, opt => opt.Ignore())
                    // .ForMember(e => e.IsRenew, opt => opt.Ignore())
                    .ForMember(e => e.ResearchApplicationId, opt => opt.Ignore())
                    .ForMember(e => e.PermitId, opt => opt.Ignore())
                    //.ForMember(e => e.StudyPeriodStart, opt=> opt.MapFrom(a=> a.StudyPeriodStart.HasValue ? a.StudyPeriodStart.Value : DateTime.Now))
                    //.ForMember(e => e.StudyPeriodEnd, opt => opt.MapFrom(a => a.StudyPeriodEnd.HasValue ? a.StudyPeriodEnd.Value : DateTime.Now))
                    .ForMember(e => e.StudyPeriodStartYear, opt => opt.Ignore())
                    .ForMember(e => e.StudyPeriodStartMonth, opt => opt.Ignore())
                    .ForMember(e => e.StudyPeriodStartDay, opt => opt.Ignore())
                    .ForMember(e => e.StudyPeriodEndYear, opt => opt.Ignore())
                    .ForMember(e => e.StudyPeriodEndMonth, opt => opt.Ignore())
                    .ForMember(e => e.StudyPeriodEndDay, opt => opt.Ignore())
                    .ForMember(e => e.IsIndependentResearcher, opt => opt.Ignore())
                    .ForMember(e => e.ResearcherId, opt => opt.Ignore())
                    .ForMember(e => e.ResearcherName, opt => opt.Ignore())
                    .ForMember(e => e.ResearcherEmail, opt => opt.Ignore())
                    .ForMember(e => e.PhoneNumber, opt => opt.Ignore())

                    .ForMember(e => e.ApplicantPhonePrefixCountryCode, opt => opt.Ignore())
                    .ForMember(e => e.ApplicantPhoneAreaCode, opt => opt.Ignore())
                    .ForMember(e => e.ApplicantPhoneNumber, opt => opt.Ignore())

                    .ForMember(e => e.Address, opt => opt.Ignore())

                    .ForMember(e => e.ApplicantAddressBlockHouseNumber, opt => opt.Ignore())
                    .ForMember(e => e.ApplicantAddressStreetName, opt => opt.Ignore())
                    .ForMember(e => e.ApplicantAddressFloorNumber, opt => opt.Ignore())
                    .ForMember(e => e.ApplicantAddressUnitNumber, opt => opt.Ignore())
                    .ForMember(e => e.ApplicantAddressBuildingName, opt => opt.Ignore())
                    .ForMember(e => e.ApplicantAddressPostalCode, opt => opt.Ignore())

                    .ForMember(e => e.InstitutionName, opt => opt.Ignore())
                    .ForMember(e => e.InstitutionAddress, opt => opt.Ignore())

                    .ForMember(e => e.InstitutionAddressBlockHouseNumber, opt => opt.Ignore())
                    .ForMember(e => e.InstitutionAddressStreetName, opt => opt.Ignore())
                    .ForMember(e => e.InstitutionAddressFloorNumber, opt => opt.Ignore())
                    .ForMember(e => e.InstitutionAddressUnitNumber, opt => opt.Ignore())
                    .ForMember(e => e.InstitutionAddressBuildingName, opt => opt.Ignore())
                    .ForMember(e => e.InstitutionAddressPostalCode, opt => opt.Ignore())

                    .ForMember(e => e.PrincipalInvestigatorEmail, opt => opt.Ignore())
                    .ForMember(e => e.PrincipalInvestigatorName, opt => opt.Ignore())
                    .ForMember(e => e.isPending_eSign, opt => opt.Ignore())
                    .ForMember(e => e.isSubmitted_For_eSign, opt => opt.Ignore())
                    .ForMember(e => e.PrincipalInvestigatorNumber, opt => opt.Ignore())

                    .ForMember(e => e.PIPhonePrefixCountryCode, opt => opt.Ignore())
                    .ForMember(e => e.PIPhoneAreaCode, opt => opt.Ignore())
                    .ForMember(e => e.PIPhoneNumber, opt => opt.Ignore())
                    ;

                //CR3&CR4 Phase1
                CreateMap<ResearchPermitApplication, ApplicationStatusViewModel.AssignedMainApplicantViewModel>()
                    .ForMember(e => e.PermitApplicationId, opt => opt.MapFrom(a => a.Id))
                    .ForMember(e => e.ResearchApplicationId, opt => opt.MapFrom(a => a.ResearchApplicationId))
                    .ForMember(e => e.ResearcherId, opt => opt.MapFrom(a => a.AtResearchApplication.Researcher.Id))
                    .ForMember(e => e.ResearcherPhoto, opt => opt.Ignore())
                    .ForMember(e => e.SelectedMemberId, opt => opt.Ignore())
                    .ForMember(e => e.NewApplicantEmail, opt => opt.Ignore())
                    .ForMember(e => e.NewApplicantPhoneNumber, opt => opt.Ignore())
                    .ForMember(e => e.ApplicantPhonePrefixCountryCodeNew, opt => opt.Ignore())
                    .ForMember(e => e.ApplicantPhoneAreaCodeNew, opt => opt.Ignore())
                    .ForMember(e => e.ApplicantPhoneNumberNew, opt => opt.Ignore())
                    .ForMember(e => e.ResearcherName, opt => opt.MapFrom(a => a.AtResearchApplication.Researcher.PersonName))
                    .ForMember(e => e.ResearcherEmail, opt => opt.MapFrom(a => a.AtResearchApplication.Researcher.Email))
                    .ForMember(e => e.PhoneNumber, opt => opt.MapFrom(a => a.AtResearchApplication.PhoneNumber))
                    .ForMember(e => e.ApplicantPhonePrefixCountryCode, opt => opt.MapFrom(a => a.AtResearchApplication.ApplicantPhonePrefixCountryCode))
                    .ForMember(e => e.ApplicantPhoneAreaCode, opt => opt.MapFrom(a => a.AtResearchApplication.ApplicantPhoneAreaCode))
                    .ForMember(e => e.ApplicantPhoneNumber, opt => opt.MapFrom(a => a.AtResearchApplication.ApplicantPhoneNumber))
                    .ForMember(e => e.AssignedMainApplicantHistJson, opt=>opt.MapFrom(a => a.AtResearchApplication.AssignedMainApplicantHistJson))
                    .ForMember(e => e.Address, opt => opt.Ignore());


                //CreateMap<ApplicationUser, ApiModelUser.UserDataAPI>()
                //    .ForMember(d =>d.
                //    .ForMember(d => d.UserGroupSelected, opt => opt.MapFrom(u => u.Group.Id))
                //    .ForMember(d => d.UserGroupChanged, opt => opt.Ignore());

                CreateMap<ApplicationUser, UserViewModel.DetailViewModel>()
                   .ForMember(d => d.RolesSelected, opt => opt.Ignore())
                   .ForMember(d => d.UserGroupSelected, opt => opt.MapFrom(u => u.Group.Id))
                   .ForMember(d => d.UserGroupChanged, opt => opt.Ignore());

                CreateMap<ApplicationUser, UserViewModel.DetailViewModelLog>()
                   .ForMember(d => d.RolesSelected, opt => opt.MapFrom(u =>u.Roles.Select(t=>t.RoleId)))
                   .ForMember(d => d.UserGroupSelected, opt => opt.MapFrom(u => u.Group.Id));

                CreateMap<BIOMERole, UserViewModel.RoleViewModel>()
                    .ForMember(r => r.Text, opt => opt.MapFrom(r => r.Name.ToProperRoleName()))
                    .ForMember(r => r.Value, opt => opt.MapFrom(r => r.Name));

                CreateMap<MainGroup, UserGroupViewModel.UserGroupMainEditViewModel>()
                    .ForMember(c => c.GroupName, opt => opt.MapFrom(g => g.Name));

                CreateMap<ApplicationUser, ApiModelUser.UserDataAPI>()
                     .ForMember(l => l.uid, opt => opt.MapFrom(u => u.Id))
                   .ForMember(l => l.USERID, opt => opt.MapFrom(u => u.UserName))
                   .ForMember(l => l.EMAIL, opt => opt.MapFrom(u => u.Email))
                   .ForMember(l => l.ACCSTATUS, opt => opt.MapFrom(u => u.AccountStatus == null ? (u.IsActive ? Constants.Account.AccountStatus.ACTIVE : Constants.Account.AccountStatus.DEACTIVATED) : u.AccountStatus))
                   .ForMember(l => l.ENDDATE, opt => opt.Ignore())
                   .ForMember(l => l.LASTDEACTIVATEDATE, opt => opt.MapFrom(u => u.LastDeActivateDate))
                     .ForMember(l => l.LASTLOGINDATE, opt => opt.MapFrom(u => u.DateLastLogin))
                    .ForMember(l => l.LASTSUSPENDDATE, opt => opt.MapFrom(u => u.LastSuspendDate))
                    .ForMember(l => l.ROLECODE, opt => opt.Ignore())
                     .ForMember(l => l.ROLEDESC, opt => opt.Ignore())
                      .ForMember(l => l.SOEID, opt => opt.MapFrom(u => u.UserName))
                     .ForMember(l => l.STAFFNAME, opt => opt.MapFrom(u => u.PersonName))
                    .ForMember(l => l.STARTDATE, opt => opt.Ignore());

                CreateMap<ApplicationUser, UserViewModel.ListViewModel>()
                    .ForMember(l => l.LastLogin, opt => opt.MapFrom(u => u.DateLastLogin))
                    .ForMember(l => l.Status, opt => opt.MapFrom(u => u.IsActive ? "Active" : "Inactive"))
                    .ForMember(l => l.Blacklisted, opt => opt.MapFrom(u => u.Blacklisted ? "Yes" : "No"))
                    .ForMember(l => l.UserGroup, opt => opt.Ignore());

                CreateMap<PagedList<ApplicationUser>, IPagedList<UserViewModel.ListViewModel>>()
                    .ConvertUsing<PagedListConverter<ApplicationUser, UserViewModel.ListViewModel>>();

                CreateMap<ApplicationUser, SiteViewModel.SiteAssignment>()
                    .ForMember(sa => sa.UserId, opt => opt.MapFrom(u => u.Id))
                    .ForMember(sa => sa.UserEmail, opt => opt.MapFrom(u => u.Email))
                    .ForMember(sa => sa.Locations, opt => opt.MapFrom(u => u.SitesManaging));

                CreateMap<GISLocation, SiteViewModel.GISLocationViewModel>();

                CreateMap<ResearchHeaderFooterTemplate, ResearchPermitTemplateViewModel.HeaderFooterTemplateViewModel>()
                    .ForMember(t => t.FooterFile, opt => opt.Ignore())
                    .ForMember(t => t.HeaderFile, opt => opt.Ignore());

                CreateMap<ResearchLetterTemplate, ResearchPermitTemplateViewModel.PermitLetterTemplateViewModel>();
                CreateMap<ResearchTermsTemplate, ResearchPermitTemplateViewModel.TermsConditionTemplateViewModel>()
                    .ForMember(t => t.TermsFile, opt => opt.Ignore());

                CreateMap<ResearchFieldSurveyTeamMember, PermitViewModel.PermitPassCreateViewModel>()
                    .ForMember(p => p.Name, opt => opt.MapFrom(fst => fst.Name))
                    .ForMember(p => p.PassportNumber, opt => opt.MapFrom(fst => fst.Identification))
                    // .ForMember(p => p.DOB, opt => opt.MapFrom(fst => fst.DOB))
                  .ForMember(p => p.DOB, opt => opt.MapFrom(p=> p.DOB.ToString("MM/dd/yyyy")))
                    .ForMember(p => p.PermitNumber, opt => opt.Ignore())
                    .ForMember(p => p.PermitExpiryDate, opt => opt.Ignore())
                    .ForMember(p => p.PermitExpiryDateDisplay, opt => opt.Ignore())
                    .ForMember(p => p.Locality, opt => opt.Ignore())
                    .ForMember(p => p.ProfileImageName, opt => opt.MapFrom(fst => fst.ProfileImageName));

                CreateMap<ApplicationStatusViewModel.FieldSurveyTeamViewModel, PermitViewModel.PermitPassCreateViewModel>()
                    .ForMember(p => p.Name, opt => opt.MapFrom(fst => fst.Name))
                    .ForMember(p => p.PassportNumber, opt => opt.MapFrom(fst => fst.Identification))
                      .ForMember(p => p.DOB, opt => opt.MapFrom(fst => fst.DOB))
                     
                    .ForMember(p => p.PermitNumber, opt => opt.Ignore())
                    .ForMember(p => p.PermitExpiryDate, opt => opt.Ignore())
                    .ForMember(p => p.PermitExpiryDateDisplay, opt => opt.Ignore())
                    .ForMember(p => p.Locality, opt => opt.Ignore())
                    .ForMember(p => p.ProfileImageName, opt => opt.MapFrom(fst => fst.ProfileImageName));

                CreateMap<ApplicationStatusViewModel.FieldSurveyTeamViewModel, PermitViewModel.PermitPassEditViewModel>()
                    .ForMember(p => p.Id, opt => opt.Ignore())
                    .ForMember(p => p.Name, opt => opt.MapFrom(fst => fst.Name))
                    .ForMember(p => p.PassportNumber, opt => opt.MapFrom(fst => fst.Identification))
                     .ForMember(p => p.DOB, opt => opt.MapFrom(fst => fst.DOB))
                     
                    .ForMember(p => p.PermitNumber, opt => opt.Ignore())
                    .ForMember(p => p.PermitExpiryDate, opt => opt.Ignore())
                    .ForMember(p => p.PermitExpiryDateDisplay, opt => opt.Ignore())
                    .ForMember(p => p.Locality, opt => opt.Ignore())
                    .ForMember(p => p.ProfileImageName, opt => opt.MapFrom(fst => fst.ProfileImageName));

                CreateMap<ResearchPermitPass, PermitViewModel.PermitPassViewModel>()
                    .ForMember(p => p.PermitExpiryDateDisplay, opt => opt.Ignore());
                CreateMap<PermitViewModel.PermitPassViewModel, PermitViewModel.PermitPassEditViewModel>();

                CreateMap<ResearchPermitLetter, PermitViewModel.PermitLetterEditViewModel>()
                    .ForMember(l => l.Name, opt => opt.MapFrom(l => l.ReceiverName))
                    .ForMember(l => l.Address, opt => opt.MapFrom(l => l.ReceiverAddress))
                    .ForMember(l => l.Title, opt => opt.MapFrom(l => l.TitleName))
                    .ForMember(l => l.AttachmentFileName, opt => opt.MapFrom(l => l.AttachmentsFileNames.FirstOrDefault()))
                    .ForMember(l => l.DateDisplay, opt => opt.Ignore());

                CreateMap<ResearchPermitLetter, PermitViewModel.PermitLetterViewModel>()
                    .ForMember(l => l.Name, opt => opt.MapFrom(l => l.ReceiverName))
                    .ForMember(l => l.Address, opt => opt.MapFrom(l => l.ReceiverAddress))
                    .ForMember(l => l.Title, opt => opt.MapFrom(l => l.TitleName))
                    .ForMember(l => l.AttachmentFileName, opt => opt.MapFrom(l => l.AttachmentsFileNames.FirstOrDefault()))
                    .ForMember(l => l.DateDisplay, opt => opt.Ignore());

                CreateMap<ApplicationStatusViewModel.StudyLocationAccessViewModel, ApplicationStatusViewModel.EditSitePermissionViewModel>()
                    .ForMember(e => e.LocationName, opt => opt.MapFrom(a => a.Loc_DESC))
                    .ForMember(e => e.ApproveAccess, opt => opt.MapFrom(a => a.Access))
                    .ForMember(e => e.OwnershipDetails, opt => opt.MapFrom(a => a.OwnershipDetails))
                    .ForMember(e => e.SiteManagerNames, opt => opt.Ignore())
                    .ForMember(e => e.SerialNo, opt => opt.Ignore());

                CreateMap<SightingDetail, SightingsViewModel.SightingsInfoAPIViewModel>()
                    .ForMember(i => i.sightingID, opt => opt.MapFrom(s => s.Id))
                    .ForMember(i => i.commonName, opt => opt.MapFrom(s => s.CommonName))
                    .ForMember(i => i.scientificName, opt => opt.MapFrom(s => s.ScientificName))
                    .ForMember(i => i.categoryName, opt => opt.MapFrom(s => s.CategoryName))
                    .ForMember(i => i.description, opt => opt.MapFrom(s => s.Description))
                    .ForMember(i => i.likeCount, opt => opt.MapFrom(s => s.LikeCount))
                    .ForMember(i => i.noSpotted, opt => opt.MapFrom(s => s.NumberSpotted))
                    .ForMember(i => i.approvedBy, opt => opt.MapFrom(s => s.ApprovedById))
                    .ForMember(i => i.submittedBy, opt => opt.MapFrom(s => s.Owner.PersonName))
                    .ForMember(i => i.dateSpotted, opt => opt.MapFrom(s => s.DateSpotted.ToString("yyyy-MM-ddTHH:mm:sszzz")))
                    .ForMember(i => i.dateSubmitted, opt => opt.MapFrom(s => s.CreatedAt.ToString("yyyy-MM-ddTHH:mm:sszzz")))
                    .ForMember(i => i.dateUpdated, opt => opt.MapFrom(s => s.UpdatedAt.ToString("yyyy-MM-ddTHH:mm:sszzz")))
                    .ForMember(i => i.isInappropriate, opt => opt.MapFrom(s => s.IsInappropriate))
                    .ForMember(i => i.isSensitive, opt => opt.MapFrom(s => s.IsSensitive))
                    .ForMember(i => i.isSensitiveByAdmin, opt => opt.MapFrom(s => s.IsAdminSensitive))
                    .ForMember(i => i.isAutoVerified, opt => opt.ResolveUsing<SightingAutoVerifiedResolver>())
                    .ForMember(i => i.isVerifiedByExpert, opt => opt.ResolveUsing<SightingIsExpertVerifiedResolver>())
                    .ForMember(i => i.image, opt => opt.MapFrom(s => s.SightingImage == null || s.SightingImage == "" ? "" : (ConfigurationManager.AppSettings["InternetEmailBaseUrl"] != null ? Url.Combine(ConfigurationManager.AppSettings["InternetEmailBaseUrl"], s.SightingImagePath, s.SightingImage) : Url.Combine(s.SightingImagePath, s.SightingImage))))
                    .ForMember(i => i.featuredBy, opt => opt.MapFrom(s => s.FeaturedBy))
                    .ForMember(i => i.sensitiveBy, opt => opt.MapFrom(s => s.AdminSensitiveBy))
                    .ForMember(i => i.status, opt => opt.MapFrom(s => s.StatusString.ToLower()))
                    .ForMember(i => i.submitFrom, opt => opt.MapFrom(s => ((Enumerations.Sighting.SubmitFromList)s.SubmitFrom).ToString().ToLower()))
                    .ForMember(i => i.taxonomies, opt => opt.MapFrom(s => s.SightingTaxonomy.Select(t => t.CommonName)))
                    .ForMember(i => i.comments, opt => opt.MapFrom(s => s.SightingComments.Select(c => c.CommentString)))
                    .ForMember(i => i.projects, opt => opt.MapFrom(s => s.ProjectSightings.Select(ps => ps.Project.Title)));

                CreateMap<MaintenanceNotice, ApiModelSystem.ResponseNotice>()
                    .ForMember(r => r.NoticeMessage, opt => opt.MapFrom(n => n.noticeMessage))
                    .ForMember(r => r.NoticeTitle, opt => opt.MapFrom(n => n.noticeTitle))
                    .ForMember(r => r.StartFrom, opt => opt.MapFrom(n => n.startFrom.ToString("yyyy-MM-ddTHH:mm:sszzz")))
                    .ForMember(r => r.EndAt, opt => opt.MapFrom(n => n.endAt.ToString("yyyy-MM-ddTHH:mm:sszzz")))
                    .ForMember(r => r.Status, opt => opt.MapFrom(n => n.status));

                CreateMap<ToDoList, ToDoListViewModel.ToDoListUIViewModel>()
                    .ForMember(e => e.TitleInfo, opt => opt.Ignore())
                    .ForMember(e => e.siteManagerName, opt => opt.Ignore());

                //CAM APIs
                CreateMap<ApplicationUser, ViewModels.CamAPI.User.User>()
                   .ForMember(a => a.id, opt => opt.MapFrom(a => a.Id))
                   .ForMember(a => a.externalId, opt => opt.MapFrom(a => a.Id))
                   .ForMember(a => a.meta, opt => opt.MapFrom(a => new ViewModels.CamAPI.Common.Meta { resourceType = "User", created = a.CreatedAt.UtcDateTime, lastModified = a.UpdatedAt.UtcDateTime }))
                   .ForMember(a => a.userName, opt => opt.MapFrom(a => a.UserName))
                   .ForMember(a => a.displayName, opt => opt.MapFrom(a => a.PersonName))
                   .ForMember(a => a.name, opt => opt.MapFrom(a => new ViewModels.CamAPI.Common.Name { familyName = "", givenName = "", formatted = a.PersonName }))
                   .ForMember(a => a.active, opt => opt.MapFrom(a => a.IsActive))
                   .ForMember(a => a.emails, opt => opt.MapFrom(a => new List<ViewModels.CamAPI.Common.Email> { new ViewModels.CamAPI.Common.Email { primary = true, value = a.Email, type = "work" } } ))
                   .ForMember(a => a.profileUrl, opt => opt.UseValue(""))
                   .ForMember(a => a.title, opt => opt.UseValue(""))
                   .ForMember(a => a.userType, opt => opt.UseValue(""))
                   .ForMember(a => a.groups, opt => opt.MapFrom(a => a.Roles.Select(r=> new ViewModels.CamAPI.Common.Resource { value = r.RoleId.ToString(), display = "", sref = "" } ))) //display is RoleName. To assign it after mapping.
                   .ForMember(a => a.enterpriseUser, opt => opt.Ignore())
                   .ForMember(a => a.schemas, opt => opt.Ignore()) 
                   .ForMember(a => a.extensionCamUser, opt => opt.MapFrom(a => new ExtensionCamUser
                   {
                       lastLogin = a.DateLastLogin.UtcDateTime,
                       lastPasswordChanged = a.DateLastChangePasswordActual.UtcDateTime,
                       isPrivileged = a.Roles.Any(r=>r.RoleId==UserRoles.SystemAdminId)
                   }));

                CreateMap<BIOMERole, ViewModels.CamAPI.Group.Group>()
                   .ForMember(a => a.id, opt => opt.MapFrom(a => a.Id))
                   .ForMember(a => a.externalId, opt => opt.MapFrom(a => a.Id))
                   .ForMember(a => a.meta, opt => opt.MapFrom(a => new ViewModels.CamAPI.Common.Meta { resourceType = "Group", created = new DateTime(2016,03,01).ToUniversalTime(), lastModified = new DateTime(2016, 03, 01).ToUniversalTime() })) //created and lastmodified is not available in BIOME DB. Roles are not configurable in BIOME. 01/Mar-2016 is the date when roles are created. 
                   .ForMember(a => a.displayName, opt => opt.MapFrom(a => a.Name))
                   .ForMember(a => a.members, opt => opt.MapFrom(a => a.Users.Select(r => new ViewModels.CamAPI.Common.ResourceType { value = r.UserId.ToString(), type = "User", sref = "" }))) //display is RoleName. To assign it after mapping.
                   .ForMember(a => a.extensionCamGroup, opt => opt.Ignore())
                   .ForMember(a => a.schemas, opt => opt.Ignore());


                // for reissue permit missing member fix
                CreateMap<ApplicationStatusViewModel.FieldSurveyTeamViewModel, ResearchFieldSurveyTeamMember>()
                    .ForMember(dest => dest.Id, opt => opt.Ignore())
                    .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                    .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                    .ForMember(dest => dest.ResearchApplication, opt => opt.Ignore())
                    .ForMember(dest => dest.ResearchPermitApplication, opt => opt.Ignore())
                    .ForMember(dest => dest.AcknowledgeSignTriedCount, opt => opt.MapFrom(src => 0)); // Default to 0


                
                //
            }
        }

        public class ModelProfile : Profile
        {
            protected override void Configure()
            {
                CreateMap<AdminHomepageViewModel.FooterViewModel, PageFooter>()
                    .ForMember(i => i.Id, opt => opt.Ignore())
                    .ForMember(i => i.CreatedAt, opt => opt.Ignore())
                    .ForMember(i => i.UpdatedAt, opt => opt.Ignore());

                CreateMap<AdminHomepageViewModel.ContactViewModel, HomepageContact>()
                    .ForMember(i => i.Id, opt => opt.Ignore())
                    .ForMember(i => i.CreatedAt, opt => opt.Ignore())
                    .ForMember(i => i.UpdatedAt, opt => opt.Ignore());

                CreateMap<AdminHomepageViewModel.SGBioAtlasRowViewModel, HomepageAppInfo>()
                    .ForMember(i => i.Id, opt => opt.Ignore())
                    .ForMember(i => i.LogoImgName, opt => opt.Ignore())
                    .ForMember(i => i.CreatedAt, opt => opt.Ignore())
                    .ForMember(i => i.UpdatedAt, opt => opt.Ignore());

                CreateMap<AdminHomepageViewModel.HighlightUploadViewModel, InternetHighlight>()
                    .ForMember(i => i.Id, opt => opt.Ignore())
                    .ForMember(i => i.CreatedAt, opt => opt.Ignore())
                    .ForMember(i => i.UpdatedAt, opt => opt.Ignore())
                    .ForMember(h => h.BackgroundImgName, opt => opt.Ignore());

                CreateMap<AdminHomepageViewModel.HighlightUploadViewModel, IntranetHighlight>()
                    .ForMember(i => i.Id, opt => opt.Ignore())
                    .ForMember(i => i.CreatedAt, opt => opt.Ignore())
                    .ForMember(i => i.UpdatedAt, opt => opt.Ignore())
                    .ForMember(h => h.BackgroundImgName, opt => opt.Ignore());

                CreateMap<AdminHomepageViewModel.HighlightEditUploadViewModel, InternetHighlight>()
                    .ForMember(i => i.CreatedAt, opt => opt.Ignore())
                    .ForMember(i => i.UpdatedAt, opt => opt.Ignore())
                    .ForMember(h => h.BackgroundImgName, opt => opt.Ignore());

                CreateMap<AdminHomepageViewModel.HighlightEditUploadViewModel, IntranetHighlight>()
                    .ForMember(i => i.CreatedAt, opt => opt.Ignore())
                    .ForMember(i => i.UpdatedAt, opt => opt.Ignore())
                    .ForMember(h => h.BackgroundImgName, opt => opt.Ignore());

                CreateMap<AccountViewModel.ProfileEditViewModel, ApplicationUser>()
                    .ForMember(i => i.DateLastLogin, opt => opt.Ignore())
                    .ForMember(i => i.DateLastChangePassword, opt => opt.Ignore())
                    .ForMember(i => i.DateActivate, opt => opt.Ignore())
                    .ForMember(i => i.Blacklisted, opt => opt.Ignore())
                    .ForMember(i => i.ProfilePicImgName, opt => opt.Ignore())
                    .ForMember(i => i.PreviousUserPasswords, opt => opt.Ignore())
                    .ForMember(i => i.MySightings, opt => opt.Ignore())
                    .ForMember(i => i.CreatedAt, opt => opt.Ignore())
                    .ForMember(i => i.UpdatedAt, opt => opt.Ignore())
                    .ForMember(i => i.Following, opt => opt.Ignore())
                    .ForMember(i => i.Followers, opt => opt.Ignore())
                    .ForMember(i => i.IsActive, opt => opt.Ignore())
                    .ForMember(i => i.AccessFailedCount, opt => opt.Ignore())
                    .ForMember(i => i.Claims, opt => opt.Ignore())
                    .ForMember(i => i.Email, opt => opt.Ignore())
                    .ForMember(i => i.EmailConfirmed, opt => opt.Ignore())
                    .ForMember(i => i.LockoutEnabled, opt => opt.Ignore())
                    .ForMember(i => i.LockoutEndDateUtc, opt => opt.Ignore())
                    .ForMember(i => i.Logins, opt => opt.Ignore())
                    .ForMember(i => i.PasswordHash, opt => opt.Ignore())
                    .ForMember(i => i.PhoneNumberConfirmed, opt => opt.Ignore())
                    .ForMember(i => i.Roles, opt => opt.Ignore())
                    .ForMember(i => i.SecurityStamp, opt => opt.Ignore())
                    .ForMember(i => i.TwoFactorEnabled, opt => opt.Ignore())
                    .ForMember(i => i.UserName, opt => opt.Ignore())
                    .ForMember(i => i.Group, opt => opt.Ignore())
                    .ForMember(i => i.IsFirstLogin, opt => opt.Ignore())
                    .ForMember(i => i.HasAgreedPermitApplicationTnC, opt => opt.Ignore())
                    .ForMember(i => i.HasNewBadge, opt => opt.Ignore())
                    .ForMember(i => i.IsSightingNoNotification, opt => opt.Ignore())
                    .ForMember(i => i.SitesManaging, opt => opt.Ignore())
                    .ForMember(i => i.ForcedInactiveByAdmin, opt => opt.Ignore())
                    .ForMember(i => i.GeneratedPassword, opt => opt.Ignore())
                    .ForMember(i => i.SessionID, opt => opt.Ignore())
                    .ForMember(i => i.AccountStatus, opt => opt.Ignore())
                    .ForMember(i => i.LastDeActivateDate, opt => opt.Ignore())
                    .ForMember(i => i.LastSuspendDate, opt => opt.Ignore())
                    .ForMember(i => i.IV, opt => opt.Ignore())
                    .ForMember(i => i.Key, opt => opt.Ignore())
                    .ForMember(i => i.LastLoginType, opt => opt.Ignore())
                    .ForMember(i => i.FirstName, opt => opt.Ignore())
                    .ForMember(i => i.LastName, opt => opt.Ignore())
                    .ForMember(i => i.IsMobileRegistered, opt => opt.Ignore());

                CreateMap<AccountViewModel.SignUpViewModel, ApplicationUser>()
                    .ForMember(i => i.Organisation, opt => opt.Ignore())
                    .ForMember(i => i.Description, opt => opt.Ignore())
                    .ForMember(i => i.DateActivate, opt => opt.Ignore())
                    .ForMember(i => i.DateLastLogin, opt => opt.Ignore())
                    .ForMember(i => i.DateLastChangePassword, opt => opt.Ignore())
                    .ForMember(i => i.Blacklisted, opt => opt.Ignore())
                    .ForMember(i => i.ProfilePicImgName, opt => opt.Ignore())
                    .ForMember(i => i.PreviousUserPasswords, opt => opt.Ignore())
                    .ForMember(i => i.Group, opt => opt.Ignore())
                    .ForMember(i => i.MySightings, opt => opt.Ignore())
                    .ForMember(i => i.CreatedAt, opt => opt.Ignore())
                    .ForMember(i => i.UpdatedAt, opt => opt.Ignore())
                    .ForMember(i => i.Following, opt => opt.Ignore())
                    .ForMember(i => i.Followers, opt => opt.Ignore())
                    .ForMember(i => i.IsActive, opt => opt.UseValue(true))
                    .ForMember(i => i.Id, opt => opt.Ignore())
                    .ForMember(i => i.Logins, opt => opt.Ignore())
                    .ForMember(i => i.Claims, opt => opt.Ignore())
                    .ForMember(i => i.Roles, opt => opt.Ignore())
                    .ForMember(i => i.AccessFailedCount, opt => opt.Ignore())
                    .ForMember(i => i.LockoutEnabled, opt => opt.Ignore())
                    .ForMember(i => i.LockoutEndDateUtc, opt => opt.Ignore())
                    .ForMember(i => i.TwoFactorEnabled, opt => opt.Ignore())
                    .ForMember(i => i.PhoneNumber, opt => opt.Ignore())
                    .ForMember(i => i.PhoneNumberConfirmed, opt => opt.Ignore())
                    .ForMember(i => i.SecurityStamp, opt => opt.Ignore())
                    .ForMember(i => i.PasswordHash, opt => opt.Ignore())
                    .ForMember(i => i.EmailConfirmed, opt => opt.Ignore())
                    .ForMember(i => i.UserName, opt => opt.MapFrom(r => r.Email))
                    .ForMember(i => i.IsFirstLogin, opt => opt.Ignore())
                    .ForMember(i => i.HasAgreedPermitApplicationTnC, opt => opt.Ignore())
                    .ForMember(i => i.HasNewBadge, opt => opt.Ignore())
                    .ForMember(i => i.IsSightingNoNotification, opt => opt.Ignore())
                    .ForMember(i => i.SitesManaging, opt => opt.Ignore())
                    .ForMember(i => i.ForcedInactiveByAdmin, opt => opt.Ignore())
                    .ForMember(i => i.GeneratedPassword, opt => opt.Ignore())
                    .ForMember(i => i.SessionID, opt => opt.Ignore())
                     .ForMember(i => i.AccountStatus, opt => opt.Ignore())
                    .ForMember(i => i.LastDeActivateDate, opt => opt.Ignore())
                    .ForMember(i => i.LastSuspendDate, opt => opt.Ignore())
                    .ForMember(i => i.IV, opt => opt.Ignore())
                    .ForMember(i => i.Key, opt => opt.Ignore())
                    .ForMember(i => i.LastLoginType, opt => opt.Ignore())
                    .ForMember(i => i.LastLoginType, opt => opt.Ignore())
                    .ForMember(i => i.IsMobileRegistered, opt => opt.UseValue(false));


                CreateMap<AccountViewModel.SignUpViewModel, AccountViewModel.UserViewModelLog>();

                CreateMap<AccountViewModel.ActivateAccountExtViewModel, ApplicationUser>()
                    .ForMember(i => i.Organisation, opt => opt.Ignore())
                    .ForMember(i => i.Description, opt => opt.Ignore())
                    .ForMember(i => i.DateActivate, opt => opt.Ignore())
                    .ForMember(i => i.DateLastLogin, opt => opt.Ignore())
                    .ForMember(i => i.DateLastChangePassword, opt => opt.Ignore())
                    .ForMember(i => i.Blacklisted, opt => opt.Ignore())
                    .ForMember(i => i.ProfilePicImgName, opt => opt.Ignore())
                    .ForMember(i => i.PreviousUserPasswords, opt => opt.Ignore())
                    .ForMember(i => i.Group, opt => opt.Ignore())
                    .ForMember(i => i.MySightings, opt => opt.Ignore())
                    .ForMember(i => i.CreatedAt, opt => opt.Ignore())
                    .ForMember(i => i.UpdatedAt, opt => opt.Ignore())
                    .ForMember(i => i.Following, opt => opt.Ignore())
                    .ForMember(i => i.Followers, opt => opt.Ignore())
                    .ForMember(i => i.IsActive, opt => opt.UseValue(true))
                    .ForMember(i => i.Id, opt => opt.Ignore())
                    .ForMember(i => i.Logins, opt => opt.Ignore())
                    .ForMember(i => i.Claims, opt => opt.Ignore())
                    .ForMember(i => i.Roles, opt => opt.Ignore())
                    .ForMember(i => i.AccessFailedCount, opt => opt.Ignore())
                    .ForMember(i => i.LockoutEnabled, opt => opt.Ignore())
                    .ForMember(i => i.LockoutEndDateUtc, opt => opt.Ignore())
                    .ForMember(i => i.TwoFactorEnabled, opt => opt.Ignore())
                    .ForMember(i => i.PhoneNumber, opt => opt.Ignore())
                    .ForMember(i => i.PhoneNumberConfirmed, opt => opt.Ignore())
                    .ForMember(i => i.SecurityStamp, opt => opt.Ignore())
                    .ForMember(i => i.PasswordHash, opt => opt.Ignore())
                    .ForMember(i => i.EmailConfirmed, opt => opt.UseValue(true))
                    .ForMember(i => i.UserName, opt => opt.MapFrom(r => r.Email))
                    .ForMember(i => i.IsFirstLogin, opt => opt.Ignore())
                    .ForMember(i => i.HasAgreedPermitApplicationTnC, opt => opt.Ignore())
                    .ForMember(i => i.HasNewBadge, opt => opt.Ignore())
                    .ForMember(i => i.IsSightingNoNotification, opt => opt.Ignore())
                    .ForMember(i => i.SitesManaging, opt => opt.Ignore())
                    .ForMember(i => i.ForcedInactiveByAdmin, opt => opt.Ignore())
                    .ForMember(i => i.GeneratedPassword, opt => opt.Ignore())
                    .ForMember(i => i.SessionID, opt => opt.Ignore())
                    .ForMember(i => i.AccountStatus, opt => opt.Ignore())
                    .ForMember(i => i.LastDeActivateDate, opt => opt.Ignore())
                    .ForMember(i => i.LastSuspendDate, opt => opt.Ignore())
                      .ForMember(i => i.IV, opt => opt.Ignore())
                        .ForMember(i => i.Key, opt => opt.Ignore())
                        .ForMember(i => i.LastLoginType, opt => opt.Ignore())
                    .ForMember(i => i.IsMobileRegistered, opt => opt.UseValue(false));

                CreateMap<SystemParametersViewModel, SystemParameters>()
                    .ForMember(m => m.SightingsMinVotesQualify, opt => opt.Ignore())
                    .ForMember(m => m.SightingsMinPercentAgree, opt => opt.Ignore())
                    .ForMember(m => m.SightingsFeaturedLikeNum, opt => opt.Ignore())
                    .ForMember(m => m.SightingsFeaturedPeriodDays, opt => opt.Ignore())
                    .ForMember(m => m.CreatedAt, opt => opt.Ignore())
                    .ForMember(m => m.UpdatedAt, opt => opt.Ignore());

                CreateMap<MaintenanceNoticeViewModel, MaintenanceNotice>()
                    .ForMember(m => m.CreatedAt, opt => opt.Ignore())
                    .ForMember(m => m.UpdatedAt, opt => opt.Ignore());

                /**.ForMember(m => m.status, opt => opt.Ignore())
                .ForMember(m => m.startFrom, opt => opt.Ignore())
                .ForMember(m => m.endAt, opt => opt.Ignore())
                .ForMember(m => m.noticeTitle, opt => opt.Ignore())
                .ForMember(m => m.noticeMessage, opt => opt.Ignore()
            **/

                CreateMap<SightingDetail, GeoJSON.Net.Geometry.IGeometryObject>()
                    .ConvertUsing((c, s) =>
                        new GeoJSON.Net.Geometry.Point(new GeoJSON.Net.Geometry.GeographicPosition(s.Latitude, s.Longitude, null)));

                CreateMap<SightingDetail, Dictionary<string, object>>()
                    .ConvertUsing((c, s) =>
                    new Dictionary<string, object>()
                        {
                            { "id", s.Id },
                            { "title", s.CommonName },
                            { "scientific_name", s.ScientificName },
                            { "image", s.GetCroppedImage() },
                            { "quantity", s.NumberSpotted },
                            { "category", s.CategoryName },
                            { "date_spotted", s.DateSpotted },
                            { "recorded_by", s.Owner.PersonName }
                        }
                    );

                CreateMap<SightingDetail, GeoJSON.Net.Feature.Feature>()
                    .ForCtorParam("geometry", opt => opt.MapFrom(s => s))
                    .ForCtorParam("properties", opt => opt.MapFrom(s => s))
                    .ForMember(f => f.Geometry, opt => opt.Ignore())
                    .ForMember(f => f.BoundingBoxes, opt => opt.Ignore())
                    .ForMember(f => f.CRS, opt => opt.Ignore())
                    .ForMember(f => f.Id, opt => opt.Ignore())
                    .ForMember(f => f.Properties, opt => opt.Ignore())
                    .ForMember(f => f.Type, opt => opt.Ignore());

                CreateMap<ApiModelUser.SignUp, ApplicationUser>()
                    .ForMember(i => i.Organisation, opt => opt.Ignore())
                    .ForMember(i => i.Description, opt => opt.Ignore())
                    .ForMember(i => i.DateActivate, opt => opt.Ignore())
                    .ForMember(i => i.DateLastLogin, opt => opt.Ignore())
                    .ForMember(i => i.DateLastChangePassword, opt => opt.Ignore())
                    .ForMember(i => i.Blacklisted, opt => opt.Ignore())
                    .ForMember(i => i.ProfilePicImgName, opt => opt.Ignore())
                    .ForMember(i => i.PreviousUserPasswords, opt => opt.Ignore())
                    .ForMember(i => i.Group, opt => opt.Ignore())
                    .ForMember(i => i.MySightings, opt => opt.Ignore())
                    .ForMember(i => i.CreatedAt, opt => opt.Ignore())
                    .ForMember(i => i.UpdatedAt, opt => opt.Ignore())
                    .ForMember(i => i.Following, opt => opt.Ignore())
                    .ForMember(i => i.Followers, opt => opt.Ignore())
                    .ForMember(i => i.IsActive, opt => opt.UseValue(true))
                    .ForMember(i => i.Id, opt => opt.Ignore())
                    .ForMember(i => i.Logins, opt => opt.Ignore())
                    .ForMember(i => i.Claims, opt => opt.Ignore())
                    .ForMember(i => i.Roles, opt => opt.Ignore())
                    .ForMember(i => i.AccessFailedCount, opt => opt.Ignore())
                    .ForMember(i => i.LockoutEnabled, opt => opt.Ignore())
                    .ForMember(i => i.LockoutEndDateUtc, opt => opt.Ignore())
                    .ForMember(i => i.TwoFactorEnabled, opt => opt.Ignore())
                    .ForMember(i => i.PhoneNumber, opt => opt.MapFrom(s => s.MobileNumber))
                    .ForMember(i => i.PhoneNumberConfirmed, opt => opt.Ignore())
                    .ForMember(i => i.SecurityStamp, opt => opt.Ignore())
                    .ForMember(i => i.PasswordHash, opt => opt.Ignore())
                    .ForMember(i => i.EmailConfirmed, opt => opt.Ignore())
                    .ForMember(i => i.UserName, opt => opt.MapFrom(r => r.Email))
                    .ForMember(i => i.IsFirstLogin, opt => opt.Ignore())
                    .ForMember(i => i.HasAgreedPermitApplicationTnC, opt => opt.Ignore())
                    .ForMember(i => i.HasNewBadge, opt => opt.Ignore())
                    .ForMember(i => i.IsSightingNoNotification, opt => opt.Ignore())
                    .ForMember(i => i.SitesManaging, opt => opt.Ignore())
                    .ForMember(i => i.ForcedInactiveByAdmin, opt => opt.Ignore())
                    .ForMember(i => i.GeneratedPassword, opt => opt.Ignore())
                    .ForMember(i => i.SessionID, opt => opt.Ignore())
                    .ForMember(i => i.AccountStatus, opt => opt.Ignore())
                    .ForMember(i => i.LastSuspendDate, opt => opt.Ignore())
                    .ForMember(i => i.LastDeActivateDate, opt => opt.Ignore())
                    .ForMember(i => i.IV, opt => opt.Ignore())
                    .ForMember(i => i.Key, opt => opt.Ignore())
                    .ForMember(i => i.LastLoginType, opt => opt.Ignore())
                    .ForMember(i => i.IsMobileRegistered, opt => opt.UseValue(true))
                    .ForMember(i => i.PersonName, opt => opt.MapFrom(s => s.FirstName.Trim() + " " + s.LastName.Trim()));

                CreateMap<PermitViewModel.FieldSurveyTeamCreateViewModel, ResearchFieldSurveyTeamMember>()
                    .ForMember(a => a.Id, opt => opt.Ignore())
                    .ForMember(a => a.CreatedAt, opt => opt.Ignore())
                    .ForMember(a => a.UpdatedAt, opt => opt.Ignore())
                      .ForMember(a => a.DOB, opt => opt.MapFrom(x => DateTimeOffset.ParseExact(x.DOB, "dd/MM/yyyy", new CultureInfo("en-US")).DateTime))
                    .ForMember(a => a.ResearchApplication, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignStatus, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignDate, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkCode, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkSentOn, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkExpiredOn, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignTriedCount, opt => opt.Ignore())
                    .ForMember(m => m.PreviousMemberID, opt => opt.Ignore())
                    .ForMember(a => a.ResearchPermitApplication, opt => opt.Ignore());

                CreateMap<PermitViewModel.GISLocationViewModel, GISLocation>()
                    .ForMember(a => a.Id, opt => opt.Ignore())
                    .ForMember(a => a.CreatedAt, opt => opt.Ignore())
                    .ForMember(a => a.UpdatedAt, opt => opt.Ignore())
                    .ForMember(a => a.SiteManagers, opt => opt.Ignore())
                    .ForMember(l => l.ResearchApplications, opt => opt.Ignore())
                    .ForMember(l => l.ResearchPermitApplications, opt => opt.Ignore())
                    .ForMember(a => a.Email, opt => opt.Ignore())
                    .ForMember(a => a.FeatureID, opt => opt.Ignore())
                    .ForMember(l => l.StudyLocationAccesses, opt => opt.Ignore());

                CreateMap<PermitViewModel.FieldSurveyTeamCreateViewModel, ResearchFieldSurveyTeamMemberDraft>()
                    .ForMember(a => a.Id, opt => opt.Ignore())
                    .ForMember(a => a.CreatedAt, opt => opt.Ignore())
                    .ForMember(a => a.UpdatedAt, opt => opt.Ignore())
                       .ForMember(a => a.DOB, opt => opt.MapFrom(x => DateTimeOffset.ParseExact(x.DOB, "dd/MM/yyyy", new CultureInfo("en-US")).DateTime))
                    .ForMember(a => a.ResearchApplicationDraft_Id, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignStatus, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignDate, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkCode, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkSentOn, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkExpiredOn, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignTriedCount, opt => opt.Ignore())
                    .ForMember(a => a.ResearchApplicationDraft, opt => opt.Ignore());

                CreateMap<PermitViewModel.ApplicationFormSubmitViewModel, ResearchApplicationDraft>()
                    .ForMember(a => a.Id, opt => opt.Ignore())
                    .ForMember(a => a.CreatedAt, opt => opt.Ignore())
                    .ForMember(a => a.UpdatedAt, opt => opt.Ignore())
                    .ForMember(a => a.Researcher, opt => opt.Ignore())
                    .ForMember(a => a.StudySubjectIdsStr, opt => opt.Ignore())
                    .ForMember(a => a.ResearchPermitApplicationId, opt => opt.Ignore())
                    .ForMember(a => a.PresentFindingsDateStart, opt => opt.ResolveUsing<ResearchPresentFindingsDateStartResolver>())
                    .ForMember(a => a.PresentFindingsDateEnd, opt => opt.ResolveUsing<ResearchPresentFindingsDateEndResolver>())
                    .ForMember(a => a.NParksContactPersonEmailWithoutDomain, opt => opt.ResolveUsing<ResearchContactPersonResolver>());

                CreateMap<PermitViewModel.ApplicationFormSubmitDraftViewModel, ResearchApplicationDraft>()
                    .ForMember(a => a.Id, opt => opt.Ignore())
                    .ForMember(a => a.CreatedAt, opt => opt.Ignore())
                    .ForMember(a => a.UpdatedAt, opt => opt.Ignore())
                    .ForMember(a => a.Researcher, opt => opt.Ignore())
                    .ForMember(a => a.StudySubjectIdsStr, opt => opt.Ignore())
                    .ForMember(a => a.ResearchPermitApplicationId, opt => opt.Ignore())
                    .ForMember(a => a.FieldSurveyTeamMembers, opt => opt.Ignore())
                    .ForMember(a => a.PresentFindingsDateStart, opt => opt.ResolveUsing<ResearchPresentFindingsDateStartResolver>())
                    .ForMember(a => a.PresentFindingsDateEnd, opt => opt.ResolveUsing<ResearchPresentFindingsDateEndResolver>())
                    .ForMember(a => a.NParksContactPersonEmailWithoutDomain, opt => opt.ResolveUsing<ResearchContactPersonResolver>());

                CreateMap<PermitViewModel.FieldSurveyTeamEditViewModel, ResearchFieldSurveyTeamMemberDraft>()
                    .ForMember(a => a.Id, opt => opt.Ignore())
                   // .ForMember(t => t.StartDate, opt => opt.MapFrom(p => DateTimeOffset.ParseExact(p.StartDate, "dd/MM/yyyy", new CultureInfo("en-US")).DateTime))
                     .ForMember(a => a.DOB, opt => opt.MapFrom(x=> DateTimeOffset.ParseExact(x.DOB, "dd/MM/yyyy", new CultureInfo("en-US")).DateTime))
                    .ForMember(a => a.CreatedAt, opt => opt.Ignore())
                    .ForMember(a => a.UpdatedAt, opt => opt.Ignore())
                    .ForMember(a => a.ResearchApplicationDraft_Id, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignStatus, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignDate, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkCode, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkSentOn, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkExpiredOn, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignTriedCount, opt => opt.Ignore())
                    .ForMember(a => a.ResearchApplicationDraft, opt => opt.Ignore());


                CreateMap<ResearchFieldSurveyTeamMemberDraft, PermitViewModel.FieldSurveyTeamView_NewSubmission_eSign_ModelDraftLog>();
                CreateMap<ResearchFieldSurveyTeamMember, PermitViewModel.FieldSurveyTeamView_AmendRenewal_eSign_ModelDraftLog>()
                    .ForMember(a => a.ResearchPermitApplication_Id, opt => opt.MapFrom(x=>x.ResearchPermitApplication.Id));

                //CreateMap<PermitViewModel.ApplicationFormSubmitViewModel, ResearchApplicationDraft>()
                //    .ForMember(a => a.Id, opt => opt.Ignore())
                //    .ForMember(a => a.CreatedAt, opt => opt.Ignore())
                //    .ForMember(a => a.UpdatedAt, opt => opt.Ignore())
                //    .ForMember(a => a.Researcher, opt => opt.Ignore())
                //    .ForMember(a => a.StudySubjectIdsStr, opt => opt.Ignore())
                //    .ForMember(a => a.ResearchPermitApplicationId, opt => opt.Ignore())
                //    .ForMember(a => a.StudyLocationsDrawn, opt => opt.MapFrom(f => DbGeometry.FromText(f.StudyLocationsDrawn)))
                //    .ForMember(a => a.PresentFindingsDateStart, opt => opt.ResolveUsing<ResearchPresentFindingsDateStartResolver>())
                //    .ForMember(a => a.PresentFindingsDateEnd, opt => opt.ResolveUsing<ResearchPresentFindingsDateEndResolver>())
                //    .ForMember(a => a.NParksContactPersonEmailWithoutDomain, opt => opt.ResolveUsing<ResearchContactPersonResolver>());

                CreateMap<PermitViewModel.ApplicationFormSubmitViewModel, ResearchApplication>()
                    .ForMember(a => a.Id, opt => opt.Ignore())
                    .ForMember(a => a.CreatedAt, opt => opt.Ignore())
                    .ForMember(a => a.UpdatedAt, opt => opt.Ignore())
                    .ForMember(a => a.Forums, opt => opt.Ignore())
                    .ForMember(a => a.Inboxes, opt => opt.Ignore())
                    .ForMember(a => a.PermitApplications, opt => opt.Ignore())
                    .ForMember(a => a.Reports, opt => opt.Ignore())
                    .ForMember(a => a.Researcher, opt => opt.Ignore())
                    .ForMember(a => a.ResearchType, opt => opt.Ignore())
                    .ForMember(a => a.StudySubjects, opt => opt.Ignore())
                    .ForMember(a => a.Habitat, opt => opt.Ignore())
                    .ForMember(a => a.StudyLocationAccesses, opt => opt.Ignore())
                    .ForMember(a => a.PassCombinedFileName, opt => opt.Ignore())
                    .ForMember(a => a.MiscFileToken, opt => opt.Ignore())
                    .ForMember(a => a.IndemnityFormFileToken, opt => opt.Ignore())
                    .ForMember(a => a.StudyLocationAccesses, opt => opt.Ignore())
                    .ForMember(a => a.AssignedMainApplicantHistJson, opt => opt.Ignore())
                    .ForMember(a => a.Institutional, opt => opt.ResolveUsing<ResearchInstitutionalResolver>())
                    .ForMember(a => a.PresentFindingsDateStart, opt => opt.ResolveUsing<ResearchPresentFindingsDateStartResolver>())
                    .ForMember(a => a.PresentFindingsDateEnd, opt => opt.ResolveUsing<ResearchPresentFindingsDateEndResolver>())
                    .ForMember(a => a.NParksContactPersonEmailWithoutDomain, opt => opt.ResolveUsing<ResearchContactPersonResolver>());

                CreateMap<PermitViewModel.ApplicationFormSubmitDraftViewModel, ResearchApplication>()
                    .ForMember(a => a.Id, opt => opt.Ignore())
                    .ForMember(a => a.CreatedAt, opt => opt.Ignore())
                    .ForMember(a => a.UpdatedAt, opt => opt.Ignore())
                    .ForMember(a => a.Forums, opt => opt.Ignore())
                    .ForMember(a => a.Inboxes, opt => opt.Ignore())
                    .ForMember(a => a.PermitApplications, opt => opt.Ignore())
                    .ForMember(a => a.Reports, opt => opt.Ignore())
                    .ForMember(a => a.Researcher, opt => opt.Ignore())
                    .ForMember(a => a.ResearchType, opt => opt.Ignore())
                    .ForMember(a => a.StudySubjects, opt => opt.Ignore())
                    .ForMember(a => a.Habitat, opt => opt.Ignore())
                    .ForMember(a => a.StudyLocationAccesses, opt => opt.Ignore())
                    .ForMember(a => a.PassCombinedFileName, opt => opt.Ignore())
                    .ForMember(a => a.IndemnityFormFileToken, opt => opt.Ignore())
                    .ForMember(a => a.MiscFileToken, opt => opt.Ignore())
                    .ForMember(a => a.StudyLocationAccesses, opt => opt.Ignore())
                    .ForMember(a => a.AssignedMainApplicantHistJson, opt => opt.Ignore())
                    .ForMember(a => a.Institutional, opt => opt.ResolveUsing<ResearchInstitutionalResolver>())
                    .ForMember(a => a.PresentFindingsDateStart, opt => opt.ResolveUsing<ResearchPresentFindingsDateStartResolver>())
                    .ForMember(a => a.PresentFindingsDateEnd, opt => opt.ResolveUsing<ResearchPresentFindingsDateEndResolver>())
                    .ForMember(a => a.NParksContactPersonEmailWithoutDomain, opt => opt.ResolveUsing<ResearchContactPersonResolver>());

                CreateMap<PermitViewModel.FieldSurveyTeamEditViewModel, ResearchFieldSurveyTeamMember>()
                    .ForMember(m => m.Id, opt => opt.Ignore())
                    .ForMember(m => m.CreatedAt, opt => opt.Ignore())
                    .ForMember(m => m.UpdatedAt, opt => opt.Ignore())
                      .ForMember(a => a.DOB, opt => opt.MapFrom(x => DateTimeOffset.ParseExact(x.DOB, "dd/MM/yyyy", new CultureInfo("en-US")).DateTime))
                    .ForMember(m => m.ResearchApplication, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignStatus, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignDate, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkCode, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkSentOn, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkExpiredOn, opt => opt.Ignore())
                    .ForMember(m => m.PreviousMemberID, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignTriedCount, opt => opt.Ignore())
                    .ForMember(m => m.ResearchPermitApplication, opt => opt.Ignore());

                CreateMap<ApplicationStatusViewModel.FieldSurveyTeamEditViewModel, ResearchFieldSurveyTeamMember>()
                    .ForMember(m => m.Id, opt => opt.Ignore())
                    .ForMember(m => m.CreatedAt, opt => opt.Ignore())
                    .ForMember(m => m.UpdatedAt, opt => opt.Ignore())
                      .ForMember(a => a.DOB, opt => opt.MapFrom(x => DateTimeOffset.ParseExact(x.DOB, "dd/MM/yyyy", new CultureInfo("en-US")).DateTime))
                    .ForMember(m => m.ResearchApplication, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignStatus, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignDate, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkCode, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkSentOn, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignLinkExpiredOn, opt => opt.Ignore())
                    .ForMember(m => m.AcknowledgeSignTriedCount, opt => opt.Ignore())
                    .ForMember(m => m.PreviousMemberID, opt => opt.Ignore())
                    .ForMember(m => m.ResearchPermitApplication, opt => opt.Ignore());

                CreateMap<ApplicationStatusViewModel.FieldSurveyTeamEditViewModel, ApplicationStatusViewModel.FieldSurveyTeamViewModel>()
                .ForMember(m => m.ProfileImagePath, opt => opt.Ignore())
                .ForMember(m => m.Id, opt => opt.MapFrom(a => a.Id));

                //.ForMember(m => m.CreatedAt, opt => opt.Ignore())
                //.ForMember(m => m.UpdatedAt, opt => opt.Ignore())
                //.ForMember(m => m.ResearchApplication, opt => opt.Ignore())
                // .ForMember(m => m., opt => opt.Ignore());

                CreateMap<UserViewModel.DetailViewModel, ApplicationUser>()
                    .ForMember(u => u.AccessFailedCount, opt => opt.Ignore())
                    .ForMember(u => u.CreatedAt, opt => opt.Ignore())
                    .ForMember(u => u.DateActivate, opt => opt.Ignore())
                    .ForMember(u => u.DateLastChangePassword, opt => opt.Ignore())
                    .ForMember(u => u.DateLastLogin, opt => opt.Ignore())
                    .ForMember(u => u.Description, opt => opt.Ignore())
                    .ForMember(u => u.Email, opt => opt.Ignore())
                    .ForMember(u => u.EmailConfirmed, opt => opt.Ignore())
                    .ForMember(u => u.FirstName, opt => opt.Ignore())
                    .ForMember(u => u.Followers, opt => opt.Ignore())
                    .ForMember(u => u.Following, opt => opt.Ignore())
                    .ForMember(u => u.Group, opt => opt.Ignore())
                    .ForMember(u => u.HasAgreedPermitApplicationTnC, opt => opt.Ignore())
                    .ForMember(u => u.HasNewBadge, opt => opt.Ignore())
                    .ForMember(u => u.Id, opt => opt.Ignore())
                    .ForMember(u => u.IsFirstLogin, opt => opt.Ignore())
                    .ForMember(u => u.IsSightingNoNotification, opt => opt.Ignore())
                    .ForMember(u => u.LastName, opt => opt.Ignore())
                    .ForMember(u => u.LockoutEnabled, opt => opt.Ignore())
                    .ForMember(u => u.LockoutEndDateUtc, opt => opt.Ignore())
                    .ForMember(u => u.MySightings, opt => opt.Ignore())
                    .ForMember(u => u.Organisation, opt => opt.Ignore())
                    .ForMember(u => u.PasswordHash, opt => opt.Ignore())
                    .ForMember(u => u.PersonName, opt => opt.Ignore())
                    .ForMember(u => u.PhoneNumber, opt => opt.Ignore())
                    .ForMember(u => u.PhoneNumberConfirmed, opt => opt.Ignore())
                    .ForMember(u => u.PreviousUserPasswords, opt => opt.Ignore())
                    .ForMember(u => u.ProfilePicImgName, opt => opt.Ignore())
                    .ForMember(u => u.Roles, opt => opt.Ignore())
                    .ForMember(u => u.SecurityStamp, opt => opt.Ignore())
                    .ForMember(u => u.SitesManaging, opt => opt.Ignore())
                    .ForMember(u => u.TwoFactorEnabled, opt => opt.Ignore())
                    .ForMember(u => u.UpdatedAt, opt => opt.Ignore())
                    .ForMember(u => u.UserName, opt => opt.Ignore())
                    .ForMember(u => u.Claims, opt => opt.Ignore())
                    .ForMember(u => u.Logins, opt => opt.Ignore())
                    .ForMember(u => u.ForcedInactiveByAdmin, opt => opt.MapFrom(d => !d.IsActive ? true : false))
                    .ForMember(i => i.GeneratedPassword, opt => opt.Ignore())
                    .ForMember(i => i.SessionID, opt => opt.Ignore())
                     .ForMember(i => i.AccountStatus, opt => opt.Ignore())
                    .ForMember(i => i.LastDeActivateDate, opt => opt.Ignore())
                    .ForMember(i => i.LastSuspendDate, opt => opt.Ignore())
                       .ForMember(i => i.IV, opt => opt.Ignore())
                          .ForMember(i => i.Key, opt => opt.Ignore())
                          .ForMember(i => i.LastLoginType, opt => opt.Ignore())
                    .ForMember(i => i.IsMobileRegistered, opt => opt.Ignore());

                CreateMap<UserGroupViewModel.UserGroupMainCreateViewModel, MainGroup>()
                    .ForMember(m => m.Id, opt => opt.Ignore())
                    .ForMember(m => m.CreatedAt, opt => opt.Ignore())
                    .ForMember(m => m.UpdatedAt, opt => opt.Ignore())
                    .ForMember(m => m.IsDeleted, opt => opt.Ignore())
                    .ForMember(m => m.ResourceFilePermission, opt => opt.Ignore())
                    .ForMember(m => m.ResourceMetaData, opt => opt.Ignore())
                    .ForMember(m => m.SubGroups, opt => opt.Ignore())
                    .ForMember(m => m.Users, opt => opt.Ignore())
                    .ForMember(m => m.Name, opt => opt.MapFrom(c => c.GroupName));

                CreateMap<UserGroupViewModel.UserGroupSubCreateViewModel, SubGroup>()
                    .ForMember(m => m.Id, opt => opt.Ignore())
                    .ForMember(m => m.CreatedAt, opt => opt.Ignore())
                    .ForMember(m => m.UpdatedAt, opt => opt.Ignore())
                    .ForMember(m => m.IsDeleted, opt => opt.Ignore())
                    .ForMember(m => m.ResourceFilePermission, opt => opt.Ignore())
                    .ForMember(m => m.ResourceMetaData, opt => opt.Ignore())
                    .ForMember(m => m.SubSubGroups, opt => opt.Ignore())
                    .ForMember(m => m.Users, opt => opt.Ignore())
                    .ForMember(m => m.MainGroupId, opt => opt.Ignore())
                    .ForMember(m => m.ParentGroup, opt => opt.Ignore())
                    .ForMember(m => m.Name, opt => opt.MapFrom(c => c.GroupName));

                CreateMap<UserGroupViewModel.UserGroupSubSubCreateViewModel, SubSubGroup>()
                    .ForMember(m => m.Id, opt => opt.Ignore())
                    .ForMember(m => m.CreatedAt, opt => opt.Ignore())
                    .ForMember(m => m.UpdatedAt, opt => opt.Ignore())
                    .ForMember(m => m.IsDeleted, opt => opt.Ignore())
                    .ForMember(m => m.ResourceFilePermission, opt => opt.Ignore())
                    .ForMember(m => m.ResourceMetaData, opt => opt.Ignore())
                    .ForMember(m => m.Users, opt => opt.Ignore())
                    .ForMember(m => m.SubGroupId, opt => opt.Ignore())
                    .ForMember(m => m.ParentGroup, opt => opt.Ignore())
                    .ForMember(m => m.Name, opt => opt.MapFrom(c => c.GroupName));

                CreateMap<SiteViewModel.GISLocationViewModel, GISLocation>()
                    .ForMember(l => l.Id, opt => opt.Ignore())
                    .ForMember(l => l.CreatedAt, opt => opt.Ignore())
                    .ForMember(l => l.UpdatedAt, opt => opt.Ignore())
                    .ForMember(l => l.SiteManagers, opt => opt.Ignore())
                    .ForMember(l => l.FeatureID, opt => opt.Ignore())
                    .ForMember(l => l.ResearchApplications, opt => opt.Ignore())
                    .ForMember(l => l.ResearchPermitApplications, opt => opt.Ignore())
                    .ForMember(l => l.StudyLocationAccesses, opt => opt.Ignore());

                CreateMap<ResearchPermitTemplateViewModel.HeaderFooterTemplateViewModel, ResearchHeaderFooterTemplate>()
                    .ForMember(t => t.Id, opt => opt.Ignore())
                    .ForMember(t => t.CreatedAt, opt => opt.Ignore())
                    .ForMember(t => t.UpdatedAt, opt => opt.Ignore())
                    .ForMember(t => t.IsDeleted, opt => opt.UseValue(false))
                    .ForMember(t => t.FooterFileName, opt => opt.MapFrom(t => t.FooterFileName))
                    .ForMember(t => t.HeaderFileName, opt => opt.MapFrom(t => t.HeaderFileName));

                CreateMap<ResearchPermitTemplateViewModel.PermitLetterTemplateViewModel, ResearchLetterTemplate>()
                    .ForMember(t => t.Id, opt => opt.Ignore())
                    .ForMember(t => t.CreatedAt, opt => opt.Ignore())
                    .ForMember(t => t.UpdatedAt, opt => opt.Ignore())
                    .ForMember(t => t.IsDeleted, opt => opt.UseValue(false));
                CreateMap<ResearchPermitTemplateViewModel.TermsConditionTemplateViewModel, ResearchTermsTemplate>()
                    .ForMember(t => t.Id, opt => opt.Ignore())
                    .ForMember(t => t.CreatedAt, opt => opt.Ignore())
                    .ForMember(t => t.UpdatedAt, opt => opt.Ignore())
                    .ForMember(t => t.IsDeleted, opt => opt.UseValue(false))
                    .ForMember(t => t.TermsFileName, opt => opt.MapFrom(t => t.TermsFileName));

                CreateMap<PermitViewModel.PermitPassCreateViewModel, ResearchPermitPass>()
                    .ForMember(t => t.Id, opt => opt.Ignore())
                    .ForMember(d => d.DOB, opt => opt.MapFrom(d => d.DOB))
                      //.ForMember(t => t.DOB, opt => opt.MapFrom(u => u.DOB.HasValue? u.DOB.Value.ToString("dd/MM/yyyy"):""))
                    .ForMember(t => t.CreatedAt, opt => opt.Ignore())
                    .ForMember(t => t.UpdatedAt, opt => opt.Ignore())
                    .ForMember(t => t.AtResearchPermitApplication, opt => opt.Ignore())
                    .ForMember(t => t.Status, opt => opt.Ignore())
                    .ForMember(t => t.PassFilename, opt => opt.Ignore());

                CreateMap<PermitViewModel.PermitPassEditViewModel, ResearchPermitPass>()
                    .ForMember(t => t.Id, opt => opt.Ignore())
                     .ForMember(t => t.DOB, opt => opt.MapFrom(u => u.DOB))
                    .ForMember(t => t.CreatedAt, opt => opt.Ignore())
                    .ForMember(t => t.UpdatedAt, opt => opt.Ignore())
                    .ForMember(t => t.AtResearchPermitApplication, opt => opt.Ignore())
                    .ForMember(t => t.Status, opt => opt.Ignore())
                    .ForMember(t => t.PassFilename, opt => opt.Ignore());

                CreateMap<PermitViewModel.PermitLetterCreateViewModel, ResearchPermitLetter>()
                    .ForMember(t => t.Id, opt => opt.Ignore())
                    .ForMember(t => t.CreatedAt, opt => opt.Ignore())
                    .ForMember(t => t.UpdatedAt, opt => opt.Ignore())
                    .ForMember(t => t.ReceiverName, opt => opt.MapFrom(p => p.Name))
                    .ForMember(t => t.ReceiverAddress, opt => opt.MapFrom(p => p.Address))
                    .ForMember(t => t.TitleName, opt => opt.MapFrom(p => p.Title))
                    .ForMember(t => t.LetterFileName, opt => opt.Ignore())
                    .ForMember(t => t.ActionByPermitManager, opt => opt.Ignore())
                    .ForMember(t => t.AtResearchPermitApplication, opt => opt.Ignore())
                    .ForMember(t => t.Status, opt => opt.Ignore())
                    .ForMember(t => t.AttachmentsFileNames, opt => opt.Ignore())
                    .ForMember(t => t.AttachmentFileNames, opt => opt.Ignore())
                    .AfterMap((s, d) =>
                    {
                        if (!string.IsNullOrEmpty(s.AttachmentFileName))
                        {
                            d.AttachmentsFileNames.Add(s.AttachmentFileName);
                        }
                    });

                CreateMap<PermitViewModel.PermitLetterEditViewModel, ResearchPermitLetter>()
                    .ForMember(t => t.Id, opt => opt.Ignore())
                    .ForMember(t => t.CreatedAt, opt => opt.Ignore())
                    .ForMember(t => t.UpdatedAt, opt => opt.Ignore())
                    .ForMember(t => t.ReceiverName, opt => opt.MapFrom(p => p.Name))
                    .ForMember(t => t.ReceiverAddress, opt => opt.MapFrom(p => p.Address))
                    .ForMember(t => t.TitleName, opt => opt.MapFrom(p => p.Title))
                    .ForMember(t => t.LetterFileName, opt => opt.Ignore())
                    .ForMember(t => t.ActionByPermitManager, opt => opt.Ignore())
                    .ForMember(t => t.AtResearchPermitApplication, opt => opt.Ignore())
                    .ForMember(t => t.Status, opt => opt.Ignore())
                    .ForMember(t => t.AttachmentsFileNames, opt => opt.Ignore())
                    .ForMember(t => t.AttachmentFileNames, opt => opt.Ignore())
                    .AfterMap((s, d) =>
                    {
                        if (!string.IsNullOrEmpty(s.AttachmentFileName))
                        {
                            d.AttachmentsFileNames.Clear();
                            d.AttachmentsFileNames.Add(s.AttachmentFileName);
                        }
                    });

                CreateMap<SurveySubmission, SurveySubmissionListViewModel>()
                    .ForMember(t => t.ID, opt => opt.MapFrom(p => p.Id))
                    .ForMember(t => t.SubmittedBy, opt => opt.MapFrom(p => p.User.PersonName))
                    .ForMember(t => t.SubmissionDate, opt => opt.MapFrom(p => p.SubmissionDate.ToString("dd/MM/yyyy")))
                    .ForMember(t => t.HasAdditionalSpecies, opt => opt.MapFrom(p => p.HasAdditionalSpecies))
                    .ForMember(t => t.StartTime, opt => opt.MapFrom(p => p.CreatedAt.ToString("hh:mm")))
                    .ForMember(t => t.Status, opt => opt.MapFrom(p => Enum.GetName(typeof(BIOME.Enumerations.Survey.SurveySubmittedStatusRank), p.SubmissionStatus)))
                    .ForMember(t => t.SpeciesDetails, opt => opt.Ignore())
                    .ForMember(t => t.PreQuestionDetails, opt => opt.Ignore())
                    .ForMember(t => t.PostQuestionDetails, opt => opt.Ignore())
                    .ForMember(t => t.SurveyLocation, opt => opt.MapFrom(p => p.SurveyLocation.Location));


                CreateMap<SurveyViewModel.SurveyInforViewModel, BIOME.Models.Survey>()
                    .ForMember(t => t.ApprovedById, opt => opt.MapFrom(p => p.ApprovedByID))
                    .ForMember(t => t.CreatedAt, opt => opt.Ignore())
                    .ForMember(t => t.Description, opt => opt.MapFrom(p => p.Description))
                    .ForMember(t => t.DownloadPermission, opt => opt.MapFrom(p => p.DownloadPermission))
                    .ForMember(t => t.Duration, opt => opt.MapFrom(p => p.duration))
                    .ForMember(t => t.EndDate, opt => opt.MapFrom(p => DateTimeOffset.ParseExact(p.EndDate, "dd/MM/yyyy", new CultureInfo("en-US")).AddHours(23).AddMinutes(59).AddSeconds(59).DateTime))
                    .ForMember(t => t.IsActive, opt => opt.MapFrom(p => p.IsActive))
                    .ForMember(t => t.IsAutoApproveM, opt => opt.MapFrom(p => p.IsAutoApproveM))
                    .ForMember(t => t.IsSearchable, opt => opt.MapFrom(p => p.IsSearchable))
                    .ForMember(t => t.OwnerId, opt => opt.MapFrom(p => p.OwnerID))
                    .ForMember(t => t.StartDate, opt => opt.MapFrom(p => DateTimeOffset.ParseExact(p.StartDate, "dd/MM/yyyy", new CultureInfo("en-US")).DateTime))
                    .ForMember(t => t.SurveyStatus, opt => opt.MapFrom(p => p.SurveyStatus))
                    .ForMember(t => t.SurveyCategoryId, opt => opt.MapFrom(p => p.CategoryID))
                    .ForMember(t => t.Title, opt => opt.MapFrom(p => p.Title))
                    .ForMember(t => t.ViewPermission, opt => opt.MapFrom(p => p.ViewPermission))
                    .ForMember(t => t.UpdatedAt, opt => opt.Ignore())
                    .ForMember(t => t.Owner, opt => opt.Ignore())
                    .ForMember(t => t.SurveyLocation, opt => opt.Ignore())
                    .ForMember(t => t.SurveyMembers, opt => opt.Ignore())
                    .ForMember(t => t.SurveyQuestions, opt => opt.Ignore())
                    .ForMember(t => t.SurveySpecies, opt => opt.Ignore())
                    .ForMember(t => t.SurveySubmission, opt => opt.Ignore())
                    .ForMember(t => t.SurveyCategory, opt => opt.Ignore())
                    .ForMember(t => t.MemberCount, opt => opt.Ignore())
                    .ForMember(t => t.LogoName, opt => opt.Ignore())
                    .ForMember(t => t.LogoPath, opt => opt.Ignore())
                    .ForMember(t => t.CopyFromSurveyId, opt => opt.Ignore())
                    .ForMember(t => t.CopyBy, opt => opt.Ignore())
                    .ForMember(t => t.CopyBySysAdmin, opt => opt.Ignore())
                    .ForMember(t => t.FirstTimeApproved, opt => opt.Ignore())
                    .ForMember(t => t.Id, opt => opt.MapFrom(p => p.SurveyId));

                CreateMap<SurveyViewModel.SurveyAdminViewModel, BIOME.Models.Survey>()
                    .ForMember(t => t.ApprovedById, opt => opt.MapFrom(p => p.ApprovedByID))
                    .ForMember(t => t.CreatedAt, opt => opt.Ignore())
                    .ForMember(t => t.Description, opt => opt.MapFrom(p => p.Description))
                    .ForMember(t => t.DownloadPermission, opt => opt.MapFrom(p => p.DownloadPermission))
                    .ForMember(t => t.Duration, opt => opt.MapFrom(p => p.duration))
                    .ForMember(t => t.EndDate, opt => opt.MapFrom(p => DateTimeOffset.ParseExact(p.EndDate, "dd/MM/yyyy", new CultureInfo("en-US")).DateTime.AddHours(23).AddMinutes(59).AddSeconds(59)))
                    .ForMember(t => t.IsActive, opt => opt.MapFrom(p => p.IsActive))
                    .ForMember(t => t.IsAutoApproveM, opt => opt.MapFrom(p => p.IsAutoApproveM))
                    .ForMember(t => t.IsSearchable, opt => opt.MapFrom(p => p.IsSearchable))
                    .ForMember(t => t.OwnerId, opt => opt.MapFrom(p => p.OwnerID))
                    .ForMember(t => t.StartDate, opt => opt.MapFrom(p => DateTimeOffset.ParseExact(p.StartDate, "dd/MM/yyyy", new CultureInfo("en-US")).DateTime))
                    .ForMember(t => t.SurveyStatus, opt => opt.MapFrom(p => p.SurveyStatus))
                    .ForMember(t => t.SurveyCategoryId, opt => opt.MapFrom(p => p.CategoryID))
                    .ForMember(t => t.Title, opt => opt.MapFrom(p => p.Title))
                    .ForMember(t => t.ViewPermission, opt => opt.MapFrom(p => p.ViewPermission))
                    .ForMember(t => t.UpdatedAt, opt => opt.Ignore())
                    .ForMember(t => t.Owner, opt => opt.Ignore())
                    .ForMember(t => t.SurveyLocation, opt => opt.Ignore())
                    .ForMember(t => t.SurveyMembers, opt => opt.Ignore())
                    .ForMember(t => t.SurveyQuestions, opt => opt.Ignore())
                    .ForMember(t => t.SurveySpecies, opt => opt.Ignore())
                    .ForMember(t => t.SurveySubmission, opt => opt.Ignore())
                    .ForMember(t => t.SurveyCategory, opt => opt.Ignore())
                    .ForMember(t => t.MemberCount, opt => opt.Ignore())
                    .ForMember(t => t.LogoName, opt => opt.Ignore())
                    .ForMember(t => t.LogoPath, opt => opt.Ignore())
                    .ForMember(t => t.CopyFromSurveyId, opt => opt.Ignore())
                    .ForMember(t => t.CopyBy, opt => opt.Ignore())
                    .ForMember(t => t.CopyBySysAdmin, opt => opt.Ignore())
                    .ForMember(t => t.FirstTimeApproved, opt => opt.Ignore())
                    .ForMember(t => t.Id, opt => opt.MapFrom(p => p.SurveyId));


                CreateMap<BIOME.Models.Survey, SurveyViewModel.SurveyInforViewModelLog>()
                   .ForMember(t => t.EndDate, opt => opt.MapFrom(p => p.EndDate.Value.ToString("dd/MM/yyyy")))
                   .ForMember(a => a.LogoFileName, opt => opt.MapFrom(p => p.LogoName))
                   .ForMember(a => a.CategoryID,opt=> opt.MapFrom(p => p.SurveyCategoryId))
                   .ForMember(a => a.SurveyId, opt => opt.MapFrom(p => p.Id))
                   .ForMember(a => a.SpeciesCategoryName, opt => opt.MapFrom(p => p.SurveyCategory.Name))
                   .ForMember(a => a.SurveyQuestion, opt => opt.MapFrom(p => p.SurveyQuestions))
                   .ForMember(t => t.StartDate, opt => opt.MapFrom(p => p.StartDate.Value.ToString("dd/MM/yyyy")));

                CreateMap<BIOME.Models.SurveyQuestions, SurveyViewModel.SurveyQuestionLog>()
                    .ForMember(t => t.SurveyQuestionDetailDescription, opt => opt.MapFrom(p => p.SurveyQuestionDetail.Select(t=>t.Description).ToList()));

                CreateMap<BIOME.Models.Survey, SurveyViewModel.SurveyAdminViewModel>()
                    .ForMember(t => t.Description, opt => opt.MapFrom(p => p.Description))
                    .ForMember(t => t.DownloadPermission, opt => opt.MapFrom(p => p.DownloadPermission))
                    .ForMember(t => t.duration, opt => opt.MapFrom(p => p.Duration != null ? p.Duration.Value : 0))
                    .ForMember(t => t.EndDate, opt => opt.MapFrom(p => p.EndDate.Value.ToString("dd/MM/yyyy")))
                    .ForMember(t => t.IsActive, opt => opt.MapFrom(p => p.IsActive))
                    .ForMember(t => t.IsAutoApproveM, opt => opt.MapFrom(p => p.IsAutoApproveM))
                    .ForMember(t => t.IsSearchable, opt => opt.MapFrom(p => p.IsSearchable))
                    .ForMember(t => t.OwnerID, opt => opt.MapFrom(p => p.OwnerId))
                    .ForMember(t => t.StartDate, opt => opt.MapFrom(p => p.StartDate.Value.ToString("dd/MM/yyyy")))
                    .ForMember(t => t.SurveyStatus, opt => opt.MapFrom(p => p.SurveyStatus))
                    .ForMember(t => t.CategoryID, opt => opt.MapFrom(p => p.SurveyCategoryId))
                    .ForMember(t => t.Title, opt => opt.MapFrom(p => p.Title))
                    .ForMember(t => t.ViewPermission, opt => opt.MapFrom(p => p.ViewPermission))
                    .ForMember(t => t.Location, opt => opt.MapFrom(p => p.Location))
                    .ForMember(t => t.HasSubmission, opt => opt.MapFrom(p => p.SurveySubmission.Count > 0))
                    .ForMember(t => t.IsNotEditable, opt => opt.MapFrom(p => p.SurveyStatus == (int)BIOME.Enumerations.Survey.SurveyStatusRank.Pending))
                    .ForMember(t => t.LogoName, opt => opt.MapFrom(p => p.LogoName))
                    .ForMember(t => t.LogoPath, opt => opt.MapFrom(p => p.LogoPath))
                    .ForMember(t => t.QuestionsAS, opt => opt.Ignore())
                    .ForMember(t => t.QuestionsBS, opt => opt.Ignore())
                    .ForMember(t => t.File, opt => opt.Ignore())
                    .ForMember(t => t.SpeciesCategoryName, opt => opt.Ignore())
                        .ForMember(t => t.LocationNames, opt => opt.Ignore())
                    //.ForMember(t => t.HiddenCategoryName, opt => opt.Ignore())
                    .ForMember(t => t.SpeciesList, opt => opt.Ignore())
                    .ForMember(t => t.SpeciesIdList, opt => opt.Ignore())
                    .ForMember(t => t.LocList, opt => opt.Ignore())
                    .ForMember(t => t.KMLFile, opt => opt.Ignore())
                    .ForMember(t => t.SurveyId, opt => opt.MapFrom(p => p.Id));



                CreateMap<BIOME.Models.Survey, SurveyViewModel.SurveyInforViewModel>()
                    .ForMember(t => t.IsAutoApproveM, opt => opt.MapFrom(p => p.IsAutoApproveM))
                    .ForMember(t => t.CategoryID, opt => opt.MapFrom(p => p.SurveyCategoryId))
                    .ForMember(t => t.Description, opt => opt.MapFrom(p => p.Description))
                    .ForMember(t => t.DownloadPermission, opt => opt.MapFrom(p => p.DownloadPermission))
                    .ForMember(t => t.duration, opt => opt.MapFrom(p => p.Duration))
                    .ForMember(t => t.EndDate, opt => opt.MapFrom(p => p.EndDate))
                    .ForMember(t => t.IsActive, opt => opt.MapFrom(p => p.IsActive))
                    .ForMember(t => t.IsAutoApproveM, opt => opt.MapFrom(p => p.IsAutoApproveM))
                    .ForMember(t => t.IsSearchable, opt => opt.MapFrom(p => p.IsSearchable))
                    .ForMember(t => t.OwnerID, opt => opt.MapFrom(p => p.OwnerId))
                    .ForMember(t => t.StartDate, opt => opt.MapFrom(p => p.StartDate))
                    .ForMember(t => t.SurveyStatus, opt => opt.MapFrom(p => p.SurveyStatus))
                    .ForMember(t => t.File, opt => opt.Ignore())
                    .ForMember(t => t.LocationNames, opt => opt.Ignore())
                    .ForMember(t => t.SpeciesCategoryName, opt => opt.Ignore())
                    .ForMember(t => t.HiddenCategoryName, opt => opt.Ignore())
                    .ForMember(t => t.SpeciesList, opt => opt.Ignore())
                    .ForMember(t => t.SpeciesIdList, opt => opt.Ignore())                  
                    .ForMember(t => t.LocList, opt => opt.Ignore())
                    .ForMember(t => t.KMLFile, opt => opt.Ignore())
                    .ForMember(t => t.QuestionsBS, opt => opt.Ignore())
                    .ForMember(t => t.QuestionsAS, opt => opt.Ignore())
                    .ForMember(t => t.SurveyId, opt => opt.MapFrom(p => p.Id))
                    .ForMember(t => t.Title, opt => opt.MapFrom(p => p.Title))
                    .ForMember(t => t.ViewPermission, opt => opt.MapFrom(p => p.ViewPermission))
                    .ForMember(t => t.ApprovedByID, opt => opt.MapFrom(p => p.ApprovedById));

            }
        }
    }
}

