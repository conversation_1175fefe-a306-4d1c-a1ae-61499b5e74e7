﻿using Microsoft.AspNet.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public class IdentityEmailService : IIdentityMessageService
    {
        public async Task SendAsync(IdentityMessage message)
        {
            var client = new SmtpClient();
            var email = new MailMessage(new MailAddress("<EMAIL>", "No-reply"),
           new MailAddress(message.Destination))
            {
                Subject = message.Subject,
                Body = message.Body,
                IsBodyHtml = true
            };

            client.SendCompleted += (s, e) => {
                client.Dispose();
            };
            await client.SendMailAsync(email);
        }
    }
}
