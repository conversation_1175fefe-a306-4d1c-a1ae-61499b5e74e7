﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Constants
{
    public static class Configuration
    {
        public static class ModelValidator
        {
            public static class RegexTester
            {
                public const string PhoneDigits = @"[\d\s\(\)\+\-]+";
                public const string Phone = @"^\+?[1-9]{1}[0-9\-\s]{3,14}$";
                public const string TwoDecimalNumber = @"\d+(\.\d{1,2})?";
                public const string mobile_ver_code = @"^[1-9]([0-9]{0,2}[0-9]|[0-9]{0,3})?";
                
            }
        }
        public static class Server
        {
            public static class AppSettings
            {
                public const string Mode = "Mode";
                public const string SiteType = "SiteType";
                public const string TemplateDirectory = "TemplateDirectory";
                public const string PermitPassLetterOutputDirectory = "PermitPassLetterOutputDirectory";
                public const string LDAP = "LDAP";
                public const string ElasticesearchServer = "ElasticseachServer";
                public const string ElasticsearchUserName = "ElasticsearchUserName";
                public const string ElasticsearchPassword = "ElasticsearchPassword";
                public const string ProtectedFilePath = "ProtectedFilePath";
                public const string SkipScheduleOnStartup = "skipScheduleOnStartup";
                public const string fbAppId = "fbAppId";
                public const string fbAppSecret = "fbAppSecret";
                public const string interUrl = "interUrl";
                public const string intraUrl = "intraUrl";
                //public const string RedirectDomain = "RedirectDomain";
                //public const string Proxy = "proxy";
                public const string MavenLayers = "mavenLayers";
                public const string DBAutoTrackLicenseKey = "DBAutoTrackLicenseKey";
                public const string DigitalSignatureKey = "DigitalSignatureKey";
            }
            public static class Mode
            {
                public const string Debug = "Debug";
                public const string Staging = "Staging";
                public const string Release = "Release";
            }

            public static class SiteType
            {
                public const string Internet = "Internet";
                public const string Intranet = "Intranet";
            }

           /* public static class PasswordCipher
            {
                public const string Key = "";
                public const string IV = "";
            }*/
            public static class DynamicPasswordCipher
            {
                public static string Key = "";
                public static string IV = "";
            }
        }

        public static class FilePath
        {
            public static class Contents
            {
                public static class Permit
                {
                    public const string IndemnityFile = "/UploadFiles/Permit/Indemnity";
                    public const string MiscFile = "/UploadFiles/Permit/Misc";
                    public const string ReportFile = "/UploadFiles/Permit/Report";
                    public const string TemplateFile = "/UploadFiles/Permit/Template";
                    public const string PassLetterFile = "/UploadFiles/Permit/PassLetter";

                    public const string PermitFileURLRoute = "/DownloadPermitFile/";
                    public const string PermitApplicationFileURLRoute = "/Admin/Research/ApplicationStatus/DownloadApplicationPDF/";
                }

                public static class Resource
                {
                    public const string ResourceFile = "/UploadFiles/Resource";
                }
                public static class DiscussionForum
                {
                    public const string AttachFile = "/UploadFiles/DiscussionForumAttachFile";
                }
            }
        }

        public static class ImagePath
        {
            public static class Contents
            {
                public static class Page
                {
                    public const string Header = "/Content/Page/Header";
                    public const string Footer = "/Content/Page/Footer";
                    public const string App = "/Content/Page/App";
                }

                public static class Sighting
                {
                    public const string ImageUpload = "/Content/Sighting/ImageUpload/";
                    public const string GuidePath = "/Content/Guide/";
                }
                public static class Survey
                {
                    public const string ImageUpload = "/Content/Survey/ImageUpload/";
                    public const string SpeciesUpload = "/Content/Survey/ImageUpload/species/";

                }
              
                public static class Project
                {
                    public const string ImageUpload = "/Content/Project/ImageUpload/";
                }

                public static class Highlights
                {
                    public const string Internet = "/Content/Highlights/Internet";
                    public const string Intranet = "/Content/Highlights/Intranet";
                }

                public static class User
                {
                    public const string ProfilePic = "/Content/User/ProfilePic";
                }

                public static class Permit
                {
                    public const string FieldsSurveyTeamProfilePic = "/UploadFiles/Permit/FieldSurveyTeam";
                }
            }
        }
        public static class FileUpload
        {
            public static class FileExt
            {
                public const string discussion_forum_attachement = ".doc,.docx,.jpeg,.jpg,.pdf,.png,.ppt,.pptx,.txt,.xls,.xlsx";
                public const string discussion_forum_attachementJSON = "[\"doc\",\"docx\",\"jpeg\",\"jpg\",\"pdf\",\"png\",\"ppt\",\"pptx\",\"txt\",\"xls\",\"xlsx\"]";



            }

      
        }
        public static bool IsGCCStorage()
        {
            //alwasy return true so that file sync will work between app servers. 
            //Only return false if want to turn off file sync between app servers and necessary to do debugging in developement mode.

            return true;



            /*try {
                //return bool.Parse(ConfigurationManager.AppSettings["isGCCStorage"]);
            }
            catch { 
                return false;
            }*/
        }

        public static string BIOMEUploadFileRootPathInternet()
        {
            try
            {
                return ConfigurationManager.AppSettings["BIOMEUploadFileRootPathInternet"];
            }
            catch
            {
                return "";
            }
        }

        public static string BIOMEUploadFileRootPathIntranet()
        {
            try
            {
                return ConfigurationManager.AppSettings["BIOMEUploadFileRootPathIntranet"];
            }
            catch
            {
                return "";
            }
        }

    }
}
