﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace BIOME.Models
{
   public class SurveyLocationAnswer:Entity<long>
    {
        public long SurveyLocationId { get; set; }
        [ForeignKey("SurveyLocationId")]
        public virtual SurveyLocation SurveyLocation { get; set; }
        public long SurveySubmissionId { get; set; } //need index
        [ForeignKey("SurveySubmissionId")]
        public virtual SurveySubmission SurveySubmission { get; set; }

    }
}
