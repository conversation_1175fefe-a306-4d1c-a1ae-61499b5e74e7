﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Resources;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Errors
{
    public class ErrorCodeHelper
    {

        [Serializable]
        public class HelperNoSettingsException : Exception
        {
            public HelperNoSettingsException() { }
            public HelperNoSettingsException(string message) : base(message) { }
            public HelperNoSettingsException(string message, Exception inner) : base(message, inner) { }
            protected HelperNoSettingsException(
              System.Runtime.Serialization.SerializationInfo info,
              System.Runtime.Serialization.StreamingContext context) : base(info, context)
            { }
        }

        #region Fields

        private static string resourceErrorKeyPrefix = null;
        private static ResourceManager errorResourceManager = null;
        //private readonly object resource;
        //private readonly Type resourceType;

        #endregion

        #region Constructors

        public ErrorCodeHelper()
        {
            
        }

        public ErrorCodeHelper(Type resourceType, string prefix)
        {
            errorResourceManager = new ResourceManager(resourceType);
            //this.resource = resource;
            //this.resourceType = resourceType;
            resourceErrorKeyPrefix = prefix;
        }

        #endregion

        #region Public Methods

        public string ErrorMsgForCode(int errorCode)
        {
            try
            {
                if (errorResourceManager == null || resourceErrorKeyPrefix == null)
                {
                    throw new HelperNoSettingsException();
                }
                return errorResourceManager.GetString(resourceErrorKeyPrefix + errorCode);
            }
            catch {
                return "";
            }
        }
        public string ErrorMsgForCodeReplacePlaceHolder(int errorCode)
        {
            try
            {
                return ErrorMsgForCode(errorCode).Replace("'{0}'", "").Replace("{0}", "").Replace("'{1}'", "").Replace("{1}", "");
            }
            catch
            {
                return "";
            }
        }

        #endregion

        #region Private Methods

        #endregion

    }
}
