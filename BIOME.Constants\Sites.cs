﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Constants
{
    public class Sites
    {
        public static class GeoJson
        {
            public const string LOC_CD = "LOC_CD";
            public const string GRASS_CUT_AREA = "GRASS_CUT_AREA";
            public const string SECTION_CD = "SECTION_CD";
            public const string BRANCH_CD = "BRANCH_CD";
            public const string DIVISION_CD = "DIVISION_CD";
            public const string LAST_UPD_DATE = "LAST_UPD_DATE";
            public const string LAST_UPD_BY = "LAST_UPD_BY";
            public const string LOC_CLASS_CD = "LOC_CLASS_CD";
            public const string AREA_MAINTAINED = "AREA_MAINTAINED";
            public const string DATE_EFFECTIVE = "DATE_EFFECTIVE";
            public const string LOC_DESC = "LOC_DESC";
            public const string AGENCY = "AGENCY";
            public const string EVE_ID = "EVE_ID";
            public const string GlobalID = "GlobalID";
            public const string SHAPESTArea = "SHAPE.STArea()";
            public const string SHAPESTLength = "SHAPE.STLength";            
            public const string SUB_LOC_CD = "SUB_LOC_CD";
            public const string SUB_LOC_DESC = "SUB_LOC_DESC";
            public const string OIC = "Officer_In_Charge";
            public const string PHONE_WORK = "OIC_Phone__Work_";
            public const string PHONE_MOBILE = "OIC_Phone__Mobile_";
            public const string EMAIL = "OIC_Email";
            public const string NAME = "PARK_NAME";
            public const string FEATURE_ID = "FeatureId";
        }
    }
}
