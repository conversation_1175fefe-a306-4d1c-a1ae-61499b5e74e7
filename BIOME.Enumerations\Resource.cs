﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Enumerations
{
    public class Resource
    {
        
        public enum ApprovalStatus
        {
            NoStatus = 0,
            Pending = 1,
            Published = 2,
            Rejected = 3,
        }

        public static List<object> ApprovalStatusOptionPending = new List<object>
        {
            new { value = (int)ApprovalStatus.Pending , text = "Pending"},
            new { value = (int)ApprovalStatus.Published , text = "Published"},
            new { value = (int)ApprovalStatus.Rejected , text = "Rejected"},
        };

        public static List<object> ApprovalStatusOptionNoStatus = new List<object>
        {
            new { value = (int)ApprovalStatus.NoStatus , text = "NoStatus"},
            new { value = (int)ApprovalStatus.Published , text = "Published"},
            new { value = (int)ApprovalStatus.Rejected , text = "Rejected"},
        };


        public static List<object> ApprovalStatusOptionPublished = new List<object>
        {
            new { value = (int)ApprovalStatus.Published , text = "Published"},
        };

        public static List<object> ApprovalStatusOptionRejected = new List<object>
        {
            new { value = (int)ApprovalStatus.Rejected , text = "Published"},
        };


        public static List<object> ApprovalStatusOptionSearch = new List<object>
        {
            new { value = (int)ApprovalStatus.NoStatus , text = "All"},
            new { value = (int)ApprovalStatus.Pending , text = "Pending"},
            new { value = (int)ApprovalStatus.Published , text = "Published"},
            new { value = (int)ApprovalStatus.Rejected , text = "Rejected"},
        };
        
    }
    public class ResourceLocationTypes {
        public static string Point = "Point";
        public static string Polygon = "Polygon";
        public static string Multiple = "Multiple";
    }
}
