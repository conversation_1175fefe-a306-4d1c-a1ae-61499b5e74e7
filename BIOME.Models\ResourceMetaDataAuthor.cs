﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResourceMetaDataAuthor : Entity<long>, IDescribableEntity 
    {
        public string Name { get; set; }
        [JsonIgnore]
        public virtual ResourceMetaData AtResourceMetaData { get; set; }
        public string Describe()
        {
            return "{ Name : \"" + Name + "\", ResourceMetaData : \"" + AtResourceMetaData?.Id   + "}";
        }
    }
}
