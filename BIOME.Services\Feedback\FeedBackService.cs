﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BIOME.Models;
using BIOME.ViewModels;

namespace BIOME.Services
{
    public class FeedBackService: ServiceBase,IFeedbackService
    {
        #region Constructors
        private readonly ApplicationDbContext dbContext;
         

        public FeedBackService(ApplicationDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        #endregion
        #region Public Method
        public long AddFeedBack(int userId , string subject, string name, string email, string description)
        {
            long retId = 0;
            Feedback feedback = new Feedback();
       
            feedback.Subject = subject;
            feedback.Name = name;
            feedback.Email = email;
            feedback.Description = description;
            
            dbContext.FeedBacks.Add(feedback);
            int maxid =Convert.ToInt32( dbContext.FeedBacks.Max(x => x.Id));
            if (dbContext.SaveChanges(false,userId, maxid) > 0)
                retId = feedback.Id;

            return retId;

        }
        public bool DelFeedBack(long Id)
        {
            Feedback feedback = new Feedback { Id = Id };
            dbContext.FeedBacks.Attach(feedback);
            dbContext.FeedBacks.Remove(feedback);

            return (dbContext.SaveChanges() > 0);
        }
        public bool UpdateFeedBack(long Id, string subject, string name, string email, string description)
        {
            Feedback feedback = dbContext.FeedBacks.Find(Id);
            dbContext.Entry(feedback).State = System.Data.Entity.EntityState.Modified;
            feedback.Subject = subject;
            feedback.Name = name;
            feedback.Email = email;
            feedback.Description = description;
            return (dbContext.SaveChanges() > 0);
        }

        public List<Feedback> GetListFeedBack()
        {
            var query = (from b in dbContext.FeedBacks
                         orderby b.Id ascending
                         select b).ToList();

            return query;
        }
        public List<Feedback> GetListFeedBack(string searhstring)
        {
            var query = (from b in dbContext.FeedBacks
                         where b.Subject.ToLower().Contains(searhstring.ToLower())
                         orderby b.Id ascending
                         select b).ToList();

            return query;
        }

        public Feedback GetFeedbacks(long id)
        {
            var query = (from b in dbContext.FeedBacks
                         where b.Id == id
                         select b).FirstOrDefault();
            
            return query;
        }
        #endregion
    }
}
