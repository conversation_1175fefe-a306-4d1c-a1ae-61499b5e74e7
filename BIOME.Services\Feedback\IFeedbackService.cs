﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BIOME.Models;

namespace BIOME.Services
{
   public interface IFeedbackService
    {
        long AddFeedBack(int userid, string subject, string name, string email, string description);
        bool DelFeedBack(long Id);
        bool UpdateFeedBack(long Id, string subject, string name, string email, string description);
         
        List<Feedback> GetListFeedBack();

        List<Feedback> GetListFeedBack(string searhstring);

        Feedback GetFeedbacks(long id);
    }
}
