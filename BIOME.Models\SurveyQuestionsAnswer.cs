﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations.Schema;
namespace BIOME.Models
{
    public class SurveyQuestionsAnswer:Entity<long>,IDescribableEntity
    {
        public long SurveySubmissionId { get; set; } //need index
        [ForeignKey("SurveySubmissionId")]
        public virtual SurveySubmission SurveySubmission { get; set; }
        public long QuestionId { get; set; } //need index
        [ForeignKey("QuestionId")]
        public virtual SurveyQuestions SurveyQuestions { get; set; }
        public long? QuestionDetailId { get; set; } //need index
        [ForeignKey("QuestionDetailId")]
        public virtual SurveyQuestionDetail SurveyQuestionDetail { get; set; }
        public string AnswerText { get; set; }

        public string Describe()
        {
            return "{ SurveySubmissionId : \"" + SurveySubmissionId + "\", QuestionId : \"" + QuestionId + "\", QuestionDetailId : \"" + QuestionDetailId + "\", AnswerText : \"" + AnswerText   + "}";
        }
    }
}
