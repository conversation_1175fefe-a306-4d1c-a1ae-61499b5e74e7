﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResourceDocument : Entity<long>,ISoftDelete,IDescribableEntity
    {
        public bool IsDeleted { get; set; }
        public string fileName { get; set; }
        public string fileExtension { get; set; }

        public int ContentLength { get; set; }
        public string ContentType { get; set; }

        public string ServerFileName { get; set; }

        public string FilePath { get; set; }

        public bool IsIndex { get; set; }
        public DateTimeOffset? IndexedDateTime { get; set; }
        public bool IsMapIndex { get; set; } //R7
        public DateTimeOffset? IndexedMapDateTime { get; set; }

        //CR3/4 Phase2
        public long? ResearchReportID { get; set; }

        public virtual ResourceMetaData AtResourceMetaData { get; set; }

        public virtual ResourceDocumentType AtResourceDocumentType { get; set; }

        public virtual ResourceStructureTemplate AtResourceStructureTemplate { get; set; }

        public virtual IList<ResourceFilePermission> ResourceFilePermissions { get; set; }


        
        [NotMapped]
        public string FileToken { get; set; }

        [NotMapped]
        public string ViewReportToken { get; set; }

        [NotMapped]
        public bool IsPreview { get; set; }

        [NotMapped]
        public bool IsDownloadable { get; set; }

        [NotMapped]
        public bool IsViewReport { get; set; }

        
        public string Describe()
        {
            return "{ fileName : \"" + fileName + "\", fileExtension : \"" + fileExtension + "\", ContentLength : \"" + ContentLength + "\", ContentType : \"" + ContentType
                + "\", ServerFileName : \"" + ServerFileName + "\", FilePath : \"" + FilePath + "\", IsIndex : \"" + IsIndex 
                //+ "\", File : \"" + File
                + "\", FileToken : \"" + FileToken + "\", ViewReportToken : \"" + ViewReportToken + "\", IsPreview : \"" + IsPreview
                + "\", IsDownloadable : \"" + IsDownloadable   + "\", IsDeleted : \"" + IsDeleted + "}";
        }


        public ResourceDocument()
        {
            ResourceFilePermissions = new List<ResourceFilePermission>();
        }

    }
}
