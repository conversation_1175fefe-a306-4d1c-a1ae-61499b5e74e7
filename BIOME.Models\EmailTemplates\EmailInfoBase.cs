﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models.EmailTemplates
{
    public class EmailInfoBase
    {
        public string Sender { get; set; }
        public string SenderName { get; set; }
        public string Subject { get; set; }
        public string Title { get; set; }
        public List<string> Recipients { get; set; }
        public List<string> CCs { get; set; }
        public List<string> BCCs { get; set; }

        public EmailInfoBase(string senderName, string senderEmail, string subject, List<string> recipients, string title)
        {
            this.SenderName = senderName;
            this.Sender = senderEmail;
            this.Subject = subject;
            this.Recipients = recipients;
            this.Title = title;
        }
    }
}
