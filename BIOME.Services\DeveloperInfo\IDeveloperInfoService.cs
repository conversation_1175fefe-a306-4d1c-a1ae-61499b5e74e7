﻿using BIOME.Models;

namespace BIOME.Services
{
    public interface IDeveloperInfoService
    {
        DeveloperInfo FindByDeveloperKeyAndIsActive(string developerKey);
        string ConstructDeveloperTokenHash(DeveloperInfo devInfo);
        string ConstructDeveloperTokenHash(string data, string developerKey);
        bool IsDeveloperTokenCorrect(string developerKey, string hashedTokenToCompare);

        string ConstructHashForLoginFormRandomRoute(string input);
        string GenerateRandomFormRoute();
    }
}