﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Utilities.Helpers;

namespace BIOME.Services.Comparer
{
    public class PermitNumberComparer : IComparer<string>
    {
        public int Compare(string x, string y)
        {
            int permitYearX = 0;
            int permitNumX = 0;
            int? renewedNumX = null;
            string amendLetterX = string.Empty;
            bool rejectedX = false;
            var successX = ResearchHelper.SplitPermitNumber(x, out permitYearX, out permitNumX, out amendLetterX, out renewedNumX, out rejectedX);

            int permitYearY = 0;
            int permitNumY = 0;
            int? renewedNumY = null;
            string amendLetterY = string.Empty;
            bool rejectedY = false;
            var successY = ResearchHelper.SplitPermitNumber(y, out permitYearY, out permitNumY, out amendLetterY, out renewedNumY, out rejectedY);

            if (successX && !successY)
            {
                return 1;
            }
            else if (!successX && successY)
            {
                return -1;
            }
            else if (permitYearX < permitYearY)
            {
                return -1;
            }
            else if (permitYearX > permitYearY)
            {
                return 1;
            }
            else if (permitNumX < permitNumY)
            {
                return -1;
            }
            else if (permitNumX > permitNumY)
            {
                return 1;
            }
            else if (string.IsNullOrEmpty(amendLetterX) && !string.IsNullOrEmpty(amendLetterY))
            {
                return -1;
            }
            else if (!string.IsNullOrEmpty(amendLetterX) && string.IsNullOrEmpty(amendLetterY))
            {
                return 1;
            }
            else if (!string.IsNullOrEmpty(amendLetterX) && !string.IsNullOrEmpty(amendLetterY) &&
                amendLetterX.CompareTo(amendLetterY) != 0)
            {
                return amendLetterX.CompareTo(amendLetterY);
            }

            if (!renewedNumX.HasValue && renewedNumY.HasValue)
            {
                return -1;
            }
            else if (renewedNumX.HasValue && !renewedNumY.HasValue)
            {
                return 1;
            }
            else if (renewedNumX.HasValue && renewedNumY.HasValue &&
                renewedNumX.Value != renewedNumY.Value)
            {
                return renewedNumX.Value.CompareTo(renewedNumY.Value);
            }

            if (!rejectedX && rejectedY)
            {
                return -1;
            }
            else if (rejectedX && !rejectedY)
            {
                return 1;
            }

            return 0;
        }
    }
}
