﻿using AutoMapper;
using BIOME.Models;
using BIOME.Constants;
using BIOME.ViewModels;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Utilities.Helpers;

namespace BIOME.Services
{
    public class HighlightsService : ServiceBase, IHighlightsService
    {
        #region Fields

        private readonly ApplicationDbContext dbContext;

        #endregion

        #region Constructors

        public HighlightsService(ApplicationDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        #endregion

        #region Public Methods

        public IQueryable<InternetHighlight> GetInternetHighlights()
        {
            return dbContext.InternetHighlights;
        }

        public IQueryable<IntranetHighlight> GetIntranetHighlights()
        {
            return dbContext.IntranetHighlights;
        }

        public AdminHomepageViewModel.HighlightEditUploadViewModel GetInternetHighlightsEdit(long id)
        {
            var highlights = dbContext.InternetHighlights.Find(id);
            return Mapper.Map<AdminHomepageViewModel.HighlightEditUploadViewModel>(highlights);
        }

        public IList<AdminHomepageViewModel.HighlightViewModel> GetInternetHighlights(out int totalCount)
        {
            var highlights = GetInternetHighlights();
            totalCount = highlights.Count();

            var pagedHighlights = highlights.ToList();
            return Mapper.Map<IList<AdminHomepageViewModel.HighlightViewModel>>(pagedHighlights);
        }

        public IList<HomepageViewModel.ResearchHighlightViewModel> GetInternetHighlightsCarousel()
        {
            var highlights = GetInternetHighlights();
            var highlightsList = highlights.ToList();
            return Mapper.Map<IList<HomepageViewModel.ResearchHighlightViewModel>>(highlightsList);
        }

        public IPagedList<AdminHomepageViewModel.HighlightViewModel> GetInternetHighlightsPaged(int currentIndexPage, int pageSize, out int totalCount)
        {
            var highlights = GetInternetHighlights();
            totalCount = highlights.Count();

            if (totalCount<= 0)
            {
                return new MvcPaging.PagedList<AdminHomepageViewModel.HighlightViewModel>(new List<AdminHomepageViewModel.HighlightViewModel>(), currentIndexPage, pageSize);
            }

            int safeCurrentIndexPage = Math.Max(0, currentIndexPage);
            var pagedHighlights = highlights.OrderBy(h => h.CreatedAt).ToPagedList(safeCurrentIndexPage, pageSize, totalCount);            return Mapper.Map<IPagedList<AdminHomepageViewModel.HighlightViewModel>>(pagedHighlights);
        }

        public AdminHomepageViewModel.HighlightEditUploadViewModel GetIntranetHighlightsEdit(long id)
        {
            var highlights = dbContext.IntranetHighlights.Find(id);
            return Mapper.Map<AdminHomepageViewModel.HighlightEditUploadViewModel>(highlights);
        }

        public IList<AdminHomepageViewModel.HighlightViewModel> GetIntranetHighlights(out int totalCount)
        {
            var highlights = GetIntranetHighlights();
            totalCount = highlights.Count();

            var pagedHighlights = highlights.ToList();
            return Mapper.Map<IList<AdminHomepageViewModel.HighlightViewModel>>(pagedHighlights);
        }

        public IList<HomepageViewModel.ResearchHighlightViewModel> GetIntranetHighlightsCarousel()
        {
            var highlights = GetIntranetHighlights();
            var highlightsList = highlights.ToList();
            return Mapper.Map<IList<HomepageViewModel.ResearchHighlightViewModel>>(highlightsList);
        }

        public IPagedList<AdminHomepageViewModel.HighlightViewModel> GetIntranetHighlightsPaged(int currentIndexPage, int pageSize, out int totalCount)
        {
            var highlights = GetIntranetHighlights();
            totalCount = highlights.Count();

            if (totalCount <= 0)
            {
                return new MvcPaging.PagedList<AdminHomepageViewModel.HighlightViewModel>(new List<AdminHomepageViewModel.HighlightViewModel>(), currentIndexPage, pageSize);
            }

            var pagedHighlights = highlights.OrderBy(h => h.CreatedAt).ToPagedList(currentIndexPage, pageSize, totalCount);
            return Mapper.Map<IPagedList<AdminHomepageViewModel.HighlightViewModel>>(pagedHighlights);
        }

        public async Task<ServiceResult> AddHighlight(string filename, string headerContentFullPath, AdminHomepageViewModel.HighlightUploadViewModel uploadViewModel, bool isInternet)
        {
            AdminHomepageViewModel.HighlightViewModelLog log_obj;
            string log_json;
            if (isInternet)
            {
                var newHighlight = Mapper.Map<InternetHighlight>(uploadViewModel);
                newHighlight.BackgroundImgName = filename;

                dbContext.InternetHighlights.Add(newHighlight);

                var changes = await dbContext.SaveChangesAsync();
                if (changes <= 0)
                {
                    return ServiceResult.Failed(new ServiceError() { Code = 9008 });
                }

                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(newHighlight, log_obj);
                log_json = log_obj.ToJson();

                return ServiceResult.SuccessWithOutput(log_json);
            }
            else
            {
                var newHighlight = Mapper.Map<IntranetHighlight>(uploadViewModel);
                newHighlight.BackgroundImgName = filename;

                dbContext.IntranetHighlights.Add(newHighlight);

                var changes = await dbContext.SaveChangesAsync();
                if (changes <= 0)
                {
                    return ServiceResult.Failed(new ServiceError() { Code = 9008 });
                }

                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(newHighlight, log_obj);
                log_json = log_obj.ToJson();

                return ServiceResult.SuccessWithOutput(log_json);
            }

           
        }

        public async Task<ServiceResult> UpdateHighlight(string filename, string headerContentFullPath, AdminHomepageViewModel.HighlightEditUploadViewModel uploadViewModel, bool isInternet)
        {
            var tempOldFilename = string.Empty;
            AdminHomepageViewModel.HighlightViewModelLog log_obj;
            string log_json_before, log_json_after;
            if (isInternet)
            {
                var existingHighlight = await dbContext.InternetHighlights.FindAsync(uploadViewModel.Id);

                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(existingHighlight, log_obj);
                log_json_before = log_obj.ToJson();

                if (!string.IsNullOrEmpty(existingHighlight.BackgroundImgName))
                {
                    tempOldFilename = existingHighlight.BackgroundImgName;
                }

                Mapper.Map(uploadViewModel, existingHighlight);

                existingHighlight.BackgroundImgName = filename;


                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(existingHighlight, log_obj);
                log_json_after = log_obj.ToJson();
            }
            else
            {
                var existingHighlight = await dbContext.IntranetHighlights.FindAsync(uploadViewModel.Id);

                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(existingHighlight, log_obj);
                log_json_before = log_obj.ToJson();

                if (!string.IsNullOrEmpty(existingHighlight.BackgroundImgName))
                {
                    tempOldFilename = existingHighlight.BackgroundImgName;
                }

                Mapper.Map(uploadViewModel, existingHighlight);

                existingHighlight.BackgroundImgName = filename;

                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(existingHighlight, log_obj);
                log_json_after = log_obj.ToJson();
            }

            var changes = await dbContext.SaveChangesAsync();
            if (changes <= 0)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 9009 });
            }

            if (!string.IsNullOrEmpty(tempOldFilename))
            {
                //File.Delete(Path.Combine(headerContentFullPath, tempOldFilename));
            }

            return ServiceResult.SuccessWithOutput(string.Format(BIOME.Enumerations.Audit.AuditLogAction.BeforeAfterLogFormat, log_json_before, log_json_after));
        }

        public async Task<ServiceResult> UpdateHighlight(AdminHomepageViewModel.HighlightEditUploadViewModel uploadViewModel, bool isInternet)
        {
            AdminHomepageViewModel.HighlightViewModelLog log_obj;
            string log_json_before, log_json_after;
            if (isInternet)
            {
                var existingHighlight = await dbContext.InternetHighlights.FindAsync(uploadViewModel.Id);
                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(existingHighlight, log_obj);
                log_json_before = log_obj.ToJson();

                if (existingHighlight.Title == uploadViewModel.Title &&
                    existingHighlight.Description == uploadViewModel.Description &&
                    existingHighlight.Url == uploadViewModel.Url)
                {
                    return ServiceResult.Failed(new ServiceError() { Code = 9001 });
                }

                Mapper.Map(uploadViewModel, existingHighlight);

                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(existingHighlight, log_obj);
                log_json_after = log_obj.ToJson();
            }
            else
            {
                var existingHighlight = await dbContext.IntranetHighlights.FindAsync(uploadViewModel.Id);
                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(existingHighlight, log_obj);
                log_json_before = log_obj.ToJson();

                if (existingHighlight.Title == uploadViewModel.Title &&
                    existingHighlight.Description == uploadViewModel.Description &&
                    existingHighlight.Url == uploadViewModel.Url)
                {
                    return ServiceResult.Failed(new ServiceError() { Code = 9001 });
                }

                Mapper.Map(uploadViewModel, existingHighlight);

                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(existingHighlight, log_obj);
                log_json_after = log_obj.ToJson();
            }

            var changes = await dbContext.SaveChangesAsync();
            if (changes <= 0)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 9009 });
            }

            return ServiceResult.SuccessWithOutput(string.Format(BIOME.Enumerations.Audit.AuditLogAction.BeforeAfterLogFormat, log_json_before, log_json_after));
        }

        public async Task<ServiceResult> DeleteHighlight(long id, bool isInternet)
        {
            AdminHomepageViewModel.HighlightViewModelLog log_obj;
            string log_json_before;
            if (isInternet)
            {
                var existingHighlight = await dbContext.InternetHighlights.FindAsync(id);
                if (existingHighlight == null)
                {
                    return ServiceResult.Failed(new ServiceError() { Code = 9010 });
                }

                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(existingHighlight, log_obj);
                log_json_before = log_obj.ToJson();

                dbContext.InternetHighlights.Remove(existingHighlight);
            }
            else
            {
                var existingHighlight = await dbContext.IntranetHighlights.FindAsync(id);
                if (existingHighlight == null)
                {
                    return ServiceResult.Failed(new ServiceError() { Code = 9010 });
                }

                log_obj = new AdminHomepageViewModel.HighlightViewModelLog();
                Mapper.Map(existingHighlight, log_obj);
                log_json_before = log_obj.ToJson();

                dbContext.IntranetHighlights.Remove(existingHighlight);
            }

            var changes = await dbContext.SaveChangesAsync();
            if (changes <= 0)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 9009 });
            }

            return ServiceResult.SuccessWithOutput(string.Format(BIOME.Enumerations.Audit.AuditLogAction.BeforeAfterLogFormat, log_json_before, ""));
        }

        

        #endregion

        #region Private Methods

        #endregion
    }
}
