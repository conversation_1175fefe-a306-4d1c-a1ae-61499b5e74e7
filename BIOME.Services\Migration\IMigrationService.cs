﻿using BIOME.Models;
using System.Collections.Generic;

namespace BIOME.Services
{
    public interface IMigrationService
    {
        void AddSystemAdmin(string email, string password);
        void AddUsers(string email, string password, string role);

        void AddUsersForPerformanceTest(string email, string password, string role, string personName);
        void AddRole(string roleName);
        bool AddBadge(BadgeDetail badgeDetail);
        bool AddGuide(GuideDetail guideDetail);
        bool AddMainGroup(MainGroup mainGroup);
        bool AddSubGroup(SubGroup subGroup, string mainGroupName);
        bool AddSubSubGroup(SubSubGroup subSubGroup, string subGroupName);
        bool AddFirstSystemParameters(SystemParameters parameters);
        bool AddFirstMaintenanceNotice(MaintenanceNotice parameters);
        bool AddPageHeader(PageHeader header);
        bool AddPageFooter(PageFooter footer);
        bool AddHomepageContact(HomepageContact contact);
        bool AddHomepageAppInfo(HomepageAppInfo appInfo);
        bool AddResearchPermitStatus(ResearchPermitStatus status);
        bool AddLayerGroup(MapLayerGroup layerGroup);
        bool RemoveLayerGroup(string groupName);
        IList<MapLayerGroup> GetResourceGroups(string groupName);
    }
}