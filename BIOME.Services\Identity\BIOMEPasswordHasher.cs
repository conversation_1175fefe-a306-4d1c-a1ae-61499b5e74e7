﻿using Microsoft.AspNet.Identity;
//using OpenSSL.Crypto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Utilities.Helpers;
using static BIOME.Constants.Configuration.Server;
using System.Web;
using System.Web.SessionState;
using System.Security.Cryptography;
using System.IO;

namespace BIOME.Services
{
    public class BIOMEPasswordHasher : IPasswordHasher
    {
        private const int SaltSize = 16; // 128 bits
        private const int HashSize = 32; // 256 bits
        private const int Iterations = 10000; // Number of iterations for PBKDF2

        /* Unused because OpenSSL.Crypto is removed as part of Remediation for dependencies vulnerabilities.
         * public string HashPassword(string password)
         {
             var encryptedString = string.Empty;

             using (var aes = new CipherContext(Cipher.AES_256_CBC))
             {
                 var encryptedByte = aes.Encrypt(ASCIIEncoding.Default.GetBytes(password), ByteHelper.ConvertHexStringToByteArray(DynamicPasswordCipher.Key), ByteHelper.ConvertHexStringToByteArray(DynamicPasswordCipher.IV));// PasswordCipher.IV // PasswordCipher.Key
                 encryptedString = ByteHelper.ConvertByteArrayToHexString(encryptedByte); 
             }
             return encryptedString;
         }*/

        public string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentNullException(nameof(password));

            // Generate a random salt
            byte[] salt = new byte[SaltSize];
            using (var rng = new RNGCryptoServiceProvider())
            {
                rng.GetBytes(salt);
            }

            // Create the hash
            byte[] hash = GetPbkdf2Hash(password, salt, Iterations, HashSize);

            // Combine the salt and hash
            byte[] hashBytes = new byte[SaltSize + HashSize];
            Array.Copy(salt, 0, hashBytes, 0, SaltSize);
            Array.Copy(hash, 0, hashBytes, SaltSize, HashSize);

            // Convert to base64 for storage
            return Convert.ToBase64String(hashBytes);
        }

        /* Unused because OpenSSL.Crypto is removed as part of Remediation for dependencies vulnerabilities.
         * public string DehashPassword(string hashedPassword)
        {
            var decryptedString = string.Empty;

            using (var aes = new CipherContext(Cipher.AES_256_CBC))
            {
                var decryptedByte = aes.Decrypt(ByteHelper.ConvertHexStringToByteArray(hashedPassword), ByteHelper.ConvertHexStringToByteArray(DynamicPasswordCipher.Key), ByteHelper.ConvertHexStringToByteArray(DynamicPasswordCipher.IV));
                decryptedString = ASCIIEncoding.Default.GetString(decryptedByte); 
            }
            return decryptedString;
        }*/
        // Legacy Password hashing method, to be removed after migration to new hashing method
        public string DehashPassword(string hashedPassword)
        {
            var decryptedString = string.Empty;

            decryptedString = Decrypt(ByteHelper.ConvertHexStringToByteArray(hashedPassword), ByteHelper.ConvertHexStringToByteArray(DynamicPasswordCipher.Key), ByteHelper.ConvertHexStringToByteArray(DynamicPasswordCipher.IV));
            //decryptedString = ASCIIEncoding.Default.GetString(decryptedByte);
            return decryptedString;
        }
        public PasswordVerificationResult VerifyHashedPassword(string hashedPassword, string providedPassword)
        {
            if (string.IsNullOrEmpty(hashedPassword))
                throw new ArgumentNullException(nameof(hashedPassword));
            if (string.IsNullOrEmpty(providedPassword))
                throw new ArgumentNullException(nameof(providedPassword));

            try
            {
                // Extract the bytes
                byte[] hashBytes = Convert.FromBase64String(hashedPassword);

                // Get the salt from the stored hash
                byte[] salt = new byte[SaltSize];
                Array.Copy(hashBytes, 0, salt, 0, SaltSize);

                // Get the hash from the stored password
                byte[] storedHash = new byte[HashSize];
                Array.Copy(hashBytes, SaltSize, storedHash, 0, HashSize);

                // Generate the hash from the provided password
                byte[] computedHash = GetPbkdf2Hash(providedPassword, salt, Iterations, HashSize);

                // Compare the computed hash with the stored hash
                for (int i = 0; i < HashSize; i++)
                {
                    if (computedHash[i] != storedHash[i])
                        return PasswordVerificationResult.Failed;
                }

                return PasswordVerificationResult.Success;
            }
            catch
            {
                return PasswordVerificationResult.Failed;
            }
        }

        private static byte[] GetPbkdf2Hash(string password, byte[] salt, int iterations, int outputBytes)
        {
            using (var pbkdf2 = new Rfc2898DeriveBytes(password, salt, iterations))
            {
                return pbkdf2.GetBytes(outputBytes);
            }
        }

        public string Encrypt(string input, byte[] key, byte[] iv)
        {
            try
            {
                using (Aes aes = Aes.Create())
                {
                    aes.Key = key;
                    aes.IV = iv;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);
                    
                    byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                    byte[] encryptedBytes;
                    
                    using (MemoryStream ms = new MemoryStream())
                    {
                        using (CryptoStream cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                        {
                            cs.Write(inputBytes, 0, inputBytes.Length);
                            cs.FlushFinalBlock();
                        }
                        encryptedBytes = ms.ToArray();
                    }
                    
                    return Convert.ToBase64String(encryptedBytes);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Encryption failed: " + ex.Message);
            }
        }

        public string Decrypt(byte[] encryptedBytes, byte[] key, byte[] iv)
        {
            try
            {
                using (Aes aes = Aes.Create())
                {
                    aes.Key = key;
                    aes.IV = iv;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);
                    
                    using (MemoryStream ms = new MemoryStream(encryptedBytes))
                    {
                        using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                        {
                            using (StreamReader reader = new StreamReader(cs, Encoding.UTF8))
                            {
                                return reader.ReadToEnd();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Decryption failed: " + ex.Message);
            }
        }
    }
}
