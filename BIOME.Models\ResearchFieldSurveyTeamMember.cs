﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchFieldSurveyTeamMember : Entity<long>,IDescribableEntity
    {
        [Required]
        [MaxLength(66)]
        public string Name { get; set; }
       // [Required]
        public string Identification { get; set; }
    //  [Required]
        public DateTime DOB { get; set; }
        public string ProfileImageName { get; set; }
        [MaxLength(320)]
        public string Email { get; set; } //CR3&CR4 Phase1

        public int AcknowledgeSignStatus { get; set; } //eSign CR 2022
        public DateTimeOffset? AcknowledgeSignDate { get; set; } //eSign CR 2022
        public DateTimeOffset? AcknowledgeSignLinkSentOn { get; set; } //eSign CR 2022
        public DateTimeOffset? AcknowledgeSignLinkExpiredOn { get; set; } //eSign CR 2022
        public string AcknowledgeSignLinkCode { get; set; } //eSign CR 2022
        public int AcknowledgeSignTriedCount { get; set; } //eSign CR 2022
        public long PreviousMemberID { get; set; } //eSign CR 2022

        public string Describe()
        {
            return "{ Name : \"" + Name + "\", Identification : \"" + Identification + "\", DOB : \"" + DOB + "\", ProfileImageName : \"" + ProfileImageName + "\", Email : \"" + Email
                + "\", AcknowledgementSignStatus : \"" + AcknowledgeSignStatus
                + "\", AcknowledgementSignDate : \"" + AcknowledgeSignDate
                + "\", AcknowledgeSignTriedCount : \"" + AcknowledgeSignTriedCount
                + "\", PreviousMemberID : \"" + PreviousMemberID
                + "}";
        }
        [JsonIgnore]
        public virtual ResearchApplication ResearchApplication { get; set; }
        [JsonIgnore]
        public virtual ResearchPermitApplication ResearchPermitApplication { get; set; }
       
    }
}
