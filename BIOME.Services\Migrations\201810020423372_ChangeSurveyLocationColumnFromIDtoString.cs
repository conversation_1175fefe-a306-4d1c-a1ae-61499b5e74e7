namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class ChangeSurveyLocationColumnFromIDtoString : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.SurveyLocations", "Location_Id", "dbo.GISLocations");
            DropIndex("dbo.SurveyLocations", new[] { "Location_Id" });
            AddColumn("dbo.SurveyLocations", "Location", c => c.String());
            DropColumn("dbo.SurveyLocations", "Location_Id");
        }
        
        public override void Down()
        {
            AddColumn("dbo.SurveyLocations", "Location_Id", c => c.Long());
            DropColumn("dbo.SurveyLocations", "Location");
            CreateIndex("dbo.SurveyLocations", "Location_Id");
            AddForeignKey("dbo.SurveyLocations", "Location_Id", "dbo.GISLocations", "Id");
        }
    }
}
