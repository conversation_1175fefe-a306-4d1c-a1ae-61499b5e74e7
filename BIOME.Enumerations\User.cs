﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Enumerations
{
    public class User
    {
        public enum Role
        {
            SystemAdmin,
            PermitManager,
            SiteManager,
            Expert,
            ResourceUploader,
            ProjectAdmin,
            ProjectMember,
            Public
        }

        public enum GroupType
        {
            Main,
            Sub,
            SubSub
        }

        public enum ExternalLoginType
        {
            ActiveDirectory,
            Facebook
        }

        public class API
        {
            public enum FeedbackCategoryType
            {
                General,
                Technical
            }
        }
    }
}
