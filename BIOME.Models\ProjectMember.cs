﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class ProjectMember : Entity<long>,IDescribableEntity 
    {
        public long ProjectId { get; set; }
        [ForeignKey("ProjectId")]
        public virtual ProjectDetail ProjectDetail { get; set; }
        public long MemberId { get; set; } //need index
        [ForeignKey("MemberId")]
        public virtual ApplicationUser Member { get; set; }
        public int MemberRank { get; set; }
        public string Describe()
        {
            return "{ ProjectId : \"" + ProjectId + "\", MemberId : \"" + MemberId + "\", MemberRank : \"" + MemberRank  + "}";
        }
        public ProjectMember()
        {

        }
    }
}
