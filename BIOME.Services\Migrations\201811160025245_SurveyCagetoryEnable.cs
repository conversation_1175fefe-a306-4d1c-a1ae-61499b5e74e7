namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class SurveyCagetoryEnable : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.SurveyCategories", "IsEnable", c => c<PERSON>(nullable: false));
        }
        
        public override void Down()
        {
            DropColumn("dbo.SurveyCategories", "IsEnable");
        }
    }
}
