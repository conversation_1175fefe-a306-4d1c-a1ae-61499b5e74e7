﻿using AutoMapper;
using BIOME.Models;
using BIOME.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public class SiteVisitResolver : ValueResolver<ResearchApplication, ApplicationStatusViewModel.SiteVisitViewModel>
    {
        protected override ApplicationStatusViewModel.SiteVisitViewModel ResolveCore(ResearchApplication source)
        {
            var siteVisitVM = new ApplicationStatusViewModel.SiteVisitViewModel();
            //siteVisitVM.Reports = Mapper.Map<List<ApplicationStatusViewModel.SiteVisitViewModel>>(source.Reports);
            return siteVisitVM;
        }
    }
}
