﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class SightingTaxonomyClassification: Entity<long>,IDescribableEntity 
    {

        public long AtSightingTaxonomyId { get; set; }
        [ForeignKey("AtSightingTaxonomyId")]
        public virtual SightingTaxonomy AtSightingTaxonomy { get; set; }


        public string TaxID { get; set; }
        public string Rank { get; set; }
        public string ScientificName { get; set; }

        public string Describe()
        {
            return "{ AtSightingTaxonomyId : \"" + AtSightingTaxonomyId + "\", TaxID : \"" + TaxID + "\", Rank : \"" + Rank + "\", ScientificName : \"" + ScientificName + "}";
        }

        public SightingTaxonomyClassification()
        {
        }
    }
}
