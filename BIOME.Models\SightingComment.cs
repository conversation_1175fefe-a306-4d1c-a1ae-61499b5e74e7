﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class SightingComment : Entity<long>,IDescribableEntity
    {
        public string CommentString { get; set; }

        public long AtSightingId { get; set; }
        [ForeignKey("AtSightingId")]
        public virtual SightingDetail AtSighting { get; set; }
        
        public long WritenByUserId { get; set; }

        public string Describe()
        {
            return "{ CommentString : \"" + CommentString + "\", AtSightingId : \"" + AtSightingId + "\", WritenByUserId : \"" + WritenByUserId + "}";
        }

        [NotMapped]
        public ApplicationUser WritenByUser { get; set; }

        public SightingComment()
        {

        }
    }
}
