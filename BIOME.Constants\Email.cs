﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Constants
{
    public class Email
    {
        public static string URLBase = @"http://192.168.1.182/";
        public static string url_pub_contact_us = URLBase + @"home/contact_us";
        public static string Contact = "Contact";
        public static string Profile = "Profile";
        public static string SightingDetail = @"Sightings/SightingDetails/AllSightings";
        public static string MySightings = @"Sightings/MySightings/Submitted";
        //public static string SystemAdminEmail = "<EMAIL>";

        public static Dictionary<string, List<string>> EmailTemplateFieldsMap = new Dictionary<string, List<string>>
        {
            { "RESET_PASSWORD", new List<string> { "<person_name>", "<url_pub_reset_password>", "<url_pub_contact_us>" } },
            { "ACCOUNT_PASSWORD_RESET_SUCCESSFULLY", new List<string> { "<person_name>", "<email_technical_support>", "<url_pub_contact_us>" } },
            { "NEW_ACCOUNT_ACTIVATION", new List<string> { "<person_name>", "<email_address>", "<url_pub_email_verification>", "<system_config_password_expiry_period>", "<system_config_account_expiry_period>", "<url_pub_contact_us>" } },
            { "NEW_ACCOUNT_ACTIVATION_WITH_TEMP_PASSWORD", new List<string> { "<person_name>", "<email_address>", "<temp_password>", "<url_pub_email_verification>", "<system_config_password_expiry_period>", "<system_config_account_expiry_period>", "<url_pub_contact_us>" } },
            { "ACCOUNT_LOGIN_OTP", new List<string> { "<person_name>", "<otp_code>", "<otp_expiry_date>", "<url_pub_contact_us>" } },
            { "ACCOUNT_PASSWORD_EXPIRY_NOTIFICATION", new List<string> { "<person_name>", "<password_expiry_date>", "<system_config_password_expiry_period>", "<url_pub_password_reset>", "<url_pub_contact_us>" } },
            { "ACCOUNT_PASSWORD_14-DAYS_BEFORE_EXPIRY_NOTIFICATION", new List<string> { "<person_name>", "<password_expiry_date>", "<system_config_password_expiry_period>", "<url_pub_password_reset>", "<url_pub_contact_us>" } },
            { "ACCOUNT_EXPIRY_NOTIFICATION", new List<string> { "<person_name>", "<account_expiry_date>", "<system_config_account_expiry_period>", "<url_pub_account_reset>", "<url_pub_contact_us>" } },
            { "ACCOUNT_EXPIRY_NOTIFICATION_FROM_SCHEDULER", new List<string> { "<person_name>", "<account_expiry_date>", "<system_config_account_expiry_period>", "<url_pub_account_reset>", "<url_pub_contact_us>" } },
            { "ACCOUNT_14-DAYS_BEFORE_EXPIRY_NOTIFICATION", new List<string> { "<person_name>", "<account_expiry_date>", "<system_config_account_expiry_period>", "<url_pub_account_reset>", "<url_pub_contact_us>" } },
            { "ACCOUNT_EXTENSION_NOTIFICATION", new List<string> { "<person_name>", "<url_Internet_user_login>", "<url_pub_contact_us>" } },
            //{ "ACCOUNT_STATUS_CHANGE", new List<string> { "<user_id>", "<user-name>", "<user_exisitingroles>", "<datesuppend>", "<datedeactive>", "<status>" } },
            //{ "ACCOUNT_STATUS_CHANGE_EXIT", new List<string> { "<user_id>", "<user-name>", "<user_exisitingroles>", "<datesuppend>", "<datedeactive>", "<status>" } },
            { "NOTIFICATION_OF_SIGHTING_VERIFIED", new List<string> { "<person_name>", "<sightingiid_link_url_pub_sightingdetails>", "<url_pub_sighting_details>", "<url_pub_contact_us>", "<sighting_title>" } },
            { "NEW_COMMENT_ON_SIGHTING", new List<string> { "<person_name>", "<commentername_link_url_commenterprofilepage>", "<sightingid_link_url_pub_sightingdetails>", "<comment_details>", "<url_pub_sighting_details>", "<url_pub_contact_us>", "<sighting_id>" } },
            { "CHANGE_OF_SPECIES_NAME_AND_SCIENTIFIC_NAME_DUE_TO_VERIFICATION", new List<string> { "<person_name>", "<sightingid_link_url_pub_sightingdetails>", "<url_pub_sighting_details>", "<url_pub_contact_us>", "<sighting_id>", "<sighting_title>" } },
            { "INAPPROPRIATE_SIGHTING_WARNING_BLACKLISTING_TO_USER", new List<string> { "<person_name>", "<sighting_date_stamp>", "<sighting_time_stamp>", "<sightingtitle_link_sightingdetails>", "<url_pub_contact_us>"} },
            { "INAPPROPRIATE_SIGHTING_WARNING_BLACKLISTING_TO_ADMIN", new List<string> { "<person_name>", "<sighting_date_stamp>", "<sighting_time_stamp>", "<SightingTitle_link_Sightingdetails>", "<sightingsarthorname_link_url_arthorprofilepage >", "<flaggerName_link_url_flaggerprofilepage >", "<url_pri_inappropriate_sighting>", "<url_pub_contact_us>" } },
            { "NOTIFICATION_OF_SIGHTING_CENSORED_AUTO-FILTERING", new List<string> { "<person_name>", "<sightingid_link_url_pub_sightingdetails>", "<url_pub_contact_us>", "<sighting_id>", "<sighting_title>" } },
            { "ACCOUNT_BLACKLISTED", new List<string> { "<person_name>", "<sightings_link_url_mysightings>", "<url_pub_contact_us>" } },
            { "NEW_FOLLOWER", new List<string> { "<person_name>", "<number_of_followers>", "<url_pub_my_profile_page>", "<url_pub_contact_us>" } },
            { "REQUEST_FOR_PROJECT_SENT_TO_SYSTEM_ADMIN", new List<string> { "<person_name>", "<projectname_link_url_pub_projectdetails>", "<project_requestor_name>", "<url_pub_manage_projects>", "<url_pub_contact_us>", "<project_name>" } },
            { "NOTIFICATION_ON_APPROVED/REJECTED_MEMBERSHIP", new List<string> { "<person_name>", "<projectname_link_url_pub_projectdetails>", "<membership_status>", "<if_approved>", "</if_approved>", "<project_manager_name>", "<project_manager_email>", "<url_pub_contact_us>" } },
            { "JOIN_PROJECT_SENT_TO_PROJECT_REQUESTOR", new List<string> { "<person_name>", "<newmembername_link_url_pub_newmemberprofilepage>", "<projectname_link_url_pub_projectdetails>", "<url_pub_contact_us>","<project_id>", "<project_name>" } },
            { "SUSPEND_PROJECT_SENT_TO_PROJECT_ADMIN", new List<string> { "<person_name>", "<projectname_link_url_pub_projectdetails>", "<url_pub_contact_us>", "<project_id>", "<project_name>", "<project_status>", "<url_pub_my_projects>" } },
            { "APPROVAL/REJECTION_OF_PROJECT_SENT_TO_REQUESTER", new List<string> { "<person_name>", "<projectname_link_url_pub_projectdetails>", "<project_status>", "<url_pub_my_projects>", "<url_pub_contact_us>", "<project_id>", "<project_name>" } },
            { "REINSTATE_PROJECT_SENT_TO_PROJECT_ADMIN", new List<string> { "<person_name>", "<projectname_link_url_pub_projectdetails>", "<url_pub_contact_us>", "<project_id>", "<project_name>", "<project_status>", "<url_pub_my_projects>" } },
            { "INVITATION_TO_JOIN_PROJECT_NON-SEARCHABLE-PROJECTS", new List<string> { "<person_name>", "<project_id>", "<projectname_link_url_pub_projectdetails>", "<url_pub_join_project>", "<url_pub_contact_us>" } },

            { "PERMIT_APPLICATION_NOTIFICATION_TO_SITE_MANAGER", new List<string> { "<person_name>", "<applicant_name>", "<url_pri_new_permit_application>", "<url_pub_contact_us>", "<research_permit_title>", "<permit_application_id>","<type_of_application>" } },
            { "RESEARCH_PERMIT_APPLICATION_STATUS_TO_APPLICANT", new List<string> { "<person_name>", "<type_of_application>", "<status>", "<url_pub_my_permit_application>", "<url_pub_contact_us>", "<research_permit_title>", "<permit_application_id>" } },
            { "RESEARCH_PERMIT_APPLICATION_STATUS_TO_SITE_MANAGER/PERMIT_MANAGER", new List<string> { "<person_name>", "<type_of_application>", "<research_permit_title>", "<permit_application_id>", "<status>", "<url_pub_my_permit_application>", "<url_pri_new_permit_application>", "<url_pub_contact_us>" } },
            { "PERMIT_APPLICATION_NOTIFICATION_TO_RESEARCH_PERMIT_MANAGER", new List<string> { "<person_name>", "<type_of_application>", "<applicant_name>", "<new_permit_application_summary>", "<url_pri_new_permit_application>", "<url_pub_contact_us>", "<research_permit_title>" } },
            { "SITE_MANAGER_HAS_APPROVED_THE_APPLICATION_TO_RESEARCH_PERMIT_MANAGER", new List<string> { "<person_name>", "<type_of_application>", "<research_permit_title>", "<site_managers_name>", "<name(s) of site(s)>", "<url_pri_new_permit_application>", "<url_pub_contact_us>", "<permit_application_id>" } },
            { "ISSUANCE_OF_PERMIT_TO_RESEARCH_PERMIT_APPLICANT", new List<string> { "<person_name>", "<permitapplicationid_link_url_permitapplication>", "<url_pub_permit>", "<url_pub_contact_us>", "<url_pub_permit_application>", "<research_permit_title>", "<permit_application_id>" } },
            { "RESEARCH_PERMIT/PASS_AMENDED_TO_APPLICANT", new List<string> { "<type_of_application>", "<research_permit_title>", "<permit_application_id>", "<applicant_name>", "<url_pub_my_permit_application>", "<url_pub_permit>", "<url_pub_my_permit_application>" } },
            { "REMINDER_TO_APPLICANT", new List<string> { "<person_name>", "<permit_expiry_date>", "<url_pub_permit>", "<url_pub_contact_us>", "<research_permit_title>", "<permit_application_id>" } },
            { "REQUEST_FOR_SURVEY_SENT_TO_SYSTEM_ADMIN", new List<string> { "<person_name>", "<survey_name>", "<surveyname_link_url_pub_surveydetails>", "<survey_requestor_name>", "<url_pub_manage_surveys>", "<url_pub_contact_us>" } },
            { "APPROVAL/REJECTION_OF_SURVEY_SENT_TO_REQUESTER", new List<string> { "<person_name>", "<survey_id>", "<survey_status>", "<survey_name>", "<survey_link_url_pub_surveydetails>", "<url_pub_my_surveys>",  "<url_pub_contact_us>" } },
            { "ADMIN_EDIT_OF_SURVEY_SENT_TO_OWNER", new List<string> { "<person_name>", "<survey_name>", "<survey_link_url_pub_surveydetails>", "<url_pub_my_surveys>",  "<url_pub_contact_us>", "<edited_by_sysadmin_name>", "<edited_by_sysadmin_email>" } },
            { "ADMIN_EDIT_OF_SURVEY_SENT_TO_OTHER_ADMIN", new List<string> { "<person_name>", "<survey_name>", "<survey_link_url_pub_surveydetails>", "<url_pub_my_surveys>",  "<url_pub_contact_us>", "<edited_by_sysadmin_name>", "<edited_by_sysadmin_email>" } },
            { "INVITATION_TO_JOIN_PROJECT_NON-SEARCHABLE-SURVEYS", new List<string> { "<person_name>", "<survey_id>", "<surveyname_link_url_pub_surveydetails>", "<url_pub_join_survey>", "<url_pub_contact_us>" } },
            { "NEW_ACTIVITY/REPLY_POSTED_ON_DISCUSSION_FORUM", new List<string> { "<person_name>", "<name_of_user_who_posted>", "<permitapplicationid_link_url_pri_newpermitapplication>", "<post_details>", "<url_pri_discussion_forum_threadxxx>", "<url_pub_contact_us>", "<research_permit_title>", "<permit_application_id>" } },
            { "REQUEST_FOR_SITE_VISIT",new List<string> { "<person_name>", "<permitapplicationid_link_url_pri_permit>", "<applicant_name>", "<site_visit_details>", "<url_pri_site_visit>", "<url_pub_contact_us>", "<research_permit_title>", "<permit_application_id>" } },
            { "REQUEST_UPLOAD_RESOURCE_PERMISSION", new List<string> { "<person_name>", "<name_of_requester>", "<url_pub_my_profile_page>", "<url_user_account_link>" } },
            { "REQUEST_RESOURCE_DOWNLOAD_PERMISSION", new List<string> { "<person_name>", "<name_of_requester>", "<url_pub_my_profile_page>","<MetaDatatitle>", "<datasettitle_link_url_datasetmetadatapage>", "<resourcedocumentfilepermissin_apporve_link>" } },
            { "REQUEST_RESOURCE_DOWNLOAD_PERMISSION_REPLY", new List<string> { "<person_name>",  "<metadatatitle>", "<datasettitle_link_url_datasetmetadatapage>", "<status>" } },
            { "REQUEST_UPLOAD_RESOURCE_PERMISSION_APPROVED", new List<string> { "<person_name>" } },
            { "RESEARCH_PERMIT_EMAIL_REMINDER_TO_SITE_MANAGER", new List<string> { "<person_name>", "<name_of_applicant>", "<url_pub_my_profile_page>","<date_of_application_submitted>","<permit_application_title>", "<url_pri_new_permit_application>", "<url_pub_contact_us>" } },
            { "RESEARCH_PERMIT_EMAIL_REMINDER_TO_PERMIT_MANAGER", new List<string> { "<person_name>", "<research_permit_title>", "<permit_application_submissioin_datetime>", "<url_emailtemplate>", "<url_pub_contact_us>" } },

            { "Notification of failed sighting submission due to virus (for mobile upload)", new List<string> { "<First Name>", "<SightingID_link_url_pub_SightingDetails>", "<sighting_date_stamp>", "<sighting_time_stamp>", "<sighting_title>", "<url_contact_us>" } },
            { "RESEARCH_PERMIT_EMAIL_REMINDER_TO_SITE_MANAGER_PENDING", new List<string> { "<person_name>", "<name_of_applicant>", "<url_pub_my_profile_page>", "<date_of_application_submitted>", "<permit_application_title>", "<url_pri_new_permit_application>", "<url_pub_contact_us>" } },
            { "RESEARCH_PERMIT_EMAIL_REMINDER_TO_SITE_MANAGER_SUPERVISOR", new List<string> { "<title_of_application>", "<date_of_submission>", "<list_of_sites_name>", "<url_pub_contact_us>" } },
            { "INVITE_PRINCIPAL_INVESTIGATOR_TO_JOIN_DISCUSSION_FORUM", new List<string> { "<person_name>", "<research_permit_title>", "<research_permit_url>", "<url_sign_up>" } },
            { "ASSIGNED_MAIN_APPLICANT", new List<string> { "<person_name>","<previous_applicant_name>", "<previous_applicant_email>", "<research_permit_title>", "<research_permit_url>" } },
            { "DELETE_POST_ON_DISCUSSION_FORUM", new List<string> { "<person_name>", "<name_of_user_who_deleted>", "<permitapplicationid_link_url_pri_newpermitapplication>", "<post_details>", "<url_pri_discussion_forum_threadxxx>", "<url_pub_contact_us>", "<research_permit_title>", "<permit_application_id>" } },
            { "RESOURCE_UPLOAD_EMAIL_TO_ADMIN", new List<string> { "<person_name>",  "<metadatatitle>", "<url_resource_metadata>", "<url_resource_approval>", "<uploader_name_with_profile_link>", "<uploader_email>", "<uploader_name>" } },
            { "RESOURCE_UPLOAD_APPROVAL_STATUS_CHANGED", new List<string> { "<person_name>",  "<metadatatitle>", "<url_resource_metadata>", "<status>" } },
            { "RESOURCE_UPLOAD_EMAIL_TO_SITE_MANAGER", new List<string> { "<person_name>",  "<metadatatitle>", "<url_resource_metadata>", "<uploader_name>", "<uploader_name_with_profile_link>", "<research_permit_title>", "<url_permit_application>" } },
            { "RESOURCE_UPLOAD_EMAIL_TO_PERMIT_MANAGER", new List<string> { "<person_name>",  "<metadatatitle>", "<url_resource_metadata>", "<uploader_name>", "<uploader_name_with_profile_link>", "<research_permit_title>", "<url_permit_application>" } },
            //{ "ACCOUNT_STATUS_CHANGE_SUMMARY", new List<string> { "<ace_file_record_count>", "<list_users_not_exist>", "<list_updated_users>", "<list_failed_to_update_users>", "<error_summary>" } },
            { "AUDITLOG_MONTHLY_REPORT", new List<string> { "<person_name>", "<start_date_of_report_period>", "<end_date_of_report_period>", "<month_year_of_report>", "<list_of_sysadmin>" } },
            { "E_ACKNOWLEDGEMENT_FOR_PERMIT_APPLICATION", new List<string> { "<person_name>", "<main_applicant>", "<main_applicant_email>", "<research_permit_title>", "<sign_link>", "<sign_link_expire_date>" } },
            { "E_ACKNOWLEDGEMENT_SIGNED_BY_ALL_MEMBERS_NOTIFICATION_TO_MAIN_APPLICANT", new List<string> { "<person_name>", "<research_permit_title>", "<type_of_application>"} },
            { "ASSIGNED_SURVEY_OWNER", new List<string> { "<person_name>", "<survey_name>", "<url_pub_contact_us>" } },
            { "APPROVAL/REJECTION_OF_AMENDMENT_SURVEY_SENT_TO_MEMBER", new List<string> { "<person_name>", "<survey_id>", "<survey_status>", "<survey_name>", "<url_pub_contact_us>", "<survey_owner_name>", "<survey_owner_email>", "<survey_admin_list>" } },
            { "AMENDMENT_PENDING_OF_SURVEY_SENT_TO_MEMBER", new List<string> { "<person_name>", "<survey_id>", "<survey_status>", "<survey_name>", "<url_pub_contact_us>", "<survey_owner_name>", "<survey_owner_email>", "<survey_admin_list>" } },
            { "AMENDMENT_PENDING_OF_SURVEY_SENT_TO_SYSTEM_ADMIN", new List<string> { "<person_name>", "<survey_id>", "<survey_status>", "<survey_name>", "<url_pub_contact_us>", "<survey_owner_name>", "<survey_owner_email>", "<amendment_submitted_by_name>", "<amendment_submitted_by_email>" } },
            { "USER_ACCOUNT_DISABLEMENT_CAM", new List<string> { "<list_updated_users>","<staff_name>", "<error_summary>" } },
            { "USER_ACCOUNT_REMOVAL_CAM", new List<string> { "<list_updated_users>", "<staff_name>", "<error_summary>" } },
        };


        public static List<string> EmailTemplateFields = new List<string>
        {
            "<First Name>",
            "<user_ID>",
            "<temp_password>",
            "<url_pub_email_verification>",
            "<system_config_password_expiry_period>",
            "<system_config_account_expiry_period>",
            "<url_contact_us>",
            "<password_expiry_date>",
            "<url_pub_password_reset>",
            "<url_pub_contact_us>",
            "<email_technical_support>",
            "<account_expiry_date>",
            "<url_pub_account_reset>",
            "<url_Internet_user_login>",
            "<sighting_date_stamp>",
            "<sighting_time_stamp>",
            "<SightingTitle_link_SightingDetails>",
            "<SightingsArthorName_link_url_ArthorProfilePage >",
            "<FlaggerName_link_url_FlaggerProfilePage >",
            "<URL_pri_inappropriate_sighting>",
            "<sightings_link_url_MySightings>",
            "<applicant_name>",
            "<url_pri_new_permit_application>",
            "<Type_of_application>",
            "<status>",
            "<url_pub_my_permit_application>",
            "<new_permit_application_summary>",
            "<research_permit_title>",
            "<Site_managers_name>",
            "<name(s) of site(s)>",
            "<PermitApplicationID_link_url_PermitApplication>",
            "<url_pub_permit>",
            "<permit_application_submissioin_datetime>",
            "<date_of_last_approval>",
            "<permit_expiry_date>",
            "<name_of_user_who_posted>",
            "<PermitApplicationID_link_url_pri_NewPermitApplication>",
            "<post_details>",
            "<url_pri_discussion_forum_threadxxx>",
            "<PermitApplicationID_link_url_pri_Permit>",
            "<site_visit_details >",
            "<url_pri_site_visit>",
            "<CommenterName_link_url_CommenterProfilePage>",
            "<SightingID_link_url_pub_SightingDetails>",
            "<comment_details>",
            "<url_pub_sighting_details>",
            "<number_of_followers>",
            "<url_pub_my_profile_page>",
            "<sighting_title>",
            "<ProjectName_link_url_pub_ProjectDetails>",
            "<project_requestor_name>",
            "<url_pub_manage_projects>",
            "<Project_Status>",
            "<url_pub_my_projects>",
            "<membership_status>",
            "<if_approved>",
            "</if_approved>",
            "<Project_manager_name>",
            "<Project_manager_email>",
            "<NewMemberName_link_url_pub_NewMemberProfilePage>",
            "<url_pub_join_project>",
            "<RequestorName_link_url_pub_RequestorProfilePage>",
            "<url_pub_user_permission>",
            "<DatasetTitle_link_url_datasetMetadataPage>",
            "<url_resource_upload_page>",
            "<user_group>",
            "<url_upload_history_page>",
            "<uploader_name>",
            "<dataset_description>",
            "<url_pub_reset_password>",
            "<project_ID>",
            "<User_ID>",
            "<Datesuppend>",
            "<DateDeactive>",
            "<Status>",
            "<url_User_Account_Link>",
            "<MetaDatatitle>",
            "<ResourceDocumentFilePermissin_Apporve_Link>",
            "<User_ExistingRoles>",
            "<User_Name>",
            "<url_sign_up>",
            "<start_date_of_report_period>",
            "<end_date_of_report_period>"
        };
    }
}
