﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class SightingFile : Entity<long>,IDescribableEntity
    {
        public string FileName { get; set; }
        public string FileType { get; set; }

        public long OwnerSightingId { get; set; }
        [ForeignKey("OwnerSightingId")]
        public virtual SightingDetail OwnerSighting { get; set; }
        public string Describe()
        {
            return "{ FileName : \"" + FileName + "\", FileType : \"" + FileType + "\", OwnerSightingId : \"" + OwnerSightingId + "}";
        }
        public SightingFile()
        {

        }
    }
}
