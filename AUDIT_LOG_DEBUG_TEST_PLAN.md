# Audit Log Debug Testing Plan

## Overview
This test plan outlines how to verify that the enhanced debugging logs are working correctly and will help identify the root cause of the audit log error when email filtering is used.

## Pre-Testing Setup

### 1. Verify Log Configuration
- Ensure `Web.config` has log4net configuration enabled
- Check that log file path `Logs/log4Net/log.txt` is writable
- Verify log level is set to DEBUG or INFO

### 2. Deploy Enhanced Code
- Deploy the updated files to UAT environment:
  - `BIOMEWebApplication/Areas/Admin/Controllers/AuditTrailController.cs`
  - `BIOME.Services/AuditTrail/AuditTrailService.cs`
  - `BIOME.Services/User/UserService.cs`

## Test Scenarios

### Test 1: Basic Audit Log Access (Baseline)
**Objective**: Verify logging works for normal audit log access

**Steps**:
1. <PERSON><PERSON> as admin user
2. Navigate to Audit Trail page without any filters
3. Check log file for entries like:
   ```
   INFO AuditTrailPage - START: actionid=1, page=1, Email='', PeriodFrom='', PeriodTo='', ipAddress=''
   INFO AuditTrailPage - SUCCESS: Returning view with X items on page 1
   ```

**Expected Result**: Logs show successful page load with item counts

### Test 2: Email Filter - Working Case
**Objective**: Test email filtering with valid email

**Steps**:
1. Navigate to Audit Trail page
2. Enter a valid email address in the email filter
3. Submit the search
4. Check logs for:
   ```
   DEBUG GetAuditTrails - Adding email filter: '<EMAIL>'
   INFO GetAuditTrails - SUCCESS: Retrieved X items
   ```

**Expected Result**: Logs show email filter being applied and results returned

### Test 3: Email Filter - Error Reproduction
**Objective**: Reproduce the original error and capture detailed logs

**Steps**:
1. Navigate to Audit Trail page
2. Enter an email address that previously caused the error
3. Submit the search
4. Monitor logs for ERROR entries

**Expected Log Patterns to Look For**:
```
ERROR AuditTrailPage - CRITICAL ERROR: [error message]
ERROR AuditTrailPage - Stack Trace: [stack trace]
ERROR AuditTrailPage - Error Context - page=1, Email='<EMAIL>'
```

### Test 4: Database Connection Issues
**Objective**: Test error handling for database problems

**Steps**:
1. Temporarily modify connection string or simulate DB issues
2. Try to access audit logs with email filter
3. Check for specific database error logs:
   ```
   ERROR GetAuditTrails - Database Entity Error: [error details]
   ERROR GetAuditTrails - SQL Error: [SQL error with codes]
   ```

### Test 5: Invalid Date Formats
**Objective**: Test date parsing error handling

**Steps**:
1. Manually construct URL with invalid date formats
2. Access audit log page
3. Look for date parsing errors:
   ```
   ERROR AuditTrailPage - Error parsing PeriodFrom 'invalid-date': [error message]
   ```

### Test 6: User Lookup Issues
**Objective**: Test user lookup error handling

**Steps**:
1. Access audit logs that reference non-existent users
2. Check for user lookup warnings:
   ```
   WARN GetUserEmailByID - User not found for ID: 12345
   WARN GetUserByIdList - Missing users for IDs: [1, 2, 3]
   ```

## Log Analysis Guide

### Key Log Patterns to Monitor

#### Success Patterns:
- `INFO [Method] - START:` - Method entry with parameters
- `INFO [Method] - SUCCESS:` - Successful completion
- `DEBUG [Method] - Retrieved X items` - Data retrieval success

#### Warning Patterns:
- `WARN [Method] - [Object] not found` - Missing data
- `WARN [Method] - [Object] returned null` - Null returns

#### Error Patterns:
- `ERROR [Method] - CRITICAL ERROR:` - Unhandled exceptions
- `ERROR [Method] - Database Entity Error:` - Database connection issues
- `ERROR [Method] - SQL Error:` - SQL execution problems

### Log File Monitoring Commands

#### Real-time monitoring:
```bash
tail -f Logs/log4Net/log.txt
```

#### Filter for audit trail errors:
```bash
grep -i "AuditTrail.*ERROR" Logs/log4Net/log.txt
```

#### Filter for specific email:
```bash
grep -i "Email='<EMAIL>'" Logs/log4Net/log.txt
```

## Troubleshooting Common Issues

### Issue 1: No Logs Appearing
**Possible Causes**:
- Log4net not configured properly
- Log file permissions
- Log level set too high

**Solution**: Check Web.config log4net configuration and file permissions

### Issue 2: Logs Too Verbose
**Possible Causes**:
- Debug level set too low

**Solution**: Adjust log level in configuration if needed

### Issue 3: Performance Impact
**Possible Causes**:
- Too much logging affecting performance

**Solution**: Monitor application performance and adjust logging if necessary

## Expected Outcomes

### Successful Debugging Session Should Provide:
1. **Exact error location** - Which method and line caused the issue
2. **Input parameters** - What data was being processed when error occurred
3. **Database state** - Whether database queries succeeded or failed
4. **User context** - Which users were being looked up
5. **Timing information** - When the error occurred in the process flow

### Next Steps After Log Analysis:
1. Identify the specific root cause from logs
2. Implement targeted fix based on findings
3. Test fix in UAT environment
4. Deploy to production with continued monitoring

## Post-Testing Cleanup

### Optional: Reduce Log Verbosity
Once the issue is identified and fixed, consider:
- Reducing DEBUG level logs to INFO level
- Keeping ERROR and WARN level logs for ongoing monitoring
- Archiving detailed debug logs for future reference

This comprehensive logging approach will provide the visibility needed to quickly identify and resolve the audit log email filtering issue.
