﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public abstract class RecordedDate : Entity<long>,IDescribableEntity
    {
        public DateTimeOffset SavedDate { get; set; }
        public string Describe()
        {
            return "{ SavedDate : \"" + SavedDate +  "}";
        }
        public RecordedDate()
        {

        }

        public RecordedDate(DateTimeOffset dateToRecord)
        {
            SavedDate = dateToRecord;
        }
    }

    public class ResearchIssuedDate : RecordedDate 
    {
        [Key, ForeignKey("PermitApplication")]
        public override long Id { get; set; }

        public virtual ResearchPermitApplication PermitApplication { get; set; }

        public ResearchIssuedDate() : base()
        {

        }

        public ResearchIssuedDate(DateTimeOffset dateToRecord) : base(dateToRecord)
        {

        }
    }

    public class ResearchApprovedDate : RecordedDate
    {
        [Key, ForeignKey("PermitApplication")]
        public override long Id { get; set; }

        public virtual ResearchPermitApplication PermitApplication { get; set; }

        public ResearchApprovedDate() : base()
        {

        }

        public ResearchApprovedDate(DateTimeOffset dateToRecord) : base(dateToRecord)
        {

        }
    }
}
