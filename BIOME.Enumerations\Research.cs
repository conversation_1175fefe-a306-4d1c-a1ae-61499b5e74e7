﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Enumerations
{
    public class Research
    {
        public class Permit
        {
            public enum AttachmentType
            {
                Indemnity,
                Misc,
                ProfilePic,
                Report
            }

            public enum ViewType
            {
                Approved,
                Original,
                Amended,
                Latest,
                LatestNonAmended,
                LatestIncludeRejected
            }

            public enum ApplicationType
            {
                New,
                Amendment,
                Renewal
            }

            public enum ApplicationStatus
            {
                Approved,
                Rejected
            }

            public enum AcknowledgeSignStatus
            {
                [Description("Draft")]
                NoStatus=1,
                [Description("Sent")]
                SentLink,
                [Description("Signed")]
                Signed,
                [Description("Locked")]
                LockedLink,
                [Description("Expired Link")]
                ExpiredLink

            }
            public enum AcknowledgeSignUpdateResponse
            {
                InvalidDOB_Email = 1,
                Signed,
                LockedLink,
                AllSigned

            }
            public enum eSignApplicationType
            {
                New=1,
                //Amendment_Renewal,
                Amendment,
                Renewal,
            }
        }
    }
    public static class EnumExtensions
    {
        public static string GetEnumDescription(this Enum value)
        {
            System.Reflection.FieldInfo fi = value.GetType().GetField(value.ToString());

            DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

            if (attributes != null && attributes.Length > 0)
                return attributes[0].Description;
            else
                return value.ToString();
        }
    }
}
