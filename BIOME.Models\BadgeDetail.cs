﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class BadgeDetail : Entity<long>,IDescribableEntity
    {
        public string Title { get; set; }
        public string Subtitle { get; set; }
        public string Description { get; set; }
        public string BadgeCategory { get; set; }
        public int TargetScore { get; set; }
        public string FileName { get; set; }
        public bool IsActive { get; set; }
        public bool IsHidden { get; set; }
        public string Describe()
        {
            return "{ Title : \"" + Title + "\", Subtitle : \"" + Subtitle + "\", Description : \"" + Description + "\", BadgeCategory : \"" 
                + BadgeCategory + "\", TargetScore : \"" + TargetScore + "\", FileName : \"" + FileName + "\", IsActive : \""
                + IsActive + "\", IsHidden : \""
                + IsHidden + "}";
        }
        public BadgeDetail()
        {

        }
    }
}
