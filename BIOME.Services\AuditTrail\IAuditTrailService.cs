﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BIOME.Models;
using BIOME.ViewModels;
using MvcPaging;

namespace BIOME.Services
{
    public interface IAuditTrailService
    {
        string GetActionName(int actionId);
        bool LogAuditTrail(long userId, string IP, int actionId, string module, string eventstring);
        bool LogAuditTrail(long userId, string IP, int actionId, string module, string eventstring, bool fromAPI);
        bool LogAuditTrail(long userId, string Module, string Action, string AdditionalInfo);
        bool LogAuditTrail(long userId, string Module, string Action, string AdditionalInfo,bool fromAPI);
        bool LogAuditTrail(string Module, string Action, string AdditionalInfo);
        List<AuditLog> GetListAuditLog(string Email, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo, string ModuelName);
        IPagedList<AuditTrailLogging>  GetListAuditTrail(int actionid, string Email, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo, string ipAddress
            , int currentIndexPage, int pageSize);
        IPagedList<AuditTrailLogging> GetAuditTrails(string module, string Email, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo, string ipAddress
            , int currentIndexPage, int pageSize);
        string GetIP(bool CheckForward = false);

        //Report
        int GetNewUsersNumber(int RegisterFrom, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo);
        int GetAllUsersNumber();
        int GetLoginNumber(string keyword, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo);
        Dictionary<long, int> GetUsersNumberPerGroup();
        int GetGroupUserNumber(long GroupId);
        List<AuditTrailLogging> GetLoginAfterOfficeHourLogs(DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo);
        List<AuditTrailLogging> GetUnsuccessfulLoginLogs(DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo);
        List<AuditTrailLogging> GetIncorrectOTPLogs(DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo);
        List<AuditTrailLogging> GetAdminActionLogs(List<string> userEmails, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo);
        List<AuditLogAction> GetAuditLogActions();
        string ConstructConCurrentAlertMessageFor1stLogin_BIOME(string source, System.Uri contactUsLink);
    }
}
