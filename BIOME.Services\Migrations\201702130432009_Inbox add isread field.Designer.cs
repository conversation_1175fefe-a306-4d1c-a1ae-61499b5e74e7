// <auto-generated />
namespace BIOME.Services
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class Inboxaddisreadfield : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(Inboxaddisreadfield));
        
        string IMigrationMetadata.Id
        {
            get { return "201702130432009_Inbox add isread field"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
