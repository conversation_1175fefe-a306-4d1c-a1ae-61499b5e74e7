﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=301879

  This file is merged on top of the IIS application host file found at:
  IIS
  C:\Windows\System32\inetsrv\config\applicationHost.config
  IIS Express
  C:\Users\<USER>\Documents\IISExpress\config\applicationhost.config
  -->
<configuration>
  <configSections>
    <!--
	<section name="system.identityModel" type="System.IdentityModel.Configuration.SystemIdentityModelSection, System.IdentityModel, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
	<section name="system.identityModel.services" type="System.IdentityModel.Services.Configuration.SystemIdentityModelServicesSection, System.IdentityModel.Services, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
	-->
    <sectionGroup name="elmah">
      <section name="security" requirePermission="false" type="Elmah.SecuritySectionHandler, Elmah" />
      <section name="errorLog" requirePermission="false" type="Elmah.ErrorLogSectionHandler, Elmah" />
      <section name="errorMail" requirePermission="false" type="Elmah.ErrorMailSectionHandler, Elmah" />
      <section name="errorFilter" requirePermission="false" type="Elmah.ErrorFilterSectionHandler, Elmah" />
    </sectionGroup>
    <section name="glimpse" type="Glimpse.Core.Configuration.Section, Glimpse.Core" />
    <sectionGroup name="nwebsec">
      <!-- For information on how to configure NWebsec please visit: https://docs.nwebsec.com/ -->
      <section name="httpHeaderSecurityModule" type="NWebsec.Modules.Configuration.HttpHeaderSecurityConfigurationSection, NWebsec, Version=4.2.0.0, Culture=neutral, PublicKeyToken=3613da5f958908a1" requirePermission="false" />
    </sectionGroup>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,     log4net" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="apiEncryptionSettings" type="System.Configuration.NameValueSectionHandler, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  </configSections>

  <appSettings>
	 <add key="aspnet:RequestQueueLimitPerSession" value="2147483647" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="elmah.mvc.disableHandler" value="false" />
    <add key="elmah.mvc.disableHandleErrorFilter" value="true" />
    <add key="elmah.mvc.requiresAuthentication" value="false" />
    <add key="elmah.mvc.IgnoreDefaultRoute" value="true" />
    <add key="elmah.mvc.allowedRoles" value="SystemAdmin" />
    <add key="elmah.mvc.allowedUsers" value="*" />
    <add key="elmah.mvc.route" value="elmah" />
    <add key="elmah.mvc.UserAuthCaseSensitive" value="true" />
    <add key="Mode" value="Release" />
    <add key="SiteType" value="Intranet" />
    <add key="SupportEmail" value="<EMAIL>" />
    <add key="EnvironmentPrefix" value="Local" />
    <add key="TemplateDirectory" value="/PDF Templates" />
    <add key="PermitPassLetterOutputDirectory" value="/UploadFiles/Permit/PassLetter" />
    <add key="LDAP" value="nparksnet.nparks.gov.sg" />
    <add key="ElasticseachServer" value="https://*************:9200" />
    <add key="ProtectedFilePath" value="" />	  <!--For PROD. To put all uploadfolders outside web folder-->

	<add key="mavenSearch" value="https://mavenuat.nparks.gov.sg/ESAPI/Search/locations?query=" />
	<add key="arcgisUrl" value="https://apigw-uat.nparks.gov.sg/Biome-Maven/arcgis/" />
    <add key="mavenUser" value="biomeedit" />
    <add key="mavenPass" value="BioME@123" />
    <add key="siteLayer" value="BioME/BioME/MapServer/8" />
    <add key="sightingLayer" value="BioME/BioME_Sightings/FeatureServer/0/" /> <!--https://maven.nparks.gov.sg/arcgis/rest/services/BioME/BioME_Sightings/FeatureServer/0-->
    <add key="researchPointLayer" value="BioME/BioME_ResearchPermit/FeatureServer/0/" />
    <add key="researchPolygonLayer" value="BioME/BioME_ResearchPermit/FeatureServer/2/" />
    <add key="resourceMetadataPointLayer" value="BioME/BioME_Resource_Metadata/FeatureServer/4" />
    <add key="resourceMetadataPolygonLayer" value="BioME/BioME_Resource_Metadata/FeatureServer/2" />
    <add key="resourceMetadataMappingLayer" value="BioME/BioME_Resource_Metadata/FeatureServer/3" />
    <add key="resourceLayer" value="maven/BIOME_RESOURCE_DOCUMENT/MapServer/" />
    <add key="mavenLayer" value="maven/MavenLayer/MapServer/0" />
    <add key="biodivGroup" value="BioDiversity" />
    <add key="enableCSP" value="false" />
    <add key="enableWebDAV" value="false" />
    <add key="webdavUrl" value="" />
    <add key="webdavUsername" value="" />
    <add key="webdavPassword" value="" />
    <add key="skipScheduleOnStartup" value="false" />
    <add key="fbAppId" value="1419542975104074" />
    <add key="fbAppSecret" value="3c83e602ccf45ca2366d9e32e5da5a20" />
    <add key="InternetEmailIP" value="" />
    <add key="IntranetEmailIP" value="" />
    <add key="InternetEmailIPSecure" value="" />
    <add key="IntranetEmailIPSecure" value="" />
    <add key="InternetEmailBaseUrl" value="https://localhost:44301" />
    <add key="IntranetEmailBaseUrl" value="https://localhost:44301" />
    <add key="interUrl" value="https://localhost:44301/" />
    <add key="intraUrl" value="https://localhost:44301/" />
    <add key="ShowResourceSearch" value="true" />

    <add key="ida:ADFSMetadata" value="********************************/biome-adfsmetadata/FederationMetadata/2007-06/FederationMetadata.xml" />
    <add key="ida:Wtrealm" value="https://localhost:44301/Account/adfs_l/" />
    <add key="ElasticsearchUserName" value="elastic" />
    <add key="ElasticsearchPassword" value="elasticpw" />
    <!--<add key="FacebookAPI" value="https://graph.facebook.com" />-->
	  <add key="FacebookAPI" value="https://apigw-uat.nparks.gov.sg/biome-facebookgraph" />
	  <!--<add key="FacebookAPI" value="**********************************************/facebookgraphapi" />-->

	  <!--Only for Internet-->
    <add key="OnemapDeveloperAPI" value="https://www.onemap.gov.sg" />
	 <!--<add key="OnemapDeveloperAPI" value="**********************************************/onemap/" />-->
    <!--Only for Internet-->
    <!--<add key="SnapMapURL" value="https://www.onemap.gov.sg/maps/tiles/Default/{z}/{x}/{y}.png" />-->
	  <!--<add key="SnapMapURL" value="**********************************************/onemap/maps/tiles/Default/{z}/{x}/{y}.png" />-->
	  <!--<add key="SnapMapURL" value="https://apigw-uat.nparks.gov.sg/Biome-Geospace/arcgis/rest/services/MapServices/ONEMAPBASEMAP/MapServer" />-->
	  <add key="SnapMapURL" value="https://www.onemap.gov.sg/maps/tiles/Default/{z}/{x}/{y}.png" />
	<!--<add key="geospaceAPI" value="https://geospace.gov.sg" />-->
	<add key="geospaceAPI" value="********************************/biome-geospace" />
    <add key="IsADFSLogin" value="true" />


    <!--Only for Intranet-->
    <!--
	<add key="ida:ADFSMetadata" value="https://sts.sgnet.gov.sg/FederationMetadata/2007-06/FederationMetadata.xml" />
	<add key="ida:Wtrealm" value="https://biome-staging.nparks.gov.sg:444/Account/adfs_l/" />
	-->
    <!--<add key="SnapMapURL" value="https://maps-{s}.onemap.sg/v3/Default/{z}/{x}/{y}.png" />-->
    <!--<add key="SnapMapURL" value="https://geospace.gov.sg/arcgis/rest/services/MapServices/ONEMAPBASEMAP/MapServer" />-->
    <!--
	  <add key="FacebookAPI" value="http://***********:9001" />
	  <add key="OnemapDeveloperAPI" value="http://***********:9002" />
	  <add key="SnapMapURL" value="http://***********:9003/v3/Default/{z}/{x}/{y}.png" />
	  -->
    <!--<add key="SnapMapURL" value="https://maps-{s}.onemap.sg/v3/Default/{z}/{x}/{y}.png" />-->
    <!--<add key="SnapMapURL" value="https://geospace.gov.sg/arcgis/rest/services/MapServices/ONEMAPBASEMAP/MapServer" />-->

	  <add key="DBAutoTrackLicenseKey" value="JDVZZ7-6OORZB-ZRILYY-YDTH3T-KQWGS" />
	  <add key="DigitalSignatureKey" value="P@sswor0@678" />

	  <add key="GoogleSiteAPI" value="https://www.google.com/recaptcha/api/siteverify" />
	  <add key="GoogleRecaptchaSiteKey" value="6Lfo_p0eAAAAAHZKF-Rb97Aw0g9nASDGtjfvdW3y" />
	  <add key="GoogleRecaptchaSecretKey" value="6Lfo_p0eAAAAAASrDr8ZNmTm3y5zOlTc6HbaxbYV1" />


	  <add key="GoogleRecaptchaServerSideVerify" value="false" />

	  <!--<add key="GoogleSiteAPI" value="http://***********:9006/recaptcha/api/siteverify" />-->
	  <!--<add key="GoogleRecaptchaSiteKey" value="6LeeDp8eAAAAAECj3d6AIs6ENNcesq-KVtIIHsTI" />
	  <add key="GoogleRecaptchaSecretKey" value="6LeeDp8eAAAAAIk-CK5vFYJvbQ1WSGGGOsxBPtXK" />
	  -->

	  <add key="resourceMetadataMultiPointLayer" value="BioME/BioME_Resource_Metadata/FeatureServer/0" />

	  <add key="OnemapLoginEmail" value="<EMAIL>" />
      <add key="OnemapLoginPwd" value="NParks@20233" />
	  <add key="MobileUserSessionExpireInMinutes" value="240" />

	  <add key="GoogleRecaptchaEnterpriseAPI" value="https://recaptchaenterprise.googleapis.com/v1/projects/nparks-sgbioatlas/assessments" />

	  <add key="GoogleRecaptchaEnterpriseAPI" value="http://***********:9004/v1/projects/nparks-sgbioatlas/assessments" />
	  <add key="GoogleRecaptchaEnterpriseAPIKey" value="AIzaSyBb2rvV8LDbTRwrysh9z2BYje5WJ4v_Gz8" />
	  <add key="GoogleRecaptchaEnterpriseSiteKeyAndroid" value="6LeHzxIjAAAAAAYhMndBkdx_B9OkdMnUF1hgd_tx" />
	  <add key="GoogleRecaptchaEnterpriseSiteKeyiOS" value="6Lcw7ikjAAAAABX6iyRrwper423O6PdQ_abQ0qab" />
	  <add key="GoogleRecaptchaEnterpriseServerSideVerify" value="false" />


	  <add key="TurnOffADFSSSO" value="true" />

	  <!--<add key="isGCCStorage" value="false" />-->

	  <add key="UseSendGridEmail" value="false" />
	  <add key="SendGridEmailTestMode" value="false" />
	  <add key="SendGridAPIURL" value="" />
	  <add key="SendGridAPIKey" value="" />
    <add key="SenderEmail" value="<EMAIL>" />
	  <!--<add key="SenderEmail" value="<EMAIL>" />-->



	  <add key="AzurekeyVaultURL" value="" />
	  <add key="AzurekeyVaultDBSecrect" value="testsecret" />
	  <add key="SCIM.API.AccountID" value="0756f5ed04674834a20e9279ade35b5t" />
	  <add key="SCIM.API.SecretKey" value="5a0c1c587f894c9c8e1eb527832bdf49" />
	  <add key="SCIM.API.RequestExpiry" value="10000" /> <!--seconds-->
	  <add key="BIOMEUploadFileRootPathInternet" value="\UploadFiles\" /> <!--For PROD-->
	  <add key="BIOMEUploadFileRootPathIntranet" value="\UploadFiles\" /> <!--For PROD-->
	  <add key="LoginFormRandomKey" value="ac5939efgd33939bk9029kkef73839201923772fglkkk33ffefb99020292fbdkdk293933" />
	  <!-- API Encryption Settings -->
	  <add key="API_Encryption_Key" value="Ws3cW+qXHkKpaI+nNOjDE2S8FpnJoFYIk8OKSCXRq1g=" />
	  <add key="API_Encryption_IV" value="+gsxgKiNjaNfOLci2s3Qpw==" />

  </appSettings>
  <connectionStrings>
    <!-- Encrypt your connection strings (See https://msdn.microsoft.com/en-us/library/dtkwfdky.aspx). -->
	  <!--<add name="DefaultConnection" connectionString="Data Source=Dev1\SQLEXPRESS;Initial Catalog=BIOME-UAT;User id=biome;Password=******;MultipleActiveResultSets=true" providerName="System.Data.SqlClient" />-->
    <!--Server=localhost\MSSQLSERVER01;Database=master;Trusted_Connection=True;-->
    <!-- <add name="DefaultConnection" connectionString="Data Source=TEBS-BDC-0110\MSSQLSERVER01;Initial Catalog=BIOME-UAT;Integrated Security=True;" providerName="System.Data.SqlClient"/> -->
    <add name="DefaultConnection" connectionString="Data Source=localhost,1433;Initial Catalog=BIOME-UAT;User ID=sa;Password=**********;TrustServerCertificate=True;" providerName="System.Data.SqlClient"/>
    <!--<add name="DefaultConnection" connectionString="ItWillGetFromAzureKeyVault" providerName="System.Data.SqlClient" />-->
  </connectionStrings>
  <log4net debug="true">
    <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value=".\\..\Logs\\log4Net\\log.txt" />
      <appendToFile value="true" />
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="20MB" />
      <staticLogFileName value="true" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%-5p %d %5rms %-22.22c{1} %-18.18M - %m%n" />
      </layout>
    </appender>
    <!--
	  <appender name="HangfireLoggerAppender" type="log4net.Appender.RollingFileAppender">
		  <file value=".\\App_Data\\logs\\log11.txt"/>
		  <appendToFile value="true"/>
		  <rollingStyle value="Size"/>
		  <maxSizeRollBackups value="2"/>
		  <maximumFileSize value="2MB"/>
		  <staticLogFileName value="true"/>
		  <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
		  <layout type="log4net.Layout.PatternLayout">
			  <conversionPattern value="%-5p %d %5rms %-22.22c{1} %-18.18M - %m%n"/>
		  </layout>
	  </appender>
	  <logger additivity="false" name="Hangfire">
		  <level value="INFO" />
		  <appender-ref ref="HangfireLoggerAppender" />
	  </logger>-->

    <!-- Specific loggers for AuditTrail debugging -->
    <logger name="BIOMEWebApplication.Areas.Admin.Controllers.AuditTrailController" additivity="false">
      <level value="DEBUG" />
      <appender-ref ref="RollingLogFileAppender" />
    </logger>

    <logger name="BIOME.Services.AuditTrailService" additivity="false">
      <level value="DEBUG" />
      <appender-ref ref="RollingLogFileAppender" />
    </logger>

    <logger name="BIOME.Services.UserService" additivity="false">
      <level value="DEBUG" />
      <appender-ref ref="RollingLogFileAppender" />
    </logger>

    <root>
      <level value="INFO" />
      <appender-ref ref="RollingLogFileAppender" />
    </root>
  </log4net>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5.2" />
      </system.Web>
  -->
  <system.web>
    <authentication mode="None" />
    <caching>
      <outputCacheSettings>
        <!-- outputCacheSettings - Controls how controller actions cache content in one central location.
                                   You can also modify the web configuration file without recompiling your application. -->
        <outputCacheProfiles>
          <!-- Cache the browserconfig.xml route for a day. -->
          <add name="BrowserConfigXml" duration="86400" location="Any" varyByParam="none" />
          <!-- Cache the Atom 1.0 feed route for a day. -->
          <add name="Feed" duration="86400" location="Any" varyByParam="none" />
          <!-- Cache the manifest.json route for a day. -->
          <add name="ManifestJson" duration="86400" location="Any" varyByParam="none" />
          <!-- Cache the opensearch.xml route for a day. -->
          <add name="OpenSearchXml" duration="86400" location="Any" varyByParam="none" />
          <!-- Cache the robots.txt route for a day. -->
          <add name="RobotsText" duration="86400" location="Any" varyByParam="none" />
          <!-- Cache the 400 Bad Request route for a day. -->
          <add name="BadRequest" duration="86400" location="Any" varyByParam="none" />
          <!-- Cache the 403 Forbidden route for a day. -->
          <add name="Forbidden" duration="86400" location="Any" varyByParam="none" />
          <!-- Cache the 405 Method Not Allowed route for a day. -->
          <add name="MethodNotAllowed" duration="86400" location="Any" varyByParam="none" />
          <!-- Cache the 404 Not Found route for a day. -->
          <add name="NotFound" duration="86400" location="Any" varyByParam="none" />
          <!-- Cache the 401 Unauthorized route for a day. -->
          <add name="Unauthorized" duration="86400" location="Any" varyByParam="none" />
          <!-- Cache the 500 Internal Server Error route for a day. -->
          <add name="InternalServerError" duration="86400" location="Any" varyByParam="none" />
        </outputCacheProfiles>
      </outputCacheSettings>
    </caching>
    <!-- debug - Specifies whether to compile retail binaries or debug binaries. This setting is true when the project is built in Debug mode but false in Release mode.
                 This is done using configuration file transforms. Never run in debug mode on your production site. See Web.Release.config for more information. -->
    <!-- enablePrefetchOptimization - Use Windows pre-fetcher (http://en.wikipedia.org/wiki/Prefetcher) to reduce disk-read cost of application start-up.
                                      The pre-fetcher is turned off in Windows Server by default and must be turned on to see the benefits of this feature
                                      (See http://www.asp.net/aspnet/overview/aspnet-and-visual-studio-2012/whats-new#_Toc_perf_6). -->
    <!-- targetFramework - Specifies the version of the .NET Framework that the Web site targets. -->
    <compilation debug="true" enablePrefetchOptimization="true" targetFramework="4.8" />
    <!-- mode - Turn on/off custom error pages. See also httpErrors existingResponse. -->
    <customErrors mode="Off"></customErrors>
    <!-- httpOnlyCookies - Ensure that external script cannot access the cookie. -->
    <!-- requireSSL - Ensure that the cookie can only be transported over HTTPS. -->
	  <!--Todo: Deploy requireSSL="true" sameSite="Lax" in next deployment.-->

    <httpCookies httpOnlyCookies="true" requireSSL="true" sameSite="Lax" />

    <!-- enableVersionHeader - Remove the ASP.NET version number from the response headers. Added security through obscurity. -->
    <!-- executionTimeout - Specifies the maximum number of seconds that a request is allowed to execute before being automatically shut down by ASP.NET.
                            This time-out applies only if the debug attribute in the compilation element is False. To help to prevent shutting down the
                            application while you are debugging, do not set this time-out to a large value. The default is 110 seconds and has
                            been reduced to 30 seconds. -->
    <!-- maxQueryStringLength - The maximum length of the query string, in number of characters. The default is 2048.  If the length of a query string
                                exceeds the size limit, ASP.NET returns an HTTP 400 (Bad Request) status code. This setting is for ASP.NET while the
                                requestLimits maxQueryString setting is for IIS, you need to set both to the same value. -->
    <!-- maxRequestLength - Specifies the limit for the input stream buffering threshold, in KB. This limit can be used to prevent denial of service
                            attacks that are caused, for example, by users posting large files to the server. The default is 4096 (4 MB) and has been
                            reduced to 1024 (1 MB). This setting is for ASP.NET while the requestLimits maxAllowedContentLength setting is for IIS,
                            you need to set both to the same value or the smaller number wins (See http://stackoverflow.com/questions/6327452/which-gets-priority-maxrequestlength-or-maxallowedcontentlength). -->
    <!-- maxUrlLength - The maximum length of the URL, in number of characters. The default is 260. If the length of the request URL (which is the value
                        of the Path property https://msdn.microsoft.com/en-us/library/vstudio/system.web.httprequest.path(v=vs.100).aspx) exceeds the
                        configured size limit, ASP.NET returns an HTTP 400 (Bad Request) status code. This setting is for ASP.NET while the requestLimits
                        maxUrl setting is for IIS, you need to set both to the same value. -->
    <httpRuntime enableVersionHeader="false" executionTimeout="30" maxQueryStringLength="2048" maxRequestLength="2147483647" maxUrlLength="260" targetFramework="4.8" />
    <!-- Glimpse: This can be commented in to add additional data to the Trace tab when using WebForms
        <trace writeToDiagnosticsTrace="true" enabled="true" pageOutput="false"/> --><httpModules>
      <add name="ErrorLog" type="Elmah.ErrorLogModule, Elmah" />
      <add name="ErrorMail" type="Elmah.ErrorMailModule, Elmah" />
      <add name="ErrorFilter" type="Elmah.ErrorFilterModule, Elmah" />
      <add name="Glimpse" type="Glimpse.AspNet.HttpModule, Glimpse.AspNet" />
    </httpModules>
    <!-- machineKey - If your this site is deployed to a server cluster or you are using shared hosting, generate a
                      machine key from IIS and enter it below. The machine key is used to generate MVC anti-forgery tokens
                      among other things (See http://blogs.msdn.com/b/amb/archive/2012/07/31/easiest-way-to-generate-machinekey.aspx)
                      Also don't forget to encrypt your machine key (See https://msdn.microsoft.com/en-us/library/dtkwfdky.aspx). -->
    <!--<machineKey decryptionKey="[YOUR DECRYPTION KEY GOES HERE]"
                validationKey="[YOUR VALIDATION KEY GOES HERE]"/>-->
    <!-- session - Sessions are created and used whenever you use the TempData property in your controllers or actions. They are generally discouraged due to performance concerns but it's up to you if you need it. -->
    <!-- cookieName - Sets the name of the ASP.NET session cookie (Defaults to 'ASP.NET_SessionId'). -->
    <!-- timeout - Specifies the number of minutes a session can be idle before it is abandoned. The default is 20 minutes. This should be kept as low as
                   possible. The longer this time period is, the more time a potential attacker has to make a man-in-the-middle attack and hijack the users
                   session. If you are using SSL/TLS, this is less of a concern. -->
    <!-- <machineKey validationKey="AutoGenerate,IsolateApps" decryptionKey="AutoGenerate,IsolateApps" validation="SHA1" decryption="3DES"/>-->
    <sessionState cookieName="s" timeout="20" />
    <!-- trace - Enable tracing. Navigate to /trace.axd to view detailed request/response information (See https://msdn.microsoft.com/library/1y89ed7z%28VS.71%29.aspx). -->

  <httpHandlers>
            <add path="glimpse.axd" verb="GET" type="Glimpse.AspNet.HttpHandler, Glimpse.AspNet" />
        </httpHandlers><pages>
			<namespaces>
				<add namespace="MvcPaging" />
			</namespaces>
		</pages></system.web>
  <system.webServer>

    <!-- Stop IIS from doing courtesy redirects used to redirect a link to a directory without to a slash
         to one with a slash e.g. /Content redirects to /Content/. This gives a clue to hackers as to the location
         of directories. See http://www.iis.net/configreference/system.webserver/defaultdocument and
         http://www.troyhunt.com/2014/09/solving-tyranny-of-http-403-responses.html
         Note: This will stop IIS from returning the default document when navigating to a folder.
               e.g. navigating to /Folder/ which contains index.html will not return /Folder/index.html.
               This should not be a problem as we are using ASP.NET MVC controllers and actions. -->
    <defaultDocument enabled="false" />
    <!-- Custom error pages. See https://support.microsoft.com/en-us/kb/943891 -->
    <!-- existingResponse - Whether or not to enable custom error pages.
                            PassThrough - Allow the default MVC debug error page showing the full exception to display
                            Replace - Replace any error responses with custom error pages. -->
    <httpErrors errorMode="Custom" existingResponse="PassThrough">
      <!-- Redirect IIS 400 Bad Request responses to the error controllers bad request action. -->
      <remove statusCode="400" />
      <error statusCode="400" responseMode="ExecuteURL" path="/error/badrequest/" />
      <!-- Redirect IIS 401 Unauthorized responses to the error controllers unauthorized action. -->
      <remove statusCode="401" />
      <error statusCode="401" responseMode="ExecuteURL" path="/error/unauthorized/" />
      <remove statusCode="403" />
      <!-- Redirect IIS 403.14 Forbidden responses to the error controllers not found action.
           A 403.14 happens when navigating to an empty folder like /Content and directory browsing is turned off
           See http://www.troyhunt.com/2014/09/solving-tyranny-of-http-403-responses.html -->
      <error statusCode="403" subStatusCode="14" responseMode="ExecuteURL" path="/error/notfound/" />
      <!-- Redirect IIS 403.501 and 403.502 Forbidden responses to a static Forbidden page.
           This happens when someone tries to carry out a Denial of Service (DoS) attack on your site.
           See http://www.iis.net/learn/get-started/whats-new-in-iis-8/iis-80-dynamic-ip-address-restrictions -->
      <error statusCode="403" subStatusCode="501" responseMode="File" path="error\forbidden.html" />
      <error statusCode="403" subStatusCode="502" responseMode="File" path="error\forbidden.html" />
      <!-- Redirect IIS 403 Forbidden responses to the error controllers forbidden action. -->
      <error statusCode="403" responseMode="ExecuteURL" path="/error/forbidden/" />
      <!-- Redirect IIS 404 Not Found responses to the error controllers not found action. -->
      <remove statusCode="404" />
      <error statusCode="404" responseMode="ExecuteURL" path="/error/notfound/" />
      <!-- Redirect IIS 405 Method Not Allowed responses to the error controllers method not allowed action. -->
      <remove statusCode="405" />
      <error statusCode="405" responseMode="ExecuteURL" path="/error/methodnotallowed/" />
      <remove statusCode="500" />
      <!-- Redirect IIS 500.13 Internal Server Error responses to a static Service Unavailable page.
           A 500.13 occurs because the web server is too busy. The amount of traffic exceeds the Web site's configured capacity. -->
      <error statusCode="500" subStatusCode="13" responseMode="File" path="error\serviceunavailable.html" />
      <!-- Redirect IIS 500 Internal Server Error responses to the error controllers internal server error action. -->
      <error statusCode="500" responseMode="ExecuteURL" path="/error/internalservererror/" />
      <!-- Redirect IIS 503 Service Unavailable responses to a static Service Unavailable page. -->
      <remove statusCode="503" />
      <error statusCode="503" responseMode="File" path="error\serviceunavailable.html" />
      <!-- Redirect IIS 504 Gateway Timeout responses to a static Gateway Timeout page. -->
      <remove statusCode="504" />
      <error statusCode="504" responseMode="File" path="error\gatewaytimeout.html" />

    </httpErrors>
    <modules>
      <remove name="FormsAuthentication" />
      <add name="ErrorLog" type="Elmah.ErrorLogModule, Elmah" preCondition="managedHandler" />
      <add name="ErrorMail" type="Elmah.ErrorMailModule, Elmah" preCondition="managedHandler" />
      <add name="ErrorFilter" type="Elmah.ErrorFilterModule, Elmah" preCondition="managedHandler" />
      <add name="Glimpse" type="Glimpse.AspNet.HttpModule, Glimpse.AspNet" preCondition="integratedMode" />
      <add name="NWebsecHttpHeaderSecurityModule" type="NWebsec.Modules.HttpHeaderSecurityModule, NWebsec, Version=4.2.0.0, Culture=neutral, PublicKeyToken=3613da5f958908a1" />
    </modules>
    <handlers>
      <add name="Glimpse" path="glimpse.axd" verb="GET" type="Glimpse.AspNet.HttpHandler, Glimpse.AspNet" preCondition="integratedMode" />
      <!-- By default any URL with a file extension is assumed to be a static file and MVC routing is not used.
           Here we enable MVC routing for opensearch.xml, robots.txt and sitemap.xml files. -->
      <add name="BrowserConfigXml" path="browserconfig.xml" verb="GET" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
      <add name="ManifestJson" path="manifest.json" verb="GET" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
      <add name="OpenSearchXml" path="opensearch.xml" verb="GET" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
      <!--<add name="RobotsText" path="robots.txt" verb="GET" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />-->
      <add name="SitemapXml" path="sitemap.xml" verb="GET" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
      <remove name="TraceHandler-Integrated" />
      <remove name="TraceHandler-Integrated-4.0" />
    <remove name="ExtensionlessUrlHandler-Integrated-4.0" /><remove name="OPTIONSVerbHandler" /><remove name="TRACEVerbHandler" /><add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" /></handlers>
    <!-- httpCompression - GZip compress static file content. Optimized for better performance.
         See http://zoompf.com/blog/2012/02/lose-the-wait-http-compression and
         http://www.iis.net/configreference/system.webserver/httpcompression -->
    <!-- minFileSizeForComp - Reduced to 512 bytes for better compression coverage -->
    <httpCompression directory="%SystemDrive%\inetpub\temp\IIS Temporary Compressed Files" minFileSizeForComp="512" staticCompressionLevel="9" dynamicCompressionLevel="4">
      <scheme name="gzip" dll="%Windir%\system32\inetsrv\gzip.dll" />
      <dynamicTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="message/*" enabled="true" />
        <add mimeType="application/x-javascript" enabled="true" />
        <!-- Compress XML files -->
        <add mimeType="application/xml" enabled="true" />
        <!-- Compress JavaScript files -->
        <add mimeType="application/javascript" enabled="true" />
        <!-- Compress JSON files -->
        <add mimeType="application/json" enabled="true" />
        <!-- Compress SVG files -->
        <add mimeType="image/svg+xml" enabled="true" />
        <!-- Compress RSS feeds -->
        <add mimeType="application/rss+xml" enabled="true" />
        <!-- Compress Atom feeds -->
        <add mimeType="application/atom+xml" enabled="true" />
        <add mimeType="*/*" enabled="false" />
      </dynamicTypes>
      <staticTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="message/*" enabled="true" />
        <add mimeType="application/x-javascript" enabled="true" />
        <add mimeType="application/atom+xml" enabled="true" />
        <add mimeType="application/xaml+xml" enabled="true" />
        <!-- Compress ICO icon files (Note that most .ico files are uncompressed but there are some that can contain PNG compressed images. If you are doing this, remove this line). -->
        <add mimeType="image/x-icon" enabled="true" />
        <!-- Compress XML files -->
        <add mimeType="application/xml" enabled="true" />
        <add mimeType="application/xml; charset=UTF-8" enabled="true" />
        <!-- Compress JavaScript files -->
        <add mimeType="application/javascript" enabled="true" />
        <!-- Compress JSON files -->
        <add mimeType="application/json" enabled="true" />
        <!-- Compress SVG files -->
        <add mimeType="image/svg+xml" enabled="true" />
        <!-- Compress EOT font files -->
        <add mimeType="application/vnd.ms-fontobject" enabled="true" />
        <!-- Compress TTF font files - application/font-ttf will probably be the new correct MIME type. IIS still uses application/x-font-ttf. -->
        <!--<add mimeType="application/font-ttf" enabled="true" />-->
        <add mimeType="application/x-font-ttf" enabled="true" />
        <!-- Compress OTF font files - application/font-opentype will probably be the new correct MIME type. IIS still uses font/otf. -->
        <!--<add mimeType="application/font-opentype" enabled="true" />-->
        <add mimeType="font/otf" enabled="true" />
        <!-- Compress RSS feeds -->
        <add mimeType="application/rss+xml" enabled="true" />
        <add mimeType="application/rss+xml; charset=UTF-8" enabled="true" />
        <add mimeType="*/*" enabled="false" />
      </staticTypes>
    </httpCompression>
    <httpProtocol>
      <customHeaders>
        <!-- X-Powered-By - Remove the HTTP header for added security and a slight performance increase. -->
        <clear />
        <!-- X-UA-Compatible - Ensure that IE and Chrome frame is using the latest rendering mode. Alternatively, use the HTML meta tag X-UA-Compatible "IE=edge" -->
        <add name="X-UA-Compatible" value="IE=edge,chrome=1" />
        <!--<add name="Content-Security-Policy" value="default-src 'self' blob: data: 'unsafe-inline' 'unsafe-eval' https://assets.dcube.cloud/scripts/wogaa.js www.google-analytics.com/analytics.js *.googleapis.com cdn.polyfill.io dev.virtualearth.net getglimpse.com/Api/Version/Check localhost:* ajax.googleapis.com ajax.aspnetcdn.com localhost:*/Content/leaflet/leaflet-measure.js https://assets.adobedtm.com https://*.dcube.cloud *.google.com data: *.facebook.net https://biome-internet-ncodestaging.ntrix.sg *.dcube.cloud *.adobedtm.com *.demdex.net *.faceb…staging.ntrix.sg *.dcube.cloud *.adobedtm.com *.demdex.net *.everesttech.net;frame-src 'self' getresultshub-a.akamaihd.net *.facebook.com;font-src 'self' fonts.gstatic.com netdna.bootstrapcdn.com maxcdn.bootstrapcdn.com;connect-src 'self' localhost:* ws://localhost:* *.arcgis.com *.gov.sg *.facebook.com https://biome-internet-ncodestaging.ntrix.sg *.facebook.net *.demdex.net;form-action 'self' *.facebook.com https://biome-internet-ncodestaging.ntrix.sg *.demdex.net;report-uri /WebResource.axd?cspReport=true;" />
		-->
        <!--<add name="Content-Security-Policy" value="default-src 'self' blob: data: 'unsafe-inline' 'unsafe-eval' https://assets.dcube.cloud/scripts/wogaa.js www.google-analytics.com/analytics.js *.googleapis.com cdn.polyfill.io dev.virtualearth.net getglimpse.com/Api/Version/Check localhost:* ajax.googleapis.com ajax.aspnetcdn.com localhost:*/Content/leaflet/leaflet-measure.js https://assets.adobedtm.com https://*.dcube.cloud *.google.com data: *.facebook.net https://biome-internet-ncodestaging.ntrix.sg *.dcube.cloud *.adobedtm.com *.demdex.net *.faceb…staging.ntrix.sg *.dcube.cloud *.adobedtm.com *.demdex.net *.everesttech.net;frame-src 'self' getresultshub-a.akamaihd.net *.facebook.com;font-src 'self' fonts.gstatic.com netdna.bootstrapcdn.com maxcdn.bootstrapcdn.com;connect-src 'self' localhost:* ws://localhost:* *.arcgis.com *.gov.sg *.facebook.com https://biome-internet-ncodestaging.ntrix.sg *.facebook.net *.demdex.net;form-action 'self' *.facebook.com https://biome-internet-ncodestaging.ntrix.sg *.demdex.net;report-uri /WebResource.axd?cspReport=true;" />
		-->
        <!--<add name="Content-Security-Policy" value="default-src https: 'self' blob: data: 'unsafe-inline' 'unsafe-eval'; object-src 'none';" />-->
        <!--connect-src 'self' https://www.google-analytics.com *.demdex.net;-->
        <!--without FB
		  <remove name="x-frame-options" />
		  <add name="x-frame-options" value="SAMEORIGIN" />
		  <add name="Content-Security-Policy" value="default-src 'self' *.onemap.sg *.onemap.gov.sg http://docs.onemap.sg/ https://geospace.gov.sg *.arcgis.com cdn.polyfill.io *.google-analytics.com *.demdex.net *.adobedtm.com wogadobeanalytics.sc.omtrdc.net *.dcube.cloud *.wogaa.sg  blob: data: 'unsafe-inline' 'unsafe-eval'; object-src 'none';" />
		  <add name="Content-Security-Policy" value="default-src 'self' *.onemap.sg *.onemap.gov.sg http://docs.onemap.sg/ https://geospace.gov.sg *.geospace.gov.sg *.arcgis.com cdn.polyfill.io *.google-analytics.com *.everesttech.net *.demdex.net *.adobedtm.com wogadobeanalytics.sc.omtrdc.net *.dcube.cloud *.wogaa.sg  blob: data: 'unsafe-inline' 'unsafe-eval'; object-src 'self' blob:;" />


		  -->
        <remove name="x-frame-options" />
        <add name="x-frame-options" value="SAMEORIGIN" />
        <!--<add name="Content-Security-Policy" value="default-src 'self' *.facebook.com *.facebook.net *.onemap.sg *.onemap.gov.sg http://docs.onemap.sg/ *.arcgis.com cdn.polyfill.io *.google-analytics.com *.everesttech.net *.demdex.net *.adobedtm.com wogadobeanalytics.sc.omtrdc.net *.dcube.cloud *.wogaa.sg  blob: data: 'unsafe-inline' 'unsafe-eval'; object-src 'self' blob:;" />-->
        <!--<add name="Content-Security-Policy" value="default-src 'self' *.assets.dcube.cloud *.code.jquery.com *.cdn.jsdelivr.net *.  *.facebook.com *.facebook.net *.onemap.sg *.onemap.gov.sg http://docs.onemap.sg/ https://geospace.gov.sg *.geospace.gov.sg *.arcgis.com cdn.polyfill.io *.google-analytics.com *.everesttech.net *.demdex.net *.adobedtm.com wogadobeanalytics.sc.omtrdc.net *.dcube.cloud *.wogaa.sg  blob: data: 'unsafe-inline' 'unsafe-eval'; object-src 'self' blob:;script-src 'self' 'unsafe-inline' 'unsafe-eval' *.apimgmttester.azure-api.net" />-->
        <!--<add name="Content-Security-Policy" value="default-src 'self' https://www.google.com/recaptcha/ https://www.gstatic.com/recaptcha/ https://services.arcgisonline.com https://server.arcgisonline.com *.osm.org https://geocode.arcgis.com *.facebook.com *.facebook.net *.onemap.sg *.onemap.gov.sg http://docs.onemap.sg/ *.arcgis.com cdn.polyfill.io *.google-analytics.com *.everesttech.net *.demdex.net *.adobedtm.com wogadobeanalytics.sc.omtrdc.net *.dcube.cloud *.wogaa.sg  blob: data: 'unsafe-inline' 'unsafe-eval'; object-src 'self' blob:;" />-->

		<add name="Content-Security-Policy" value="default-src 'self' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://www.googletagmanager.com https://apigw-uat.nparks.gov.sg/ https://www.google.com/recaptcha/ https://www.gstatic.com/recaptcha/ https://services.arcgisonline.com https://server.arcgisonline.com *.osm.org https://geocode.arcgis.com *.facebook.com *.facebook.net *.onemap.sg *.onemap.gov.sg http://docs.onemap.sg/ *.arcgis.com https://wwww.google-analytics.com https://www.googletagmanager.com *.everesttech.net *.demdex.net *.adobedtm.com wogadobeanalytics.sc.omtrdc.net *.dcube.cloud *.wogaa.sg blob: data: 'unsafe-inline' 'unsafe-eval'; object-src 'self' blob:;frame-ancestors 'self';" />
		<add name="Strict-Transport-Security" value="max-age=31536000;includeSubDomains; preload" />
	    <remove name="X-Content-Type-Options" />
        <add name="X-Content-Type-Options" value="nosniff" />
		<add name="X-Permitted-Cross-Domain-Policies" value="none" />
		<add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
		<remove name="Cache-Control" />
		<add name="Cache-Control" value="no-cache, no-store, must-revalidate" />
	  </customHeaders>
    </httpProtocol>
    <security>
      <!-- Enable Dynamic IP Security. During a Denial of Service (DoS) attack, a very simple and small 403.501 or 403.502 Forbidden static error page is displayed.
           See http://www.iis.net/configreference/system.webserver/security/dynamicipsecurity -->
      <!-- enableLoggingOnlyMode - Specifies that IIS will log requests from the client that would be rejected without actually rejecting them.
                                   After running your site for a while, and searching the IIS logs for 403.501 and 403.502 errors, set sensible limits below.
                                   Note: Google and Bing make large numbers of requests at times and can look like a DoS attack. -->
      <!-- enableProxyMode - Set this to true if you are behind a proxy. -->
      <!--<dynamicIpSecurity enableLoggingOnlyMode="true">
        -->
      <!-- maxConcurrentRequests - The number of concurrent HTTP connection requests from a client that will result in the client being blocked. -->
      <!--
        <denyByConcurrentRequests enabled="true" maxConcurrentRequests="20" />
        -->
      <!-- maxRequests - The number of requests received from a specific client over a specified period of time that will result in the client being blocked (if the check is enabled). -->
      <!--
        -->
      <!-- requestIntervalInMilliseconds - The period of time (in milliseconds) that is used to determine the request rate for a specific client. This rate is used to determine whether the receive rate exceeds that the maximum specified, resulting in the client being blocked (if the check is enabled). -->
      <!--
        <denyByRequestRate enabled="true" maxRequests="30" requestIntervalInMilliseconds="300" />
      </dynamicIpSecurity>-->
      <requestFiltering>
        <hiddenSegments>
          <add segment="NWebsecConfig" />
        </hiddenSegments>
        <!-- maxAllowedContentLength - Specifies the maximum length of content in a request, in bytes. The default value is 30000000 (~28.6 MB) and has
                                       been reduced to 1048576 (1 MB). This setting is for IIS while the httpRuntime maxRequestLength setting is
                                       for ASP.NET, you need to set both to the same value or the smaller number wins (See http://stackoverflow.com/questions/6327452/which-gets-priority-maxrequestlength-or-maxallowedcontentlength). -->
        <!-- maxQueryString - Specifies the maximum length of the query string, in bytes. The default value is 2048. This setting is for IIS while the
                              httpRuntime maxQueryStringLength setting is for ASP.NET, you need to set both to the same value. -->
        <!-- maxUrl - Specifies maximum length of the URL, in bytes. The default value is 4096. This setting is for IIS while the
                      httpRuntime maxUrlLength setting is for ASP.NET, you need to set both to the same value. -->
        <requestLimits maxAllowedContentLength="2147483647" maxQueryString="2048" maxUrl="4096" />
      </requestFiltering>
    </security>
    <staticContent>
      <!-- clientCache - Enable client caching of static files like images. By default each static file is given an ETag (like a fingerprint).
           See http://www.iis.net/configreference/system.webserver/staticcontent/clientcache -->
      <!-- cacheControlCustom - Set the Cache-Control HTTP header to a public cache so it is cached by the browser and any proxies. -->
      <!-- cacheControlMode=UseMaxAge - Adds a Cache-Control: max-age=<nnn> header to the response based on the value specified in the CacheControlMaxAge attribute. -->
      <!-- cacheControlMaxAge - Set the maximum age to one year. -->
      <!-- setEtag=false - Specifies whether the HTTP ETag header is calculated and set. The ETag header is used for web cache validation,
           and enables a Web server to not have to send a full response if no changes have been made to the content. Disabling ETags
           causes browsers to use the Last-Modified HTTP header to determine if it has the latest version of the file or not.
           Note: if using < IIS8, add the following line to customHeaders <add name="ETag" value=""/> to have the same effect and remove setEtag from here. -->
      <!--<clientCache cacheControlCustom="public" cacheControlMode="UseMaxAge" cacheControlMaxAge="365.00:00:00" setEtag="false" />-->
      <clientCache cacheControlCustom="public" cacheControlMode="UseMaxAge" cacheControlMaxAge="365.00:00:00" />
      <!-- MIME types - Add some missing MIME types and also modify others to be more up to date. -->
      <!-- .js - JavaScript files are served as application/x-javascript in IIS 7.5 and below. Use the correct MIME type of application/javascript. See http://stackoverflow.com/questions/9664282/difference-between-application-x-javascript-and-text-javascript-content-types. -->
      <remove fileExtension=".js" />
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
      <!-- .json - IIS does not have a JSON MIME type by default. -->
      <remove fileExtension=".json" />
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <!-- .rss - IIS does not have a RSS MIME type by default. Add the optional charset to the RSS MIME type. See http://www.rssboard.org/rss-mime-type-application.txt. -->
      <remove fileExtension=".rss" />
      <mimeMap fileExtension=".rss" mimeType="application/rss+xml; charset=UTF-8" />
      <!-- .html - Add the optional charset to the HTML MIME type. -->
      <remove fileExtension=".html" />
      <mimeMap fileExtension=".html" mimeType="text/html; charset=UTF-8" />
      <!-- .xml - Add the optional charset to the XML MIME type. -->
      <remove fileExtension=".xml" />
      <mimeMap fileExtension=".xml" mimeType="application/xml; charset=UTF-8" />
      <!-- HTML5 Video - IIS 7.5 and below does not have these MIME types by default. -->
      <remove fileExtension=".mp4" />
      <mimeMap fileExtension=".mp4" mimeType="video/mp4" />
      <remove fileExtension=".m4v" />
      <mimeMap fileExtension=".m4v" mimeType="video/m4v" />
      <remove fileExtension=".ogg" />
      <mimeMap fileExtension=".ogg" mimeType="video/ogg" />
      <remove fileExtension=".ogv" />
      <mimeMap fileExtension=".ogv" mimeType="video/ogg" />
      <remove fileExtension=".webm" />
      <mimeMap fileExtension=".webm" mimeType="video/webm" />
      <!-- HTML5 Audio - IIS 7.5 and below does not have these MIME types by default. -->
      <remove fileExtension=".oga" />
      <mimeMap fileExtension=".oga" mimeType="audio/ogg" />
      <remove fileExtension=".spx" />
      <mimeMap fileExtension=".spx" mimeType="audio/ogg" />
      <!-- .svgz - IIS 7.5 does not have a compressed SVG MIME type by default. -->
      <remove fileExtension=".svgz" />
      <mimeMap fileExtension=".svgz" mimeType="image/svg+xml" />
      <!-- Web Fonts -->
      <!-- .eot - Served as application/octet-stream. Use the correct MIME type of application/vnd.ms-fontobject. -->
      <remove fileExtension=".eot" />
      <mimeMap fileExtension=".eot" mimeType="application/vnd.ms-fontobject" />
      <!-- .ttf - Served as application/octet-stream. Use the correct MIME type of application/application/x-font-ttf. -->
      <remove fileExtension=".ttf" />
      <mimeMap fileExtension=".ttf" mimeType="application/x-font-ttf" />
      <!-- .ttc - IIS does not have a TTC MIME type by default. -->
      <remove fileExtension=".ttc" />
      <mimeMap fileExtension=".ttc" mimeType="application/x-font-ttf" />
      <!-- .otf - IIS does not have a OTF MIME type by default. -->
      <remove fileExtension=".otf" />
      <mimeMap fileExtension=".otf" mimeType="font/otf" />
      <!-- .woff - Served as font/woff. Use the correct MIME type of application/font-woff. -->
      <remove fileExtension=".woff" />
      <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
      <!-- .woff2 - IIS does not have a WOFF2 MIME type by default. -->
      <remove fileExtension=".woff2" />
      <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
      <!-- .webp - IIS does not have a WEBP MIME type by default. -->
      <remove fileExtension=".webp" />
      <mimeMap fileExtension=".webp" mimeType="image/webp" />
      <!-- .avif - Modern image format for better compression -->
      <remove fileExtension=".avif" />
      <mimeMap fileExtension=".avif" mimeType="image/avif" />
      <!-- .map - Source map files for debugging -->
      <remove fileExtension=".map" />
      <mimeMap fileExtension=".map" mimeType="application/json" />
      <!-- .appcache - IIS does not have a appcache MIME type by default. -->
      <remove fileExtension=".appcache" />
      <mimeMap fileExtension=".appcache" mimeType="text/cache-manifest" />
      <!-- .manifest - IIS does not have a manifest MIME type by default. -->
      <remove fileExtension=".manifest" />
      <mimeMap fileExtension=".manifest" mimeType="text/cache-manifest" />
	  <remove fileExtension=".kml" />
	  <mimeMap fileExtension=".kml" mimeType="application/vnd" />
    </staticContent>
    <validation validateIntegratedModeConfiguration="false" />
    <!-- Enable gzip and deflate HTTP compression. See http://www.iis.net/configreference/system.webserver/urlcompression
         doDynamicCompression - enables or disables dynamic content compression at the site, application, or folder level.
         doStaticCompression - enables or disables static content compression at the site, application, or folder level.
         dynamicCompressionBeforeCache - specifies whether IIS will dynamically compress content that has not been cached.
                                         When the dynamicCompressionBeforeCache attribute is true, IIS dynamically compresses
                                         the response the first time a request is made and queues the content for compression.
                                         Subsequent requests are served dynamically until the compressed response has been
                                         added to the cache directory. Once the compressed response is added to the cache
                                         directory, the cached response is sent to clients for subsequent requests. When
                                         dynamicCompressionBeforeCache is false, IIS returns the uncompressed response until
                                         the compressed response has been added to the cache directory.
                                         Note: This is set to false in Debug mode to enable Browser Link to work when debugging.
                                         The value is set to true in Release mode (See web.Release.config).-->
    <urlCompression doDynamicCompression="true" doStaticCompression="true" dynamicCompressionBeforeCache="true" />
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Autofac" publicKeyToken="17863af14b0044da" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.3.0.0" newVersion="6.3.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Razor" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages.Razor" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Autofac.Integration.Owin" publicKeyToken="17863af14b0044da" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.1.0" newVersion="6.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Cors" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Extensions" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.29.0" newVersion="2.2.29.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Data.SQLite" publicKeyToken="db937bc2d44ff139" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.0.118.0" newVersion="1.0.118.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.1" newVersion="6.0.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Autofac.Integration.WebApi" publicKeyToken="17863af14b0044da" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.1.0.0" newVersion="6.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.1" newVersion="4.0.5.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Azure.Core" publicKeyToken="92742159e12e44c8" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.40.0.0" newVersion="1.40.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="RazorEngine" publicKeyToken="9ee697374c7e744a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.10.0.0" newVersion="3.10.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Cryptography.ProtectedData" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="AngleSharp" publicKeyToken="e83494dcdc6d31ea" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-0.17.1.0" newVersion="0.17.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Collections.Immutable" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.1" newVersion="9.0.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Magick.NET.Core" publicKeyToken="2004825badfa91ec" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-14.6.0.0" newVersion="14.6.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>

    <!-- allowRemoteAccess - Allow remotely connected users to access the elmah page. -->
	<elmah>
    <security allowRemoteAccess="true" />
    <!--<errorLog type="Elmah.SqlErrorLog, Elmah" connectionStringName="DefaultConnection" />-->
    <errorLog type="BIOMEWebApplication.BIOMEElmahErrorLog, BIOMEWebApplication" connectionStringName="DefaultConnection" />
	<errorFilter>
		<test>
		<or>
			<!--<equal binding="Exception.Type" value="System.Web.Mvc.MvcException" type="String" />-->
			<equal binding="HttpStatusCode" value="404" type="Int32" />
			<!--<equal binding="Exception.Type" value="System.ArgumentException" type="String" />-->

		</or>
		</test>
	</errorFilter>
  </elmah>
  <!-- glimpse - Navigate to {your site}/glimpse and turn on Glimpse to see detailed information about your site.
                 (See http://getglimpse.com/ for a video about how this helps with debugging).
                 You can also install add-ons for Glimpse to see even more information. E.g. Install the Glimpse.EF6
                 NuGet package to see your SQL being executed (See http://getglimpse.com/Extensions for all Glimpse extensions).
                 For more information on how to configure Glimpse, please visit http://getglimpse.com/Help/Configuration
                 or access {your site}/glimpse for even more details and a Configuration Tool to support you.
                 Note: To change the glimpse URL, change the value in endpointBaseUri and also the glimpse URL under
                 httpHandlers and handlers sections above. -->
	<glimpse defaultRuntimePolicy="Off" endpointBaseUri="~/glimpse.axd" />
  <!-- glimpse Security - If you want to enable glimpse in your release build. Open Web.Release.config and
                          comment out where we turn off glimpse. Now uncomment the following config to
                          only allow an ASP.NET role with the name Administrator (Or whatever role you want
                          to grant access) access to it. (See http://blog.getglimpse.com/2013/12/09/protect-glimpse-axd-with-your-custom-runtime-policy/
                          for more information). -->
  <!--<location path="glimpse">
    <system.web>
      <authorization>
        <deny users="*"/>
        <allow roles="Administrator"/>
      </authorization>
    </system.web>
  </location>-->
	<!--<location path="UploadFiles/Permit/PassLetter">
		<system.webServer>
			<directoryBrowse enabled="false" />
		</system.webServer>
	</location>-->
    <location path="UploadFiles/Permit/PassLetter">
    <system.webServer>
	    <handlers>
        <add name="DisallowServe" path="*.*" verb="*" type="System.Web.HttpNotFoundHandler" />
        </handlers>
    </system.webServer>
    </location>
    <location path="CertFiles">
    <system.webServer>
	    <handlers>
        <add name="DisallowServe" path="*.*" verb="*" type="System.Web.HttpNotFoundHandler" />
        </handlers>

    </system.webServer>
    </location>
	<location path="camapi">
		<system.webServer>
			<httpErrors errorMode="Custom" existingResponse="PassThrough">
				<remove statusCode="400" />
				<remove statusCode="401" />
				<remove statusCode="403" />
				<remove statusCode="404" />
				<remove statusCode="405" />
				<remove statusCode="500" />
				<remove statusCode="503" />
				<remove statusCode="504" />

			</httpErrors>

		</system.webServer>
	</location>
  <system.net>
    <!--
    <defaultProxy>
      <proxy
          usesystemdefault = "false"
          bypassonlocal="false"
          proxyaddress="http://*************:3128"
       />
    </defaultProxy>
    -->
    <mailSettings>
      <!-- Method#1: Configure smtp server credentials -->
      <smtp from="<EMAIL>">
        <network
          enableSsl="true"
          host="smtp.gmail.com"
          port="587"
          userName="<EMAIL>"
          password="rama kops uxhm iqyr" />
      </smtp>
      <!--<smtp from="">
        <network enableSsl="true" host="smtp.gmail.com" port="587" userName="<EMAIL>" password="valid-password" />
      </smtp>-->
      <!--Method#2: Dump emails to a local directory -->
      <!--<smtp from="<EMAIL>" deliveryMethod="SpecifiedPickupDirectory">
        <network host="localhost" />
        <specifiedPickupDirectory pickupDirectoryLocation="~/App_Data/tmpMail" />
      </smtp>-->

    </mailSettings>
  </system.net>
  <nwebsec>
    <httpHeaderSecurityModule xsi:noNamespaceSchemaLocation="NWebsecConfig/HttpHeaderSecurityModuleConfig.xsd" xmlns="http://nwebsec.com/HttpHeaderSecurityModuleConfig.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
  </nwebsec>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
      <!--<provider invariantName="System.Data.SQLite.EF6" type="System.Data.SQLite.EF6.SQLiteProviderServices, System.Data.SQLite.EF6" />-->
    </providers>
  </entityFramework>
  <system.diagnostics>
    <switches>
      <add name="Microsoft.Owin" value="Verbose" />
    </switches>
    <sharedListeners>
      <add name="console" type="System.Diagnostics.ConsoleTraceListener" />
    </sharedListeners>
    <trace autoflush="true" />
    <sources>
      <source name="Microsoft.Owin">
        <listeners>
          <add name="console" />
        </listeners>
      </source>
      <!-- remove it because uncessarsy and also to fix error This method or property is not supported after HttpRequest.GetBufferlessInputStream has been invoked.
      <source name="Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware">
        <listeners>
          <add name="file" type="System.Diagnostics.TextWriterTraceListener" initializeData="traces-FacebookAuthenticationMiddleware.log" />
        </listeners>
      </source>
	  -->
    </sources>
  </system.diagnostics>
  <system.data>
    <DbProviderFactories>
      <remove invariant="System.Data.SQLite.EF6" />
      <add name="SQLite Data Provider (Entity Framework 6)" invariant="System.Data.SQLite.EF6" description=".NET Framework Data Provider for SQLite (Entity Framework 6)" type="System.Data.SQLite.EF6.SQLiteProviderFactory, System.Data.SQLite.EF6" />
      <remove invariant="System.Data.SQLite" />
      <add name="SQLite Data Provider" invariant="System.Data.SQLite" description=".NET Framework Data Provider for SQLite" type="System.Data.SQLite.SQLiteFactory, System.Data.SQLite" />
    </DbProviderFactories>
  </system.data>
<apiEncryptionSettings>
    <add key="Key" value="Ws3cW+qXHkKpaI+nNOjDE2S8FpnJoFYIk8OKSCXRq1g=" />
    <add key="IV" value="+gsxgKiNjaNfOLci2s3Qpw==" />
  </apiEncryptionSettings>
</configuration>
<!--ProjectGuid: 6DE7E2BC-4AB7-4FD6-BD23-D3100AFA7762-->