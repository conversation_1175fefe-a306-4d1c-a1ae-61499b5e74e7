namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class SurveyChanges : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.SurveyLocationAnswers", "SurveyLocationId", "dbo.SurveyLocations");
            DropForeignKey("dbo.SurveyLocationAnswers", "SurveySubmissionId", "dbo.SurveySubmissions");
            DropForeignKey("dbo.SurveySpeciesAnswers", "SurveySpeciesId", "dbo.SurveySpecies");
            DropIndex("dbo.SurveyLocationAnswers", new[] { "SurveyLocationId" });
            DropIndex("dbo.SurveyLocationAnswers", new[] { "SurveySubmissionId" });
            DropIndex("dbo.SurveySpeciesAnswers", new[] { "SurveySpeciesId" });
            AddColumn("dbo.SurveySubmissions", "SurveyLocationID", c => c.<PERSON>(nullable: false));
            AlterColumn("dbo.Surveys", "ViewPermission", c => c.Int());
            AlterColumn("dbo.Surveys", "DownloadPermission", c => c.Int());
            AlterColumn("dbo.Surveys", "SurveyStatus", c => c.Int());
            AlterColumn("dbo.Surveys", "ApprovedById", c => c.Long());
            AlterColumn("dbo.Surveys", "Duration", c => c.Int());
            AlterColumn("dbo.Surveys", "StartDate", c => c.DateTime());
            AlterColumn("dbo.Surveys", "EndDate", c => c.DateTime());
            AlterColumn("dbo.Surveys", "SurveyCategoryId", c => c.Long());
            AlterColumn("dbo.Surveys", "MemberCount", c => c.Int());
            AlterColumn("dbo.SurveySpeciesAnswers", "SurveySpeciesId", c => c.Long());
            CreateIndex("dbo.SurveySubmissions", "SurveyLocationID");
            CreateIndex("dbo.SurveySpeciesAnswers", "SurveySpeciesId");
            AddForeignKey("dbo.SurveySubmissions", "SurveyLocationID", "dbo.SurveyLocations", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveySpeciesAnswers", "SurveySpeciesId", "dbo.SurveySpecies", "Id");
            DropTable("dbo.SurveyLocationAnswers");
        }
        
        public override void Down()
        {
            CreateTable(
                "dbo.SurveyLocationAnswers",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        SurveyLocationId = c.Long(nullable: false),
                        SurveySubmissionId = c.Long(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            DropForeignKey("dbo.SurveySpeciesAnswers", "SurveySpeciesId", "dbo.SurveySpecies");
            DropForeignKey("dbo.SurveySubmissions", "SurveyLocationID", "dbo.SurveyLocations");
            DropIndex("dbo.SurveySpeciesAnswers", new[] { "SurveySpeciesId" });
            DropIndex("dbo.SurveySubmissions", new[] { "SurveyLocationID" });
            AlterColumn("dbo.SurveySpeciesAnswers", "SurveySpeciesId", c => c.Long(nullable: false));
            AlterColumn("dbo.Surveys", "MemberCount", c => c.Int(nullable: false));
            AlterColumn("dbo.Surveys", "SurveyCategoryId", c => c.Long(nullable: false));
            AlterColumn("dbo.Surveys", "EndDate", c => c.DateTime(nullable: false));
            AlterColumn("dbo.Surveys", "StartDate", c => c.DateTime(nullable: false));
            AlterColumn("dbo.Surveys", "Duration", c => c.Int(nullable: false));
            AlterColumn("dbo.Surveys", "ApprovedById", c => c.Long(nullable: false));
            AlterColumn("dbo.Surveys", "SurveyStatus", c => c.Int(nullable: false));
            AlterColumn("dbo.Surveys", "DownloadPermission", c => c.Int(nullable: false));
            AlterColumn("dbo.Surveys", "ViewPermission", c => c.Int(nullable: false));
            DropColumn("dbo.SurveySubmissions", "SurveyLocationID");
            CreateIndex("dbo.SurveySpeciesAnswers", "SurveySpeciesId");
            CreateIndex("dbo.SurveyLocationAnswers", "SurveySubmissionId");
            CreateIndex("dbo.SurveyLocationAnswers", "SurveyLocationId");
            AddForeignKey("dbo.SurveySpeciesAnswers", "SurveySpeciesId", "dbo.SurveySpecies", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveyLocationAnswers", "SurveySubmissionId", "dbo.SurveySubmissions", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveyLocationAnswers", "SurveyLocationId", "dbo.SurveyLocations", "Id", cascadeDelete: true);
        }
    }
}
