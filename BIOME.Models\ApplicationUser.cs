﻿using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ApplicationUser : IdentityUser<long, BIOMEUserLogin, BIOMEUserRole, BIOMEUserClaim>, IEntity<long>, IDescribableEntity
    {

        [MaxLength(320)]
        public override string Email { get; set; }

        [NotMapped]
        public string FirstName { get; set; }

        [NotMapped]
        public string LastName { get; set; }
        [MaxLength(66)]
        public string PersonName { get; set; }
        public string Organisation { get; set; }
        public string Description { get; set; }
        public DateTimeOffset DateActivate { get; set; }
        public DateTimeOffset DateLastLogin { get; set; }
        public DateTimeOffset DateLastChangePassword { get; set; }
        public bool Blacklisted { get; set; }
        public string ProfilePicImgName { get; set; }
        public virtual IList<PreviousPassword> PreviousUserPasswords { get; set; }
        public bool GeneratedPassword { get; set; }
        public string SessionID { get; set; }
        public bool IsMobileRegistered { get; set; }
        [JsonIgnore]
        public virtual Group Group { get; set; }

        [JsonIgnore]
        [InverseProperty("Owner")]
        public virtual IList<SightingDetail> MySightings { get; set; }
        

        public DateTimeOffset CreatedAt { get; set; }
        public DateTimeOffset UpdatedAt { get; set; }
        [JsonIgnore]
        public virtual IList<ApplicationUser> Following { get; set; }
        [JsonIgnore]
        public virtual IList<ApplicationUser> Followers { get; set; }

        public bool HasNewBadge { get; set; }
        public bool IsSightingNoNotification { get; set; }
        public bool IsActive { get; set; }
        public bool IsFirstLogin { get; set; }
        public bool ForcedInactiveByAdmin { get; set; }
        public bool HasAgreedPermitApplicationTnC { get; set; }

        public string Describe()
        {
            return "{ ID : \"" + Id + "\", Name : \"" +PersonName + "\" }";
        }


        //[NotMapped]
        //public string FullName
        //{
        //    get
        //    {
        //        var namesPart = new List<string>();
        //        if (!string.IsNullOrEmpty(FirstName))
        //        {
        //            namesPart.Add(FirstName);
        //        }
        //        if (!string.IsNullOrEmpty(LastName))
        //        {
        //            namesPart.Add(LastName);
        //        }
        //        return string.Join(" ", namesPart);
        //    }
        //}

        [NotMapped]
        public string getFirstName
        {
            get
            {
                if (PersonName != null) {
                    return PersonName.Split(' ')[0];
                }

                return "";
               
            }
        }
        [NotMapped]
        public string getLastName
        {
            get
            {
                if (PersonName != null)
                {
                   var nameparts = PersonName.Split(' ');
                    if(nameparts.Length > 1)
                    {
                        return nameparts[1];
                    }
                }

                return "";

            }
        }

        [NotMapped]
        public DateTimeOffset DateLastLoginOrActivate => DateActivate > DateLastLogin ? DateActivate : DateLastLogin;

        [NotMapped]
        public DateTimeOffset DateLastChangePasswordActual => DateLastChangePassword == new DateTimeOffset() ? DateActivate : DateLastChangePassword;

        public virtual ICollection<GISLocation> SitesManaging { get; set; }
        public DateTimeOffset LastSuspendDate { get; set; }
        public DateTimeOffset LastDeActivateDate { get; set; }

        public string IV { get; set; }
        public string Key { get; set; }

        public string AccountStatus { get; set; }
        public int LastLoginType { get; set; }
        public ApplicationUser()
        {
            PreviousUserPasswords = new List<PreviousPassword>();
            Following = new List<ApplicationUser>();
            Followers = new List<ApplicationUser>();

            DateActivate = DateTimeOffset.Now;
            CreatedAt = DateTimeOffset.Now;
            UpdatedAt = DateTimeOffset.Now;

            IsActive = false;
            IsFirstLogin = false;
            AccessFailedCount = 0;

            SitesManaging = new List<GISLocation>();
        }

        public async Task<ClaimsIdentity> GenerateUserIdentityAsync(UserManager<ApplicationUser, long> manager)
        {
            // Note the authenticationType must match the one defined in CookieAuthenticationOptions.AuthenticationType
            var userIdentity = await manager.CreateIdentityAsync(this, DefaultAuthenticationTypes.ApplicationCookie);
            
            // Add custom user claims here
            userIdentity.AddClaim(new Claim(ClaimTypes.GivenName, this.getFirstName));
            userIdentity.AddClaim(new Claim(ClaimTypes.Surname, this.getLastName));
            //userIdentity.AddClaim(new Claim(ClaimTypes.GivenName, this.PersonName));
            userIdentity.AddClaim(new Claim(ClaimTypes.Email, this.Email));

            List<Claim> roleClaims = new List<Claim>();

            //if (this.IsFirstLogin)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.IsFirstLogin));
            //}
            //if (this.RoleRights.Administration.Create)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Administration.Create));
            //}
            //if (this.RoleRights.Administration.Read)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Administration.Read));
            //}
            //if (this.RoleRights.Administration.Update)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Administration.Update));
            //}
            //if (this.RoleRights.Administration.Delete)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Administration.Delete));
            //}

            //if (this.RoleRights.Architecture.Create)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Architecture.Create));
            //}
            //if (this.RoleRights.Architecture.Read)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Architecture.Read));
            //}
            //if (this.RoleRights.Architecture.Update)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Architecture.Update));
            //}
            //if (this.RoleRights.Architecture.Delete)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Architecture.Delete));
            //}

            //if (this.RoleRights.Range.Create)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Range.Create));
            //}
            //if (this.RoleRights.Range.Read)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Range.Read));
            //}
            //if (this.RoleRights.Range.Update)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Range.Update));
            //}
            //if (this.RoleRights.Range.Delete)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Range.Delete));
            //}

            //if (this.RoleRights.Configuration.Update)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.Configuration.Update));
            //}

            //if (this.RoleRights.DataImportExport.Update)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.DataImportExport.Update));
            //}

            //if (this.RoleRights.SuperAdmin)
            //{
            //    roleClaims.Add(new Claim(ClaimTypes.Role, Constants.Role.SuperAdmin));
            //}

            userIdentity.AddClaims(roleClaims);

            return userIdentity;
        }

        public async Task<ClaimsIdentity> GenerateUserIdentityAsync(UserManager<ApplicationUser, long> manager, string authenticationType)
        {
            // Note the authenticationType must match the one defined in CookieAuthenticationOptions.AuthenticationType
            var userIdentity = await manager.CreateIdentityAsync(this, authenticationType);
            // Add custom user claims here
            return userIdentity;
        }

        public string GetProfilePicImg()
        {
            var imagename = "/Content/images/profile-default.jpg";
            if (!string.IsNullOrEmpty(ProfilePicImgName))
            {
                imagename = Constants.Configuration.ImagePath.Contents.User.ProfilePic+"/"+ProfilePicImgName;
            }

            return imagename;
        }
    }
}
