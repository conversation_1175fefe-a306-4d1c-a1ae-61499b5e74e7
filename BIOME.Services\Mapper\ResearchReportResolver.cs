﻿using AutoMapper;
using BIOME.Models;
using BIOME.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public class ResearchReportResolver : ValueResolver<ResearchApplication, ApplicationStatusViewModel.DetailsReportViewModel>
    {
        protected override ApplicationStatusViewModel.DetailsReportViewModel ResolveCore(ResearchApplication source)
        {
            var detailsReportVM = new ApplicationStatusViewModel.DetailsReportViewModel();
            detailsReportVM.Reports = Mapper.Map<List<ApplicationStatusViewModel.DetailsReportItemViewModel>>(source.Reports);
            return detailsReportVM;
        }
    }
}
