﻿using AutoMapper;
using BIOME.Models;
using BIOME.Enumerations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public class SightingAutoVerifiedResolver : ValueResolver<SightingDetail, bool>
    {
        protected override bool ResolveCore(SightingDetail source)
        {
            return source.ApprovedById == 0 && source.StatusRank == (int)BIOME.Enumerations.Sighting.StatusRank.Verified;
        }
    }
}
