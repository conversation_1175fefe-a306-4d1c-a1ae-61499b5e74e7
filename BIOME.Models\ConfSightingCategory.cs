﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ConfSightingCategory : Entity<long>,IDescribableEntity
    {
        public string CategoryName { get; set; }
        public string Description { get; set; }
        public int ParentId { get; set; }
        public string SetDefault { get; set; }
        public int TopParentId { get; set; }
        public int BasicCategoryId { get; set; }
        public long UserId { get; set; }

        [MaxLength(320)]
        public string ExpertEmail { get; set; }
        public string CreatedBy { get; set; }
        public string ModifiedBy { get; set; }
        public bool IsEnable { get; set; }

        public string Describe()
        {
            return "{ CategoryName : \"" + CategoryName + "\", Description : \"" + Description + "\", ParentId : \"" + ParentId + "\", SetDefault : \"" + SetDefault
                + "\", TopParentId : \"" + TopParentId + "\", BasicCategoryId : \"" + BasicCategoryId + "\", UserId : \"" + UserId + "\", ExpertEmail : \"" + ExpertEmail
                + "\", CreatedBy : \"" + CreatedBy + "\", ModifiedBy : \"" + ModifiedBy + "\", IsEnable : \"" + IsEnable   + "}";
        }

        [JsonIgnore]
        public virtual ICollection<ResearchApplication> ResearchApplications { get; set; }
        [JsonIgnore]
        public virtual ICollection<ResourceMetaData> ResourceMetaDatas { get; set; }
        [JsonIgnore]
        public virtual ICollection<ResearchPermitApplication> ResearchPermitApplications { get; set; }

        public ConfSightingCategory()
        {
            ResearchApplications = new List<ResearchApplication>();
            ResourceMetaDatas = new List<ResourceMetaData>();
            ResearchPermitApplications = new List<ResearchPermitApplication>();
        }
    }
}
