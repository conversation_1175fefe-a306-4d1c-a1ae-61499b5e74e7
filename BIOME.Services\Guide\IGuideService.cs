﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BIOME.Models;
using BIOME.ViewModels;

namespace BIOME.Services
{
    public interface IGuideService
    {
        long AddGuideDetail(long userId,long Id, string Title, string FileName, string Version, string CheckSum, int FileSize, bool IsEnable);
        bool DelGuideDetail(long userId,long Id);
        bool UpdateGuideDetail(long UserId,long Id, string Title, bool IsEnable);
        bool UpdateGuideDetailFile(long userId, long Id, string FileName, string Version, string CheckSum, int FileSize);
        List<GuideDetail> GetListGuideDetail();

        List<GuideDetail> GetListGuideDetailEnabled();
        GuideDetail GetGuideDetail(long Id);
        void IncrementGuideDownload(long Id);
        Task<bool> UpdateMasterSpeciesStatusByCategory(long CategoryId);
        IQueryable<SurveyMasterSpecies> GetSurveyMasterSpecies();
        Task<SurveyMasterSpecies> GetSurveyMasterSpeciesById(long Id);
        Task<bool> AddSurveyMasterSpecies(SurveyMasterSpecies species);
        Task<bool> UpdateSurveyMasterSpecies(SurveyMasterSpecies species);
    }
}
