﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Constants
{
    public class ErrorCodes
    {
        public const string ResourceName = "ErrorsResource";
        public const string ResourcePrefix = "ErrorCode_";

        public class Account
        {
            public const string ErrorOccured = ResourcePrefix + "1000";
            public const string LoginFailure = ResourcePrefix + "1001";
            public const string PasswordExpired = ResourcePrefix + "1002";
            public const string AccountExpired = ResourcePrefix + "1003";
            public const string EmailInvalid = ResourcePrefix + "1004";
            public const string EmailTaken = ResourcePrefix + "1005";
            public const string UsernameTaken = ResourcePrefix + "1006";
            public const string UsernameInvalidOnlyContainLettersOrDigits = ResourcePrefix + "1007";
            public const string EmailRequired = ResourcePrefix + "1008";
            public const string NameInvalid = ResourcePrefix + "9107";
            public const string NameMaxCharacter = ResourcePrefix + "9108";
            public const string PasswordRequired = ResourcePrefix + "1009";
            public const string PasswordStrengthFailed = ResourcePrefix + "1010";
            public const string IncorrectPassword = ResourcePrefix + "10011";
            public const string PasswordMissingDigit = ResourcePrefix + "10012";
            public const string PasswordMissingLowercase = ResourcePrefix + "10013";
            public const string PasswordMissingNonLetterOrDigit = ResourcePrefix + "1014";
            public const string PasswordMissingUppercase = ResourcePrefix + "1015";
            public const string PasswordTooShort = ResourcePrefix + "1016";
            public const string UserAlreadyInRole = ResourcePrefix + "1017";
            public const string RoleTaken = ResourcePrefix + "1018";
            public const string RoleNameMissing = ResourcePrefix + "1019";
            public const string FirstNameRequired = ResourcePrefix + "1020";
            public const string LastNameRequired = ResourcePrefix + "1021";
            public const string MustAgreeTnC = ResourcePrefix + "1022";
            public const string RegisterSuccess = ResourcePrefix + "1023";
            public const string AccountReactivated = ResourcePrefix + "1024";
            public const string PasswordReused = ResourcePrefix + "1025";
            public const string ResetPasswordSent = ResourcePrefix + "1026";
            public const string EmailSOERequired = ResourcePrefix + "1027";
            public const string OldNewPasswordSame = ResourcePrefix + "1028";
            public const string ChangePasswordSuccess = ResourcePrefix + "1029";
            public const string AccountActivated = ResourcePrefix + "1030";
            public const string ActivationEmailSent = ResourcePrefix + "1031";
            public const string OTPLoginFailure = ResourcePrefix + "1035";

            public const string UserNotFound = ResourcePrefix + "1101";
            public const string InvalidToken = ResourcePrefix + "1102";
            public const string ActivateUserFailed = ResourcePrefix + "1103";
            public const string AccountLocked = ResourcePrefix + "1104";
            public const string AccountRequiresVerification = ResourcePrefix + "1105";
            public const string ExternalLoggedIn = ResourcePrefix + "1106";
            public const string FailedLinkFB = ResourcePrefix + "1107";
            public const string AccountNotActivated = ResourcePrefix + "1108";

            public const string CannotFollowSelf = ResourcePrefix + "1201";

            public const string NameRequired = ResourcePrefix + "9207";
        }

        public class Research
        {
            public const string PermitAcceptFailed = ResourcePrefix + "3101";
        }

        public class Others
        {
            public const string UpdateSuccess = ResourcePrefix + "9000";
            public const string NoChanges = ResourcePrefix + "9001";
            public const string NParksUpdateFail = ResourcePrefix + "9002";
            public const string GovUpdateFail = ResourcePrefix + "9003";
            public const string TitleUpdateFail = ResourcePrefix + "9004";
            public const string SGBioAtlasUpdateFail = ResourcePrefix + "9005";
            public const string FooterUpdateFail = ResourcePrefix + "9006";
            public const string BackgroundImgUpdateFail = ResourcePrefix + "9007";
            public const string HighlightAddFail = ResourcePrefix + "9008";
            public const string HighlightUpdateFail = ResourcePrefix + "9009";
            public const string HighlightNotFound = ResourcePrefix + "9010";
            public const string SystemParamsUpdateFail = ResourcePrefix + "9011";

            public const string UrlFormatInvalid = ResourcePrefix + "9100";
            public const string PhoneFormatInvalid = ResourcePrefix + "9101";
            public const string PasswordConfirmPasswordNotMatch = ResourcePrefix + "9102";
            public const string FileSizeExceed = ResourcePrefix + "9103";
            public const string MaxCharExceed = ResourcePrefix + "9104";
            public const string StatusIsRequired = ResourcePrefix + "9105";
            public const string RecipentRequired = ResourcePrefix + "9106";
            public const string EmailMaxCharExceed = ResourcePrefix + "9206";
        }
    }
}
