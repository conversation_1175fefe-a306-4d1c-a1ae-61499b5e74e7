﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Constants
{
    public class Badge
    {
        public static Dictionary<string, string> BadgeCategoryData = new Dictionary<string, string>
        {
            { "Amphibian", "sightings-amphibian" },
            { "Arachnid / Insect", "sightings-arachnid-/-insect"},
            { "Bird", "sightings-bird"},
            { "Fish", "sightings-fish"},
            { "Mammal", "sightings-mammal"},
            { "Mollusc", "sightings-mollusc"},
            { "Reptile", "sightings-reptile"},
            { "Fungi", "sightings-fungus"},
            //{ "Algae", "sightings-plants"},
            { "Aquatic Plants", "sightings-plants"},
            { "Creepers / Climbers", "sightings-plants"},
            { "Cycads", "sightings-plants"},
            { "Epiphytes", "sightings-plants"},
            { "Ferns & Fern Allies", "sightings-plants"},
            { "Hemiepiphytes", "sightings-plants"},
            { "Herbs", "sightings-plants"},
            { "Mosses", "sightings-plants"},
            { "Other Plants", "sightings-plants"},
            { "Palms", "sightings-plants"},
            { "Shrubs", "sightings-plants"},
            { "Trees", "sightings-plants"},
        };

        public static Dictionary<long, string> BadgeProjectData = new Dictionary<long, string>
        {
            { 10, "project-community-garden-bird-count" },
            { 11, "project-npark-butterfly-count-2015" },
            { 13, "project-greening-schools-for-biodiversity" },
        };

        public static Dictionary<string, string> BadgeSpecialDayData = new Dictionary<string, string>
        {
            { "0809", "special-national-day" },
            { "0522", "special-international-day-biological-diversity" },
            { "0422", "special-earth-day" },
        };

        /*
        public static List<BadgeLocation> BadgeLocationData = new List<BadgeLocation>
        {
            new BadgeLocation{ BadgeCategory = "submission-central-catchment-nature-reserve" , LatitudeFrom = 1.337203, LatitudeTo = 1.417477, LongitudeFrom = 103.773618, LongitudeTo = 103.838350},
            new BadgeLocation{ BadgeCategory = "submission-labrador-nature-reserve" , LatitudeFrom = 1.262321, LatitudeTo = 1.270153, LongitudeFrom = 103.799147, LongitudeTo = 103.806981},
            new BadgeLocation{ BadgeCategory = "submission-sungei-buloh-nature-reserve" , LatitudeFrom = 1.432562, LatitudeTo = 1.451610, LongitudeFrom = 103.714496, LongitudeTo = 103.737284},
            new BadgeLocation{ BadgeCategory = "submission-bukit-timah-nature-reserve" , LatitudeFrom = 1.341245, LatitudeTo = 1.364948, LongitudeFrom = 103.767612, LongitudeTo = 103.788001},
            new BadgeLocation{ BadgeCategory = "submission-singapore-botanic-gardens" , LatitudeFrom = 1.306676, LatitudeTo = 1.323308, LongitudeFrom = 103.812447, LongitudeTo = 103.819736},
        };*/
        public static List<BadgeLocation> BadgeLocationData = new List<BadgeLocation>
        {
            new BadgeLocation{ BadgeCategory = "submission-central-catchment-nature-reserve" , PolygonPoints = new Point[]{ new Point { x=1.410959, y=103.770589}, new Point { x=1.347978, y=103.789987 }, new Point { x = 1.335272, y = 103.817506 }, new Point { x = 1.342553, y = 103.837412 }, new Point { x = 1.385025, y = 103.825760 }, new Point { x = 1.417060, y = 103.805610 } } },
            new BadgeLocation{ BadgeCategory = "submission-labrador-nature-reserve" , PolygonPoints = new Point[]{ new Point { x=1.267724, y=103.798519 }, new Point { x = 1.262132, y = 103.805159 }, new Point { x = 1.265632, y = 103.807252 }, new Point { x = 1.270539, y = 103.800612 } } },
            new BadgeLocation{ BadgeCategory = "submission-sungei-buloh-nature-reserve" , PolygonPoints = new Point[]{ new Point { x=1.450990, y=103.709427 }, new Point { x = 1.431653, y = 103.718016 }, new Point { x = 1.439301, y = 103.740390 }, new Point { x = 1.452712, y = 103.732523 } }},
            new BadgeLocation{ BadgeCategory = "submission-bukit-timah-nature-reserve" , PolygonPoints = new Point[]{ new Point { x=1.363440, y=103.764714 }, new Point { x = 1.340272, y = 103.777503 }, new Point { x = 1.348853, y = 103.792866 }, new Point { x = 1.366872, y = 103.780593 } }},
            new BadgeLocation{ BadgeCategory = "submission-singapore-botanic-gardens" , PolygonPoints = new Point[]{ new Point { x=1.323365, y=103.814015 }, new Point { x = 1.312753, y = 103.812331 }, new Point { x = 1.306885, y = 103.815597 }, new Point { x = 1.306834, y = 103.818914 }, new Point { x = 1.322549, y = 103.819833 } }},
        };
    }

    public class BadgeLocation
    {
        public string BadgeCategory { get; set; }
        public Point[] PolygonPoints { get; set; }
    }

    public class Point
    {
        public double x { get; set; }
        public double y { get; set; }
    }
}
