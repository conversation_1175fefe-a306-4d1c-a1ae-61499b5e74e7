﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
   public class SurveyLocation:Entity<long>, IDescribableEntity
    {
        public string Location { get; set; }
        public string Remarks { get; set; }
        //public bool IsModified { get; set; }
        public bool IsRemoved { get; set; }
        //  public virtual GISLocation Location { get; set; }

        [JsonIgnore]
        public virtual Survey Survey { get; set; }
        public string Describe()
        {
            return "{ Location : \"" + Location + "\", Remarks : \"" + Remarks + "\", Survey : \"" + Survey?.Id + "}";
        }
        public SurveyLocation()
        {
            
        }
    }
}
