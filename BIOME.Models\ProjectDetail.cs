﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class ProjectDetail : Entity<long>,IDescribableEntity
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public int ViewPermission { get; set; }
        public int DownloadPermission { get; set; }
        public bool IsAutoApproveM { get; set; }
        public bool IsSearchable { get; set; }
        public bool IsActive { get; set; }
        public string ProjectCategoryName { get; set; }
        public int ProjectStatus { get; set; }
        public long ApprovedById { get; set; }
        public string Message { get; set; }

        public long OwnerId { get; set; }
        [NotMapped]
        public ApplicationUser Owner { get; set; }
        public int MemberCount { get; set; }
        public int PendingCount { get; set; }
        public int SightingCount { get; set; }
        public bool IsSpecialProject { get; set; }

        public string LogoName { get; set; }
        public string LogoPath { get; set; }

        public string Describe()
        {
            return "{ Title : \"" + Title + "\", Description : \"" + Description + "\", ViewPermission : \"" + ViewPermission + "\", DownloadPermission : \"" + DownloadPermission
                + "\", IsAutoApproveM : \"" + IsAutoApproveM + "\", IsSearchable : \"" + IsSearchable + "\", IsActive : \"" + IsActive + "\", ProjectCategoryName : \"" + ProjectCategoryName
                + "\", ProjectStatus : \"" + ProjectStatus + "\", ApprovedById : \"" + ApprovedById + "\", Message : \"" + Message
                + "\", OwnerId : \"" + OwnerId + "\", MemberCount : \"" + MemberCount + "\", PendingCount : \"" + PendingCount + "\", SightingCount : \"" + SightingCount
                + "\", IsSpecialProject : \"" + IsSpecialProject + "\", LogoName : \"" + LogoName + "\", LogoPath : \"" + LogoPath + "}";
        }


        public virtual List<ProjectMember> ProjectMembers { get; set; }
        public virtual List<ProjectSighting> ProjectSightings { get; set; }

        public ProjectDetail()
        {
            IsSpecialProject = false;
            MemberCount = 0;
            PendingCount = 0;
            SightingCount = 0;

            ProjectMembers = new List<ProjectMember>();
            ProjectSightings = new List<ProjectSighting>();
        }

        public string GetLogoImage()
        {
            var imagename = "/Content/images/nophotoavailable.jpg";
            if (!string.IsNullOrEmpty(LogoName))
            {
                imagename = LogoPath + LogoName;
            }

            return imagename;
        }
    }
}
