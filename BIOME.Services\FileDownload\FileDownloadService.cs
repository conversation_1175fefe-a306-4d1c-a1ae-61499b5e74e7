﻿using BIOME.Enumerations;
using BIOME.Models;
using BIOME.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;

namespace BIOME.Services
{
    public class FileDownloadService : IFileDownloadService
    {
        public const int defaultLengthMap = 60;
        public const int defaultPadLength = 30;
        private readonly IUserQueryService userRetrieveService;        

        public FileDownloadService(IUserQueryService userRetrieveService)
        {
            this.userRetrieveService = userRetrieveService;
        }

        private static byte[] GetBytes(string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                return new byte[0];
            }
            byte[] bytes = new byte[str.Length * sizeof(char)];
            System.Buffer.BlockCopy(str.ToCharArray(), 0, bytes, 0, bytes.Length);
            return bytes;
        }

        private static string GetString(byte[] bytes)
        {
            char[] chars = new char[bytes.Length / sizeof(char)];
            System.Buffer.BlockCopy(bytes, 0, chars, 0, bytes.Length);
            return new string(chars);
        }

        public string GenerateFileDownloadToken(FileDownloadViewModel fileDownloadVM)
        {
            string token = "";
            try
            {
                byte[] _expiredDate = BitConverter.GetBytes(fileDownloadVM.ExpiredDate.Date.ToBinary());
                byte[] _fileInfo = GetBytes(fileDownloadVM.DownloadFileInfo);
                byte[] _fileType = GetBytes(fileDownloadVM.FileType.ToString());
                byte[] _accessRight = GetBytes(fileDownloadVM.AccessRight);
                byte[] _fileName = GetBytes(fileDownloadVM.FileName);
                byte[] _contentType = GetBytes(fileDownloadVM.ContentType);
                byte[] _resourceDocId = GetBytes(fileDownloadVM.ResourceDocumentID);
                byte[] _userIds = GetBytes(fileDownloadVM.userIDs);
                string lengthMap = _expiredDate.Length.ToString() + "," +
                                   _fileType.Length.ToString() + "," +
                                   _accessRight.Length.ToString() + "," +
                                   _fileInfo.Length.ToString() + "," +
                                   _fileName.Length.ToString() + "," +
                                   _contentType.Length.ToString() +","+
                                   _resourceDocId.Length.ToString() + ","+
                                   _userIds.Length.ToString();

                lengthMap = lengthMap.PadRight(defaultPadLength, ' ');

                byte[] _lengthMap = GetBytes(lengthMap);

                byte[] data = new byte[_lengthMap.Length + _expiredDate.Length + _fileType.Length + _accessRight.Length + _fileInfo.Length + _fileName.Length + _contentType.Length + _resourceDocId.Length + _userIds.Length ];

                System.Buffer.BlockCopy(_lengthMap, 0, data, 0, defaultLengthMap);
                System.Buffer.BlockCopy(_expiredDate, 0, data, defaultLengthMap, _expiredDate.Length);
                System.Buffer.BlockCopy(_fileType, 0, data, defaultLengthMap + _expiredDate.Length, _fileType.Length);
                System.Buffer.BlockCopy(_accessRight, 0, data, defaultLengthMap + _expiredDate.Length + _fileType.Length, _accessRight.Length);
                System.Buffer.BlockCopy(_fileInfo, 0, data, defaultLengthMap + _expiredDate.Length + _fileType.Length + _accessRight.Length, _fileInfo.Length);
                System.Buffer.BlockCopy(_fileName, 0, data, defaultLengthMap + _expiredDate.Length + _fileType.Length + _accessRight.Length + _fileInfo.Length, _fileName.Length);
                System.Buffer.BlockCopy(_contentType, 0, data, defaultLengthMap + _expiredDate.Length + _fileType.Length + _accessRight.Length + _fileInfo.Length + _fileName.Length, _contentType.Length);
                System.Buffer.BlockCopy(_resourceDocId, 0, data, defaultLengthMap + _expiredDate.Length + _fileType.Length + _accessRight.Length + _fileInfo.Length + _fileName.Length+  _contentType.Length, _resourceDocId.Length );
                System.Buffer.BlockCopy(_userIds, 0, data, defaultLengthMap + _expiredDate.Length + _fileType.Length + _accessRight.Length + _fileInfo.Length + _fileName.Length+ _contentType.Length + _resourceDocId.Length,_userIds.Length);

                token = Convert.ToBase64String(data.ToArray());
            }
            catch (Exception ex)
            {
                return token;
            }

            return token;
        }

        public FileDownloadViewModel CheckFileDownloadToken(string token, long userId)
        {
            FileDownloadViewModel fileDownloadVM = new FileDownloadViewModel();

            try
            {
                fileDownloadVM.IsValid = true;

                if (string.IsNullOrEmpty(token))
                {
                    fileDownloadVM.IsValid = false;
                    fileDownloadVM.InvalidReason = "Empty Token String.";
                }

                byte[] data = Convert.FromBase64String(token);
                byte[] _lengthMap = data.Take(defaultLengthMap).ToArray();

                string lengthMap = GetString(_lengthMap);

                string[] lengthMapList = lengthMap.Split(',');

                int expiredDateLength = 0;
                int fileInfoLength = 0;
                int fileTypeLength = 0;
                int accessRightLength = 0;
                int fileNameLength = 0;
                int contentTypeLength = 0;
                int resourceDocLength = 0;
                int userIDsLength = 0;

                if (lengthMapList.Count() == 8)
                {
                    expiredDateLength = Convert.ToInt32(lengthMapList[0]);
                    fileTypeLength = Convert.ToInt32(lengthMapList[1]);
                    accessRightLength = Convert.ToInt32(lengthMapList[2]);
                    fileInfoLength = Convert.ToInt32(lengthMapList[3]);
                    fileNameLength = Convert.ToInt32(lengthMapList[4]);
                    contentTypeLength = Convert.ToInt32(lengthMapList[5]);
                    resourceDocLength = Convert.ToInt32(lengthMapList[6]);
                    userIDsLength = Convert.ToInt32(lengthMapList[7]);
                }

                byte[] _expiredDate = data.Skip(defaultLengthMap).Take(expiredDateLength).ToArray();
                byte[] _fileType = data.Skip(defaultLengthMap + expiredDateLength).Take(fileTypeLength).ToArray();
                byte[] _accessRight = data.Skip(defaultLengthMap + expiredDateLength + fileTypeLength).Take(accessRightLength).ToArray();
                byte[] _fileInfo = data.Skip(defaultLengthMap + expiredDateLength + fileTypeLength + accessRightLength).Take(fileInfoLength).ToArray();
                byte[] _fileName = data.Skip(defaultLengthMap + expiredDateLength + fileTypeLength + accessRightLength + fileInfoLength).Take(fileNameLength).ToArray();
                byte[] _contentType = data.Skip(defaultLengthMap + expiredDateLength + fileTypeLength + accessRightLength + fileInfoLength + fileNameLength).Take(contentTypeLength).ToArray();
                byte[] _resourceDocId = data.Skip(defaultLengthMap + expiredDateLength + fileTypeLength + accessRightLength + fileInfoLength + fileNameLength + contentTypeLength).Take(resourceDocLength).ToArray();
                byte[] _userIds = data.Skip(defaultLengthMap + expiredDateLength + fileTypeLength + accessRightLength + fileInfoLength + fileNameLength + contentTypeLength + resourceDocLength).Take(userIDsLength).ToArray();

                DateTime expiredDate = DateTime.FromBinary(BitConverter.ToInt64(_expiredDate, 0));
                string fileInfo = GetString(_fileInfo);
                string fileType = GetString(_fileType);
                string accessRight = GetString(_accessRight);
                string fileName = GetString(_fileName);
                string contentType = GetString(_contentType);
                string resourceDocId = GetString(_resourceDocId);
                string userIds = GetString(_userIds);
                #region "Validate Token"

                if (expiredDate > DateTime.Now)
                {
                    fileDownloadVM.ExpiredDate = expiredDate;
                }
                else
                {
                    //expired
                    fileDownloadVM.ExpiredDate = expiredDate;
                    fileDownloadVM.IsValid = false;
                    fileDownloadVM.InvalidReason = "Expired Token String.";
                }

                if (!string.IsNullOrEmpty(fileInfo))
                {
                    fileDownloadVM.DownloadFileInfo = fileInfo;
                }
                else
                {
                    fileDownloadVM.DownloadFileInfo = fileInfo;
                    fileDownloadVM.IsValid = false;
                    fileDownloadVM.InvalidReason = "Empty File Info.";
                }

                if (!string.IsNullOrEmpty(fileName))
                {
                    fileDownloadVM.FileName = fileName;
                }
                else
                {
                    fileDownloadVM.FileName = fileName;
                    fileDownloadVM.IsValid = false;
                    fileDownloadVM.InvalidReason = "Empty File Name.";
                }

                if (!string.IsNullOrEmpty(contentType))
                {
                    fileDownloadVM.ContentType = contentType;
                }

                if (!string.IsNullOrEmpty(fileType))
                {
                    FileDownload.FileType parseFileType;
                    Enum.TryParse(fileType, out parseFileType);

                    fileDownloadVM.FileType = parseFileType;
                }
                else
                {
                    //fileDownloadVM.FileType =  fileType;
                    fileDownloadVM.IsValid = false;
                    fileDownloadVM.InvalidReason = "Empty File Type Info.";
                }

                fileDownloadVM.AccessRight = accessRight;
                if (!string.IsNullOrEmpty(resourceDocId))
                {
                    fileDownloadVM.ResourceDocumentID = resourceDocId;
                }
                if (!string.IsNullOrEmpty(userIds))
                {
                    fileDownloadVM.userIDs = userIds;
                }
                if (!IsValidForDownload(userId, accessRight, fileDownloadVM.FileType,userIds))
                {
                    fileDownloadVM.IsValid = false;
                    fileDownloadVM.InvalidReason = "You do not have permission to download this file. To download file, please click on the request button below.";
                }

                #endregion "Validate Token"
            }
            catch (Exception ex)
            {
                fileDownloadVM.IsValid = false;
                fileDownloadVM.InvalidReason = "Invalid Token String.";

                return fileDownloadVM;
            }

            return fileDownloadVM;
        }

        private bool IsValidForDownload(long userId, string accessRight, FileDownload.FileType fileType,string accessUserRight)
        {
            bool result = false;
            if (accessRight == null) accessRight = "";

            ApplicationUser user = userRetrieveService.GetUserById(userId);
            string downloadUserGroupId = "";
            if (user.Group !=null )
            {
                downloadUserGroupId = user.Group.Id.ToString();
            }
            
            string downloadUserId = user.Id.ToString();

            if (fileType.Equals(FileDownload.FileType.Resource))
            {
                List<string> userRole = userRetrieveService.GetRolesForUser(userId).ToList();

                bool isAuthorizeUser = userRole.Any(s => s.Equals("SystemAdmin"));

                if (isAuthorizeUser)
                {
                    return true;
                }

                string[] userIds = accessUserRight.Split(',');

                if (userIds.Length > 0)
                {
                    for (int i = 0; i < userIds.Length; i++)
                    {
                        if (userIds[i].Equals(downloadUserId))
                        {
                            result = true;
                        }
                    }
                }

                if (string.IsNullOrEmpty(downloadUserGroupId))
                {
                    return false;
                }
                else
                {
                    string[] groups = accessRight.Split(',');

                    if (groups.Length > 0)
                    {
                        for (int i = 0; i < groups.Length; i++)
                        {
                            if (groups[i].Equals(downloadUserGroupId))
                            {
                                result = true;
                            }
                        }
                    }
                }
               
            }
            else if (fileType.Equals(FileDownload.FileType.Permit))
            {
                List<string> userRole = userRetrieveService.GetRolesForUser(userId).ToList();

                bool isAuthorizeUser = userRole.Any(s => s.Equals("SiteManager") || s.Equals("PermitManager") || s.Equals("SystemAdmin"));

                if (isAuthorizeUser)
                {
                    return true;
                }

                if (accessRight.Equals(downloadUserId))
                {
                    result = true;
                }
            }
            else if (fileType.Equals(FileDownload.FileType.DiscussionForum))
            {
                List<string> userRole = userRetrieveService.GetRolesForUser(userId).ToList();

                bool isAuthorizeUser = userRole.Any(s => s.Equals("SiteManager") || s.Equals("PermitManager") || s.Equals("SystemAdmin"));

                if (isAuthorizeUser)
                {
                    return true;
                }

                string[] researche_PI = accessRight.Split(',');

                if (researche_PI[0].Equals(downloadUserId))
                {
                    result = true;
                }
                else
                {
                    
                    if (researche_PI.Length == 2)
                    {
                        
                        var d_user = userRetrieveService.GetUserById(long.Parse(downloadUserId));
                        if (d_user != null)
                        {
                            if (researche_PI[1].Equals(d_user.Email,StringComparison.OrdinalIgnoreCase))
                            {
                                result = true;
                            }
                        }
                        
                    }
                }
            }

            return result;
        }
    }
}