﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Constants
{
    public class UserRoles
    {
        public const string SystemAdmin = "SystemAdmin";
        public const string PermitManager = "PermitManager";
        public const string SiteManager = "SiteManager";
        public const string Expert = "Expert";
        public const string ResourceUploader = "ResourceUploader";
        public const string ProjectAdmin = "ProjectAdmin";
        public const string ProjectMember = "ProjectMember";
        public const string Public = "Public";

        public const string FullNoAccess = "FullNoAccess";


        public const int SystemAdminId = 1;
        public const int PermitManagerId = 2;
        public const int SiteManagerId = 3;
        public const int ExpertId = 4;
        public const int ResourceUploaderId = 5;
        public const int ProjectAdminId = 6;
        public const int ProjectMemberId = 7;
        public const int PublicId = 8;

    }
}
