﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
   public class Survey : Entity<long>
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public int? ViewPermission { get; set; }
        public int? DownloadPermission { get; set; }
        public bool IsAutoApproveM { get; set; }
        public bool IsSearchable { get; set; }
        public bool IsActive { get; set; }
        public int? SurveyStatus { get; set; }
        public long? ApprovedById { get; set; }
        public int? Duration { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public long OwnerId { get; set; }
        [NotMapped]
        public virtual ApplicationUser Owner { get; set; }
        public long? SurveyCategoryId { get; set; }
        [NotMapped]
        public virtual SurveyCategory SurveyCategory { get; set; }
        public string LogoName { get; set; }
        public string LogoPath { get; set; }
        public int? MemberCount { get; set; }
        public long? CopyFromSurveyId { get; set; } //SurveyCR 2023
        public long? CopyBy { get; set; } //SurveyCR 2023
        public bool? CopyBySysAdmin { get; set; } //SurveyCR 2023

        public bool? FirstTimeApproved { get; set; } //SurveyCR 2023
        public string GetLogoImage()
        {
            var imagename = "/Content/images/nophotoavailable.jpg";
            if (!string.IsNullOrEmpty(LogoName))
            {
                imagename = LogoPath + LogoName;
            }

            return imagename;
        }
        public virtual List<SurveyMembers> SurveyMembers { get; set; }
        public virtual List<SurveyQuestions> SurveyQuestions { get; set; }
        public virtual List<SurveyLocation> SurveyLocation { get; set; }
        public string Location { get; set; }
        public virtual List<SurveySpecies> SurveySpecies { get; set; }
        public virtual List<SurveySubmission> SurveySubmission{ get; set; }
        public string Describe()
        {
            return "{ Title : \"" + Title + "\", Description : \"" + Description + "\", ViewPermission : \"" + ViewPermission + "\", DownloadPermission : \"" + DownloadPermission
                + "\", IsAutoApproveM : \"" + IsAutoApproveM + "\", IsSearchable : \"" + IsSearchable + "\", IsActive : \"" + IsActive + "\", SurveyCategoryId : \"" + SurveyCategoryId
                + "\", SurveyStatus : \"" + SurveyStatus + "\", ApprovedById : \"" + ApprovedById + "\", Duration : \"" + Duration + "\", StartDate : \"" + StartDate + "\", EndDate : \"" + EndDate
                + "\", OwnerId : \"" + OwnerId 
                + "\", MemberCount : \"" + MemberCount
                + "\", CopyFromSurveyId : \"" + CopyFromSurveyId
                + "\", CopyBy : \"" + CopyBy
                + "\", LogoName : \"" + LogoName + "\", LogoPath : \"" + LogoPath + "}";
        }
        public Survey()
        {

            MemberCount = 0;
          SurveyMembers = new List<SurveyMembers>();
          SurveyQuestions = new List<Models.SurveyQuestions>();
          SurveySubmission = new List<Models.SurveySubmission>();
          SurveyLocation = new List<SurveyLocation>();
         SurveySpecies = new List<SurveySpecies>();

        }
    }
}
