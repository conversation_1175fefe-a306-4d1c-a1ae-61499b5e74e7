﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class GISLocation : Entity<long>
    {
        public long ObjectId { get; set; }
        public string GlobalID { get; set; }
        public string Name { get; set; }
        public string Loc_CD { get; set; }
        public string Loc_DESC { get; set; }
        public string Sub_Loc_CD { get; set; }
        public string Sub_Loc_DESC { get; set; }
        public string Agency { get; set; }
        public string OIC { get; set; }
        [MaxLength(320)]
        public string Email { get; set; }
        public string Phone_Work { get; set; }
        public string Phone_Mobile { get; set; }
        public string FeatureID { get; set; } //Added to sove [NPARKS-10] Permit Application non-MAVEN sites approve/reject/pending issue

        [JsonIgnore]
        public virtual ICollection<ApplicationUser> SiteManagers { get; set; }
        [JsonIgnore]
        public virtual ICollection<ResearchStudyLocation> StudyLocationAccesses { get; set; }
        [JsonIgnore]
        public virtual ICollection<ResearchApplication> ResearchApplications { get; set; }
        [JsonIgnore]
        public virtual ICollection<ResearchPermitApplication> ResearchPermitApplications { get; set; }

        public GISLocation()
        {
            SiteManagers = new List<ApplicationUser>();
            StudyLocationAccesses = new List<ResearchStudyLocation>();
            ResearchPermitApplications = new List<ResearchPermitApplication>();

        }
    }
}
