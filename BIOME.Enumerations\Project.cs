﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Enumerations
{
    public class Project
    {
        public enum PermissionRank
        {
            PublicUsers=10,
            ProjectMembers=20,
            ProjectAdmins=30
        }

        public static List<object> ViewPermissionOption = new List<object>
        {
            new { value = (int)PermissionRank.PublicUsers , text = "Public users"},
            new { value = (int)PermissionRank.ProjectMembers , text = "Project members"},
            new { value = (int)PermissionRank.ProjectAdmins , text = "Project admins"},
        };

        public static List<object> DownloadPermissionOption = new List<object>
        {
            new { value = (int)PermissionRank.ProjectMembers , text = "Project members"},
            new { value = (int)PermissionRank.ProjectAdmins , text = "Project admins"},
        };

        public enum MemberRank
        {
            Rejected=10,
            Pending=20,
            Member=30,
            Admin=40
        }

        public static List<object> MemberRankOption = new List<object>
        {
           new { value = (int)MemberRank.Admin , text = "Admin"},
            new { value = (int)MemberRank.Member , text = "Member"},
            new { value = (int)MemberRank.Pending , text = "Pending"},
            new { value = (int)MemberRank.Rejected , text = "Rejected"},
        };

        public enum ProjectStatusRank
        {
            Rejected = 10,
            Suspend = 15,
            Pending = 20,
            Approved = 30,
        }

        public static List<object> ProjectStatusRankOption = new List<object>
        {
            new { value = (int)ProjectStatusRank.Approved , text = "Approved"},
            new { value = (int)ProjectStatusRank.Pending , text = "Pending"},
            new { value = (int)ProjectStatusRank.Suspend , text = "Suspend"},
            new { value = (int)ProjectStatusRank.Rejected , text = "Rejected"},
        };

        public static List<object> ProjectStatusRankOptionPending = new List<object>
        {
            new { value = (int)ProjectStatusRank.Approved , text = "Approved"},
            new { value = (int)ProjectStatusRank.Pending , text = "Pending"},
            new { value = (int)ProjectStatusRank.Rejected , text = "Rejected"},
        };

        public static List<object> ProjectStatusRankOptionApproved = new List<object>
        {
            new { value = (int)ProjectStatusRank.Approved , text = "Approved"},
            new { value = (int)ProjectStatusRank.Suspend , text = "Suspend"},
        };

        public static List<object> ProjectStatusRankOptionRejected = new List<object>
        {
            new { value = (int)ProjectStatusRank.Approved , text = "Approved"},
            new { value = (int)ProjectStatusRank.Rejected , text = "Rejected"},
        };

        public static List<object> ProjectStatusRankOptionSuspend = new List<object>
        {
            new { value = (int)ProjectStatusRank.Approved , text = "Approved"},
            new { value = (int)ProjectStatusRank.Suspend , text = "Suspend"},
        };

        public enum SearchProjectStatus
        {
            All = 1,
            Searchable = 2,
            NonSearchable = 3,
            Active = 4,
            Inactive = 5,
            Approved = 6,
            Suspended = 7,
        }
        public static List<object> SearchProjectStatusOption = new List<object>
        {
            new { value = "" , text = SearchProjectStatus.All.ToString()},
            new { value = SearchProjectStatus.Searchable.ToString() , text = SearchProjectStatus.Searchable.ToString()},
            new { value = SearchProjectStatus.NonSearchable.ToString() , text = "Non-searchable"},
            new { value = SearchProjectStatus.Active.ToString() , text = SearchProjectStatus.Active.ToString()},
            new { value = SearchProjectStatus.Inactive.ToString() , text = SearchProjectStatus.Inactive.ToString()},
            new { value = SearchProjectStatus.Approved.ToString() , text = SearchProjectStatus.Approved.ToString()},
            new { value = SearchProjectStatus.Suspended.ToString() , text = SearchProjectStatus.Suspended.ToString()},
        };
        public static List<object> SearchSightingsStatusOption = new List<object>
        {
            new { value = "" , text = SearchProjectStatus.All.ToString()},
            new { value = ((int)Sighting.StatusRank.NeedVerification).ToString(), text =  "Need Verification"},
            new { value = ((int)Sighting.StatusRank.Verified).ToString(), text =  "Verified"},

        };
    }
}
