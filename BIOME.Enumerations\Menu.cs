﻿using BIOME.Constants;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Enumerations
{
    public class Menu
    {
        public class Main
        {
            public enum MainMenu
            {
                Home,
                Sightings,
                Map,
                Research,
                Resource,
                About,
                Help,
                Login,
                Search
            }

            public enum SightingsSubMenu
            {
                None,
                All,
                My,
                Submit,
                Projects,
                Survey,
                Taxonomical
            }

            public enum MapSubMenu
            {
                None
            }

            public enum ResearchSubMenu
            {
                None,
                Application,
                Status,
                List
            }

            public enum ResearchApplicationSubMenu
            {
                [Description(BIOME.Constants.Research.Permit.ApplicationSubMenu.Terms)]
                Terms,
                [Description(BIOME.Constants.Research.Permit.ApplicationSubMenu.Online)]
                Online,
                [Description(BIOME.Constants.Research.Permit.ApplicationSubMenu.FAQ)]
                FAQ,
                [Description(BIOME.Constants.Research.Permit.ApplicationSubMenu.Contact)]
                Contact
            }

            public enum ResourceSubMenu
            {
                None,
                [Description("My Resource List")]
                MyResourceList,
                [Description("Resource List")]
                ResourceList
            }

            public enum AboutSubMenu
            {
                None,
                About,
                Partners,
                Related
            }

            public enum HelpSubMenu
            {
                None,
                FAQ,
                Terms,
                Guides,
            }

            public enum LoginNotAuthenticatedSubMenu
            {
                None,
                Login,
                SignUp
            }

            public enum LoginIsAuthenticatedSubMenu
            {
                None,
                MyProfile
            }

            public enum SearchSubMenu
            {
                None
            }
        }

        public class Admin
        {
            public enum MainMenu
            {
                Home,
                Research,
                Resources,
                Sightings,
                Projects,
                Survey,
                FeedBack,
                Map,
                User,
                ResearchTemplate,
                Misc,
                MaintainableList,
                Parameters,
                SiteAssignments,
                Reports,
                FieldGuides,
                MaintenanceNotice,
                AuditTrails
            }

            public enum HomeSubMenu
            {
                None,
                Header,
                Title,
                SGBioAtlasRow,
                BackgroundImg,
                //SelectedSightingsRow,
                HighlightsRow,
                IntranetHighlightsRow,
                Footer
            }

            public enum ResearchSubMenu
            {
                None,
                ApplicationListing,
                Calendar,
                Inbox
            }

            public enum ResourcesSubMenu
            {
                None,
                StepOne,
                StepTwo,
                StepThree,
                StepFour,
                DocumentType,
                StructuredDataTemplate
            }

            public enum SightingsSubMenu
            {
                None,
                AllSightings,
                FeaturedSightings,
                FlaggedSightings,
                InappropriateSightings,
                AutofilteredSightings,
                BlacklistedSightings,
                AdminSensitiveSightings,
                MaintainListofCategories,
                ThreatenedSpecies,
                MaskedSites,
                SightingParameters,
                FieldGuides,
                Projects,
                Survey,
                Feedback
            }

            public enum MapSubMenu
            {

            }

            public enum UserSubMenu
            {
                None,
                List,
                ManageUserGroup,
                ManageUser
            }

            public enum ResearchPermitSubMenu
            {
                None,
                Permit,
                Header,
                Terms
            }

            public enum MiscSubMenu
            {
                None,
                EmailTemplates,
                SystemParameters,
                MaintenanceNotice,
                AuditTrails
            }

            public enum EmailSubMenu
            {

            }

            public enum OtherSubMenu
            {
                

            }

            public enum MaintainableListSubMenu
            {
                None,
                DocumentType,
                StructuredDataTemplate,
                ResearchType,
                ProjectCategory,
                SurveyCategory,
                Habitats,
                Environment,
                MaintainListofCategories,
                ListOthers
            }

            public enum ParameterSubMenu
            {

            }

            public enum SiteAssignmentsSubMenu
            {

            }

            public enum ReportSubMenu
            {
                None,
                Users,
                Sightings,
                Resource,
                Projects,
                Survey,
                ResearchPermitApplication,
                CoralCollection
            }

            public enum FieldGuidesSubMenu
            {

            }

            public enum MaintenanceSubMenu
            {

            }

            public enum AuditTrailsSubMenu
            {
                None,
                Login,
                Logout,
                AccessResourcesUnsuccessful,
                SystemStartUpShutDown,
                Transactions,
                ResourceDocument,
                DetailsTransactions
            }
        }
    }
}
