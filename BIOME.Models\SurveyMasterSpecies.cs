﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class SurveyMasterSpecies:Entity<long>,IDescribableEntity 
    {
        //FamilySuborder,Category,CategoryName,Grouping,Species,CommonName,
        //Description,Photo1,Photo2,Photo3,Photo4,Attributes,Others
        public string FamilySuborder { get; set; }
        //public string Category { get; set; }
        //public string CategoryName { get; set; }
        public long? CategoryId { get; set; }
        [ForeignKey("CategoryId")]
        public virtual GuideDetail Category { get; set; }
        public string Grouping { get; set; }
        public string Species { get; set; }
        public string CommonName { get; set; }
        public string Description { get; set; }
        public string Photo1 { get; set; }
        public string Photo2 { get; set; }
        public string Photo3 { get; set; }
        public string Photo4 { get; set; }
        public string Attributes { get; set; }
        public string Others { get; set; }
        public string ImagePath { get; set; }
        public bool IsDeleted { get; set; }
        public string GetImage()
        {
            var imagename = "/Content/images/nophotoavailable.jpg";
            if (!string.IsNullOrEmpty(Photo1))
            {
                imagename = ImagePath + Photo1;
            }

            return imagename;
        }

        public string Describe()
        {
            return "{ FamilySuborder : \"" + FamilySuborder + "\", CategoryId : \"" + CategoryId + "\", Grouping : \"" + Grouping + "\", Species : \"" + Species
                + "\", CommonName : \"" + CommonName + "\", Description : \"" + Description + "\", Photo1 : \"" + Photo1 + "\", Photo2 : \"" + Photo2
                + "\", Photo3 : \"" + Photo3 + "\", Photo4 : \"" + Photo4 + "\", Attributes : \"" + Attributes + "\", Others : \"" + Others + "\", ImagePath : \"" + ImagePath
                + "\", IsDeleted : \"" + IsDeleted   +  "}";
        }

    }
}
