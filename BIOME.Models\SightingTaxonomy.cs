﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class SightingTaxonomy : Entity<long>,IDescribableEntity
    {
        public long AtSightingId { get; set; }
        [ForeignKey("AtSightingId")]
        public virtual SightingDetail AtSighting { get; set; }

        public string GenbankCommonName { get; set; }
        public string CommonName { get; set; }
        public string Authority { get; set; }


        public string Describe()
        {
            return "{ AtSightingId : \"" + AtSightingId + "\", GenbankCommonName : \"" + GenbankCommonName + "\", CommonName : \"" + CommonName + "\", Authority : \"" + Authority + "}";
        }

        public virtual IList<SightingTaxonomyClassification> SightingTaxonomyClassifications { get; set; }

        public SightingTaxonomy()
        {
            SightingTaxonomyClassifications = new List<SightingTaxonomyClassification>();
        }
    }
}
