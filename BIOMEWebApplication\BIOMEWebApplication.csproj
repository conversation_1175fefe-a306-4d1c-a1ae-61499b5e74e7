<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{6DE7E2BC-4AB7-4FD6-BD23-D3100AFA7762}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>BIOMEWebApplication</RootNamespace>
    <AssemblyName>BIOMEWebApplication</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort>44301</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <UseGlobalApplicationHostFile />
    <ApplicationInsightsResourceId />
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <UpdateAssemblyVersion>True</UpdateAssemblyVersion>
    <UpdateAssemblyFileVersion>True</UpdateAssemblyFileVersion>
    <UpdateAssemblyInfoVersion>True</UpdateAssemblyInfoVersion>
    <AssemblyVersionSettings>None.None.None.None</AssemblyVersionSettings>
    <AssemblyFileVersionSettings>None.None.None.None</AssemblyFileVersionSettings>
    <AssemblyInfoVersionSettings>None.None.None.None</AssemblyInfoVersionSettings>
    <Use64BitIISExpress>false</Use64BitIISExpress>
    <TypeScriptToolsVersion>4.9</TypeScriptToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug Internet|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;DEBUG;INTERNET</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release Internet|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;INTERNET</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AngleSharp, Version=********, Culture=neutral, PublicKeyToken=e83494dcdc6d31ea, processorArchitecture=MSIL">
      <HintPath>..\packages\AngleSharp.0.17.1\lib\net472\AngleSharp.dll</HintPath>
    </Reference>
    <Reference Include="AngleSharp.Css, Version=0.17.0.0, Culture=neutral, PublicKeyToken=e83494dcdc6d31ea, processorArchitecture=MSIL">
      <HintPath>..\packages\AngleSharp.Css.0.17.0\lib\net472\AngleSharp.Css.dll</HintPath>
    </Reference>
    <Reference Include="AntiXssLibrary">
      <HintPath>..\packages\AntiXSS.4.3.0\lib\net40\AntiXssLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="ArcGIS.ServiceModel, Version=5.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ArcGIS.PCL.5.1.0\lib\net45\ArcGIS.ServiceModel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ArcGIS.ServiceModel.NET, Version=5.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ArcGIS.PCL.5.1.0\lib\net45\ArcGIS.ServiceModel.NET.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Autofac, Version=6.3.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.6.3.0\lib\netstandard2.0\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Integration.Mvc, Version=*******, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.Mvc5.6.0.0\lib\net472\Autofac.Integration.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Integration.Mvc.Owin, Version=*******, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.Mvc5.Owin.6.0.0\lib\net472\Autofac.Integration.Mvc.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Integration.Owin, Version=6.0.1.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.Owin.6.0.1\lib\net472\Autofac.Integration.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Integration.WebApi, Version=6.1.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.WebApi2.6.1.0\lib\net472\Autofac.Integration.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Integration.WebApi.Owin, Version=*******, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.WebApi2.Owin.6.0.0\lib\net472\Autofac.Integration.WebApi.Owin.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=4.2.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.4.2.1\lib\net45\AutoMapper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Azure.Core, Version=1.40.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Core.1.40.0\lib\net472\Azure.Core.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Identity, Version=1.12.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Identity.1.12.0\lib\netstandard2.0\Azure.Identity.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Security.KeyVault.Secrets, Version=4.5.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Security.KeyVault.Secrets.4.5.0\lib\netstandard2.0\Azure.Security.KeyVault.Secrets.dll</HintPath>
    </Reference>
    <Reference Include="Boilerplate.Web.Mvc5, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fc5550082a9c642c, processorArchitecture=MSIL">
      <HintPath>..\packages\Boilerplate.Web.Mvc5.1.0.24\lib\net45\Boilerplate.Web.Mvc5.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ByteSizeLib, Version=0.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ByteSize.1.1.2\lib\dotnet\ByteSizeLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="CsvHelper, Version=2.0.0.0, Culture=neutral, PublicKeyToken=8c4959082be5c823, processorArchitecture=MSIL">
      <HintPath>..\packages\CsvHelper.2.14.2\lib\net45\CsvHelper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DataAnnotationsExtensions, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DataAnnotationsExtensions.*******\lib\net45\DataAnnotationsExtensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DataAnnotationsExtensions.ClientValidation, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DataAnnotationsExtensions.MVC.*******\lib\net45\DataAnnotationsExtensions.ClientValidation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.5\lib\DocumentFormat.OpenXml.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DotNetZip, Version=********, Culture=neutral, PublicKeyToken=6583c7c814667745, processorArchitecture=MSIL">
      <HintPath>..\packages\DotNetZip.1.15.0\lib\net40\DotNetZip.dll</HintPath>
    </Reference>
    <Reference Include="Elasticsearch.Net, Version=*******, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\Elasticsearch.Net.7.17.5\lib\net461\Elasticsearch.Net.dll</HintPath>
    </Reference>
    <Reference Include="Elmah, Version=1.2.14706.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\elmah.corelibrary.1.2.2\lib\Elmah.dll</HintPath>
    </Reference>
    <Reference Include="Elmah.Mvc, Version=2.1.2.1389, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Elmah.MVC.2.1.2\lib\net40\Elmah.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.DynamicFilters, Version=1.4.10.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.DynamicFilters.1.4.10.1\lib\net40\EntityFramework.DynamicFilters.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Flurl, Version=1.1.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Flurl.1.1.2\lib\portable-net40+sl50+win+wpa81+wp80+MonoAndroid10+MonoTouch10\Flurl.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="gdalconst_csharp, Version=3.10.0.0, Culture=neutral, PublicKeyToken=db5a52b08dc5b321, processorArchitecture=MSIL">
      <HintPath>..\packages\GDAL.3.10.0\lib\netstandard2.0\gdalconst_csharp.dll</HintPath>
    </Reference>
    <Reference Include="gdal_csharp, Version=3.10.0.0, Culture=neutral, PublicKeyToken=db5a52b08dc5b321, processorArchitecture=MSIL">
      <HintPath>..\packages\GDAL.3.10.0\lib\netstandard2.0\gdal_csharp.dll</HintPath>
    </Reference>
    <Reference Include="GeoJSON.Net, Version=0.1.47.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoJSON.Net.0.1.47\lib\portable-net40+sl5+wp80+win8+wpa81\GeoJSON.Net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="GeoJSON.Net.Contrib.EF, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoJSON.Net.Contrib.EF.1.0.0\lib\net452\GeoJSON.Net.Contrib.EF.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Glimpse.AspNet, Version=1.9.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Glimpse.AspNet.1.9.2\lib\net45\Glimpse.AspNet.dll</HintPath>
    </Reference>
    <Reference Include="Glimpse.Core, Version=1.8.6.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Glimpse.1.8.6\lib\net45\Glimpse.Core.dll</HintPath>
    </Reference>
    <Reference Include="Glimpse.Elmah, Version=1.1.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Glimpse.Elmah.1.1.1\lib\net40\Glimpse.Elmah.dll</HintPath>
    </Reference>
    <Reference Include="Glimpse.Mvc5, Version=1.5.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Glimpse.Mvc5.1.5.3\lib\net45\Glimpse.Mvc5.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.Autofac, Version=2.4.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.Autofac.2.4.1\lib\net45\Hangfire.Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.Core, Version=1.8.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.Core.1.8.2\lib\net46\Hangfire.Core.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.Dashboard.Authorization, Version=3.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.Dashboard.Authorization.3.0.1\lib\net45\Hangfire.Dashboard.Authorization.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.SqlServer, Version=1.8.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.SqlServer.1.8.2\lib\net451\Hangfire.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="HtmlSanitizationLibrary">
      <HintPath>..\packages\AntiXSS.4.3.0\lib\net40\HtmlSanitizationLibrary.dll</HintPath>
    </Reference>
    <Reference Include="HtmlSanitizer, Version=9.0.0.0, Culture=neutral, PublicKeyToken=61c49a1a9e79cc28, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlSanitizer.9.0.876\lib\net461\HtmlSanitizer.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.12.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.12\lib\net45\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Magick.NET-Q16-AnyCPU, Version=14.6.0.0, Culture=neutral, PublicKeyToken=2004825badfa91ec, processorArchitecture=MSIL">
      <HintPath>..\packages\Magick.NET-Q16-AnyCPU.14.6.0\lib\netstandard20\Magick.NET-Q16-AnyCPU.dll</HintPath>
    </Reference>
    <Reference Include="Magick.NET.Core, Version=14.6.0.0, Culture=neutral, PublicKeyToken=2004825badfa91ec, processorArchitecture=MSIL">
      <HintPath>..\packages\Magick.NET.Core.14.6.0\lib\netstandard20\Magick.NET.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.4\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.4\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.1.1.1\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Identity.Client, Version=4.61.3.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Client.4.61.3\lib\net462\Microsoft.Identity.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client.Extensions.Msal, Version=4.61.3.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Client.Extensions.Msal.4.61.3\lib\netstandard2.0\Microsoft.Identity.Client.Extensions.Msal.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Abstractions, Version=6.35.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Abstractions.6.35.0\lib\net472\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.5.3.0\lib\net451\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.5.3.0\lib\net451\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.5.3.0\lib\net451\Microsoft.IdentityModel.Protocols.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols.WsFederation, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.WsFederation.5.3.0\lib\net451\Microsoft.IdentityModel.Protocols.WsFederation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.5.3.0\lib\net451\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens.Saml, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.Saml.5.3.0\lib\net451\Microsoft.IdentityModel.Tokens.Saml.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Xml, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Xml.5.3.0\lib\net451\Microsoft.IdentityModel.Xml.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.0.1\lib\net45\Microsoft.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.3.1.0\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.4.0.1\lib\net45\Microsoft.Owin.Security.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.3.1.0\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.3.1.0\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.WsFederation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.WsFederation.4.0.1\lib\net45\Microsoft.Owin.Security.WsFederation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.11.0.2\lib\net20\Microsoft.SqlServer.Types.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MvcPaging, Version=2.1.13.46, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MvcPaging.2.1.13\lib\net40\MvcPaging.dll</HintPath>
    </Reference>
    <Reference Include="Nest, Version=*******, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\NEST.7.17.5\lib\net461\Nest.dll</HintPath>
    </Reference>
    <Reference Include="Nest.JsonNetSerializer, Version=*******, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\NEST.JsonNetSerializer.7.17.5\lib\net461\Nest.JsonNetSerializer.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.2\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="NLog.Web, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.Web.4.9.2\lib\net35\NLog.Web.dll</HintPath>
    </Reference>
    <Reference Include="NWebsec, Version=4.2.0.0, Culture=neutral, PublicKeyToken=3613da5f958908a1, processorArchitecture=MSIL">
      <HintPath>..\packages\NWebsec.4.2.0\lib\45\NWebsec.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NWebsec.Core, Version=1.5.0.0, Culture=neutral, PublicKeyToken=3613da5f958908a1, processorArchitecture=MSIL">
      <HintPath>..\packages\NWebsec.Core.1.5.0\lib\45\NWebsec.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NWebsec.Mvc, Version=4.2.0.0, Culture=neutral, PublicKeyToken=3613da5f958908a1, processorArchitecture=MSIL">
      <HintPath>..\packages\NWebsec.Mvc.4.2.0\lib\45\NWebsec.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NWebsec.Owin, Version=2.2.0.0, Culture=neutral, PublicKeyToken=3613da5f958908a1, processorArchitecture=MSIL">
      <HintPath>..\packages\NWebsec.Owin.2.2.0\lib\45\NWebsec.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ogr_csharp, Version=3.10.0.0, Culture=neutral, PublicKeyToken=db5a52b08dc5b321, processorArchitecture=MSIL">
      <HintPath>..\packages\GDAL.3.10.0\lib\netstandard2.0\ogr_csharp.dll</HintPath>
    </Reference>
    <Reference Include="osr_csharp, Version=3.10.0.0, Culture=neutral, PublicKeyToken=db5a52b08dc5b321, processorArchitecture=MSIL">
      <HintPath>..\packages\GDAL.3.10.0\lib\netstandard2.0\osr_csharp.dll</HintPath>
    </Reference>
    <Reference Include="Owin">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="SDammann.WebApi.Versioning, Version=2.7.0.0, Culture=neutral, PublicKeyToken=8e49016fcaf923da, processorArchitecture=MSIL">
      <HintPath>..\packages\SDammann.WebApi.Versioning.2.8.0.0\lib\net45\SDammann.WebApi.Versioning.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ClientModel, Version=1.0.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ClientModel.1.0.0\lib\netstandard2.0\System.ClientModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=9.0.0.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.Immutable.9.0.1\lib\net462\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.SQLite, Version=1.0.118.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\lib\net46\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SQLite.EF6, Version=1.0.109.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Data.SQLite.EF6.1.0.109.0\lib\net451\System.Data.SQLite.EF6.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data.SQLite.Linq, Version=1.0.109.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Data.SQLite.Linq.1.0.109.0\lib\net451\System.Data.SQLite.Linq.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=6.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.6.0.1\lib\net461\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.5.3.0\lib\net451\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.IO.FileSystem.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.AccessControl.5.0.0\lib\net461\System.IO.FileSystem.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=1.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.1.0.2\lib\net461\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Extensions, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Primitives, Version=4.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.5.0.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.ProtectedData, Version=4.0.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.ProtectedData.4.7.0\lib\net461\System.Security.Cryptography.ProtectedData.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encoding.CodePages, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.6.0.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.4.7.2\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.4.7.2\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.2.3\lib\net45\System.Web.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Owin.5.2.7\lib\net45\System.Web.Http.Owin.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.1.0\lib\net40\WebActivatorEx.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="WebGrease, Version=1.6.5135.21930, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Areas\Admin\Constants\ApplicationStatusController\ApplicationStatusControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\ApplicationStatusController\ApplicationStatusControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\ApplicationStatusController\ApplicationStatusView.cs" />
    <Compile Include="Areas\Admin\Constants\AuditTrailController\AuditTrailControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\AuditTrailController\AuditTrailControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\AuditTrailController\AuditTrailView.cs" />
    <Compile Include="Areas\Admin\Constants\DiscussionsController\DiscussionsControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\DiscussionsController\DiscussionsControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\DiscussionsController\DiscussionsView.cs" />
    <Compile Include="Areas\Admin\Constants\DocumentTypeController\DocumentTypeControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\DocumentTypeController\DocumentTypeControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\DocumentTypeController\DocumentTypeView.cs" />
    <Compile Include="Areas\Admin\Constants\EmailTemplateController\EmailTemplateControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\EmailTemplateController\EmailTemplateControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\EmailTemplateController\EmailTemplateView.cs" />
    <Compile Include="Areas\Admin\Constants\LettersController\LettersControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\LettersController\LettersControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\LettersController\LettersView.cs" />
    <Compile Include="Areas\Admin\Constants\MaintainableListController\MaintainableListControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\MaintainableListController\MaintainableListControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\MaintainableListController\MaintainableListView.cs" />
    <Compile Include="Areas\Admin\Constants\PassesController\PassesControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\PassesController\PassesControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\PassesController\PassesView.cs" />
    <Compile Include="Areas\Admin\Constants\FeedbackController\FeedbacksControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\FeedbackController\FeedbacksControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\FeedbackController\FeedbacksView.cs" />
    <Compile Include="Areas\Admin\Constants\ProjectsController\ProjectsControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\ProjectsController\ProjectsControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\ProjectsController\ProjectsView.cs" />
    <Compile Include="Areas\Admin\Constants\ReportController\ReportControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\ReportController\ReportControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\ReportController\ReportView.cs" />
    <Compile Include="Areas\Admin\Constants\ResearchController\ResearchControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\ResearchController\ResearchControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\ResearchController\ResearchView.cs" />
    <Compile Include="Areas\Admin\Constants\ResourceController\ResourceControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\ResourceController\ResourceControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\ResourceController\ResourceView.cs" />
    <Compile Include="Areas\Admin\Constants\ResearchPermitTemplateController\ResearchPermitTemplateControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\ResearchPermitTemplateController\ResearchPermitTemplateControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\ResearchPermitTemplateController\ResearchPermitTemplateView.cs" />
    <Compile Include="Areas\Admin\Constants\SessionExtensions.cs" />
    <Compile Include="Areas\Admin\Constants\SightingsController\SightingsControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\SightingsController\SightingsControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\SightingsController\SightingsView.cs" />
    <Compile Include="Areas\Admin\Constants\SitesController\SitesControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\SitesController\SitesControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\SitesController\SitesView.cs" />
    <Compile Include="Areas\Admin\Constants\StructuredDataTemplateController\StructuredDataTemplateControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\StructuredDataTemplateController\StructuredDataTemplateControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\StructuredDataTemplateController\StructuredDataTemplateView.cs" />
    <Compile Include="Areas\Admin\Constants\MaintenanceNoticeController\MaintenanceNoticeControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\MaintenanceNoticeController\MaintenanceNoticeControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\MaintenanceNoticeController\MaintenanceNoticeView.cs" />
    <Compile Include="Areas\Admin\Constants\SurveysController\SurveysControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\SurveysController\SurveysControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\SurveysController\SurveysView.cs" />
    <Compile Include="Areas\Admin\Constants\UsersController\UsersControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\UsersController\UsersControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\UsersController\UsersView.cs" />
    <Compile Include="Areas\Admin\Controllers\AdminControllerBase.cs" />
    <Compile Include="Areas\Admin\Controllers\ApplicationStatusController.cs" />
    <Compile Include="Areas\Admin\Controllers\AuditTrailController.cs" />
    <Compile Include="Areas\Admin\Controllers\DiscussionsController.cs" />
    <Compile Include="Areas\Admin\Controllers\DocumentTypeController.cs" />
    <Compile Include="Areas\Admin\Controllers\EmailTemplateController.cs" />
    <Compile Include="Areas\Admin\Controllers\FeedbackController.cs" />
    <Compile Include="Areas\Admin\Controllers\HomeController.cs" />
    <Compile Include="Areas\Admin\Controllers\LettersController.cs" />
    <Compile Include="Areas\Admin\Controllers\MaintainableListController.cs" />
    <Compile Include="Areas\Admin\Controllers\MaintenanceNoticesController.cs" />
    <Compile Include="Areas\Admin\Controllers\PassesController.cs" />
    <Compile Include="Areas\Admin\Controllers\ProjectsController.cs" />
    <Compile Include="Areas\Admin\Controllers\ReportsController.cs" />
    <Compile Include="Areas\Admin\Controllers\ResearchController.cs" />
    <Compile Include="Areas\Admin\Controllers\ResearchPermitTemplateController.cs" />
    <Compile Include="Areas\Admin\Controllers\ResourceController.cs" />
    <Compile Include="Areas\Admin\Controllers\SightingsController.cs" />
    <Compile Include="Areas\Admin\Controllers\SitesController.cs" />
    <Compile Include="Areas\Admin\Controllers\StructuredDataTemplateController.cs" />
    <Compile Include="Areas\Admin\Controllers\SurveysController.cs" />
    <Compile Include="Areas\Admin\Controllers\SystemParametersController.cs" />
    <Compile Include="Areas\Admin\Controllers\UsersController.cs" />
    <Compile Include="Areas\Admin\HtmlPrefixScopeExtensions.cs" />
    <Compile Include="Authentication\SCIMAuthorizationAttribute.cs" />
    <Compile Include="Authentication\APIDevCredAuthenticationAttribute.cs" />
    <Compile Include="ApplicationPreload.cs" />
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RegisterClientValidationExtensions.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\Startup.Container.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Areas\Admin\AdminAreaRegistration.cs" />
    <Compile Include="Areas\Admin\Constants\ControllerName.cs" />
    <Compile Include="Areas\Admin\Constants\SystemParametersController\SystemParametersControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\SystemParametersController\SystemParametersControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\SystemParametersController\SystemParametersView.cs" />
    <Compile Include="Authentication\BasicAuthenticationAttribute.cs" />
    <Compile Include="Authentication\BasicAuthenticationSuppressFormsRedirectResult.cs" />
    <Compile Include="Authentication\APIInternetOnlyAuthenticationAttribute.cs" />
    <Compile Include="Authorization\AccessDeniedAuthorizeAttribute.cs" />
    <Compile Include="Authorization\AjaxAuthorizationAttribute.cs" />
    <Compile Include="Authorization\APIInternetOnlyAuthorizationAttribute.cs" />
    <Compile Include="Authorization\APITokenAuthorizationAttribute.cs" />
    <Compile Include="Authorization\SessionAuthorizationAttribute.cs" />
    <Compile Include="Breadcrumbs\Breadcrumbs.cs" />
    <Compile Include="BundleHelper\AsIsBundleOrderer.cs" />
    <Compile Include="Areas\Admin\Constants\HomeController\HomeControllerAction.cs" />
    <Compile Include="Areas\Admin\Constants\HomeController\HomeControllerRoute.cs" />
    <Compile Include="Areas\Admin\Constants\HomeController\HomeView.cs" />
    <Compile Include="Constants\AboutUsController\AboutUsControllerAction.cs" />
    <Compile Include="Constants\AboutUsController\AboutUsControllerRoute.cs" />
    <Compile Include="Constants\AboutUsController\AboutUsView.cs" />
    <Compile Include="Constants\ApplicationStatusController\ApplicationStatusControllerAction.cs" />
    <Compile Include="Constants\ApplicationStatusController\ApplicationStatusControllerRoute.cs" />
    <Compile Include="Constants\ApplicationStatusController\ApplicationStatusView.cs" />
    <Compile Include="Constants\AreaName.cs" />
    <Compile Include="Constants\CategoriesController\CategoriesControllerAction.cs" />
    <Compile Include="Constants\CategoriesController\CategoriesControllerRoute.cs" />
    <Compile Include="Constants\CategoriesController\CategoriesView.cs" />
    <Compile Include="Constants\DiscussionsController\DiscussionsControllerAction.cs" />
    <Compile Include="Constants\DiscussionsController\DiscussionsControllerRoute.cs" />
    <Compile Include="Constants\DiscussionsController\DiscussionsView.cs" />
    <Compile Include="Constants\FileDownloadController\FileDownloadControllerAction.cs" />
    <Compile Include="Constants\FileDownloadController\FileDownloadControllerRoute.cs" />
    <Compile Include="Constants\FileDownloadController\FileDownloadView.cs" />
    <Compile Include="Constants\HomeController\HomeView.cs" />
    <Compile Include="Constants\HelpController\HelpControllerAction.cs" />
    <Compile Include="Constants\HelpController\HelpControllerRoute.cs" />
    <Compile Include="Constants\HelpController\HelpView.cs" />
    <Compile Include="Constants\LettersController\LettersControllerAction.cs" />
    <Compile Include="Constants\LettersController\LettersControllerRoute.cs" />
    <Compile Include="Constants\LettersController\LettersView.cs" />
    <Compile Include="Constants\FeedbacksController\FeedbacksControllerAction.cs" />
    <Compile Include="Constants\FeedbacksController\FeedbacksControllerRoute.cs" />
    <Compile Include="Constants\FeedbacksController\FeedbacksView.cs" />
    <Compile Include="Constants\SessionExtensions.cs" />
    <Compile Include="Constants\ToDoListController\ToDoListControllerAction.cs" />
    <Compile Include="Constants\ToDoListController\ToDoListControllerRoute.cs" />
    <Compile Include="Constants\ToDoListController\ToDoListView.cs" />
    <Compile Include="Constants\SurveysController\SurveysControllerAction.cs" />
    <Compile Include="Constants\SurveysController\SurveysControllerRoute.cs" />
    <Compile Include="Constants\SurveysController\SurveysView.cs" />
    <Compile Include="Constants\ReportsController\ReportsControllerAction.cs" />
    <Compile Include="Constants\ReportsController\ReportsControllerRoute.cs" />
    <Compile Include="Constants\ReportsController\ReportsView.cs" />
    <Compile Include="Constants\PassesController\PassesControllerAction.cs" />
    <Compile Include="Constants\PassesController\PassesControllerRoute.cs" />
    <Compile Include="Constants\PassesController\PassesView.cs" />
    <Compile Include="Constants\PermitsController\PermitsControllerAction.cs" />
    <Compile Include="Constants\PermitsController\PermitsControllerRoute.cs" />
    <Compile Include="Constants\PermitsController\PermitsView.cs" />
    <Compile Include="Constants\ProjectsController\ProjectsControllerAction.cs" />
    <Compile Include="Constants\ProjectsController\ProjectsControllerRoute.cs" />
    <Compile Include="Constants\ProjectsController\ProjectsView.cs" />
    <Compile Include="Constants\ResourcesController\ResourcesControllerAction.cs" />
    <Compile Include="Constants\ResourcesController\ResourcesControllerRoute.cs" />
    <Compile Include="Constants\ResourcesController\ResourcesView.cs" />
    <Compile Include="Constants\SearchController\SearchControllerAction.cs" />
    <Compile Include="Constants\SearchController\SearchControllerRoute.cs" />
    <Compile Include="Constants\SearchController\SearchView.cs" />
    <Compile Include="Constants\SharedView.cs" />
    <Compile Include="Constants\SightingsController\SightingsControllerAction.cs" />
    <Compile Include="Constants\SightingsController\SightingsControllerRoute.cs" />
    <Compile Include="Constants\SightingsController\SightingsView.cs" />
    <Compile Include="Constants\MapController\MapControllerAction.cs" />
    <Compile Include="Constants\MapController\MapControllerRoute.cs" />
    <Compile Include="Constants\MapController\MapView.cs" />
    <Compile Include="Constants\AccountController\AccountControllerAction.cs" />
    <Compile Include="Constants\AccountController\AccountControllerRoute.cs" />
    <Compile Include="Constants\AccountController\AccountView.cs" />
    <Compile Include="Constants\Application.cs" />
    <Compile Include="Constants\CacheSetting.cs" />
    <Compile Include="Constants\CacheProfileName.cs" />
    <Compile Include="Constants\ContentDeliveryNetwork.cs" />
    <Compile Include="Constants\ControllerName.cs" />
    <Compile Include="Constants\ErrorController\ErrorControllerAction.cs" />
    <Compile Include="Constants\ErrorController\ErrorControllerRoute.cs" />
    <Compile Include="Constants\HomeController\HomeControllerAction.cs" />
    <Compile Include="Constants\HomeController\HomeControllerRoute.cs" />
    <Compile Include="Constants\ProfileController\ProfileControllerAction.cs" />
    <Compile Include="Constants\ProfileController\ProfileControllerRoute.cs" />
    <Compile Include="Constants\ProfileController\ProfileView.cs" />
    <Compile Include="Controllers\AboutUsController.cs" />
    <Compile Include="Controllers\CAMAPI\ApiICAMControllerBase.cs" />
    <Compile Include="Controllers\CAMAPI\GroupController.cs" />
    <Compile Include="Controllers\CAMAPI\UserController.cs" />
    <Compile Include="Controllers\ToDoListController.cs" />
    <Compile Include="Controllers\FeedbacksController.cs" />
    <Compile Include="Controllers\AccountController.cs" />
    <Compile Include="Controllers\API\ApiBadgeControllerBase.cs" />
    <Compile Include="Controllers\API\ApiConfigControllerBase.cs" />
    <Compile Include="Controllers\API\ApiControllerBase.cs" />
    <Compile Include="Controllers\API\ApiGuideControllerBase.cs" />
    <Compile Include="Controllers\API\ApiInternetOnlyController.cs" />
    <Compile Include="Controllers\API\ApiProjectControllerBase.cs" />
    <Compile Include="Controllers\API\ApiSightingControllerBase.cs" />
    <Compile Include="Controllers\API\ApiSightingMavenControllerBase.cs" />
    <Compile Include="Controllers\API\ApiSurveyControllerBase.cs" />
    <Compile Include="Controllers\API\ApiSystemControllerBase.cs" />
    <Compile Include="Controllers\API\ApiUserControllerBase.cs" />
    <Compile Include="Controllers\API\v1_0\ApiBadgeController.cs" />
    <Compile Include="Controllers\API\v1_0\ApiConfigController.cs" />
    <Compile Include="Controllers\API\v1_0\ApiGuideController.cs" />
    <Compile Include="Controllers\API\v1_0\ApiProjectController.cs" />
    <Compile Include="Controllers\API\v1_0\ApiResearchMavenController.cs" />
    <Compile Include="Controllers\API\v1_0\ApiResourceMavenController.cs" />
    <Compile Include="Controllers\API\v1_0\ApiSightingController.cs" />
    <Compile Include="Controllers\API\v1_0\ApiSightingMavenController.cs" />
    <Compile Include="Controllers\API\v1_0\ApiSurveyController.cs" />
    <Compile Include="Controllers\API\v1_0\ApiSystemController.cs" />
    <Compile Include="Controllers\API\v1_0\ApiUserController.cs" />
    <Compile Include="Controllers\ApplicationStatusController.cs" />
    <Compile Include="Controllers\CategoriesController.cs" />
    <Compile Include="Controllers\ControllerBase.cs" />
    <Compile Include="Controllers\DiscussionsController.cs" />
    <Compile Include="Controllers\ErrorController.cs" />
    <Compile Include="Controllers\FileDownloadController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\DownloadPermitFileController.cs" />
    <Compile Include="Controllers\LettersController.cs" />
    <Compile Include="Controllers\MapController.cs" />
    <Compile Include="Controllers\HelpController.cs" />
    <Compile Include="Controllers\PassesController.cs" />
    <Compile Include="Controllers\PermitsController.cs" />
    <Compile Include="Controllers\ProfileController.cs" />
    <Compile Include="Controllers\SurveysController.cs" />
    <Compile Include="Controllers\ProjectsController.cs" />
    <Compile Include="Controllers\PublicControllerBase.cs" />
    <Compile Include="Controllers\ReportsController.cs" />
    <Compile Include="Controllers\ResourcesController.cs" />
    <Compile Include="Controllers\SearchController.cs" />
    <Compile Include="Controllers\SightingsController.cs" />
    <Compile Include="Extensions\NotificationExtensions.cs" />
    <Compile Include="Filters\HasPasswordExpiredAttribute.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="HangfireBootstrapper.cs" />
    <Compile Include="Helpers\Encryption64.cs" />
    <Compile Include="Helpers\MapHelper.cs" />
    <Compile Include="Helpers\RenderViewHelper.cs" />
    <Compile Include="Models\HttpAPIResponse\AddChallengeOnUnauthorizedResult.cs" />
    <Compile Include="Models\HttpAPIResponse\AuthenticationFailureResult.cs" />
    <Compile Include="Models\HttpAPIResponse\JsonAPIResult.cs" />
    <Compile Include="ModelState\ExportModelStateToTempData.cs" />
    <Compile Include="ModelState\ImportModelStateFromTempData.cs" />
    <Compile Include="ModelState\ModelStateTempDataTransfer.cs" />
    <Compile Include="Models\SubMenu.cs" />
    <Compile Include="Notifications\SystemNotification.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Providers\ApplicationOAuthProvider.cs" />
    <Compile Include="Results\CamAPIErrorResult.cs" />
    <Compile Include="Results\InvalidTokenAuthenticationFailureResult.cs" />
    <Compile Include="Results\GenericAuthenticationFailureResult.cs" />
    <Compile Include="Results\AuthenticationFailureResult.cs" />
    <Compile Include="Results\ChallengeResult.cs" />
    <Compile Include="Results\ExpiredSessionAuthenticationFailureResult.cs" />
    <Compile Include="Results\InvalidSessionAuthenticationFailureResult.cs" />
    <Compile Include="Services\BrowserConfig\BrowserConfigService.cs" />
    <Compile Include="Services\BrowserConfig\IBrowserConfigService.cs" />
    <Compile Include="Services\Cache\CacheService.cs" />
    <Compile Include="Services\Cache\ICacheService.cs" />
    <Compile Include="Services\Logging\ILoggingService.cs" />
    <Compile Include="Services\Logging\LoggingService.cs" />
    <Compile Include="Services\Manifest\IManifestService.cs" />
    <Compile Include="Services\Manifest\ManifestService.cs" />
    <Compile Include="Services\OpenSearch\IOpenSearchService.cs" />
    <Compile Include="Services\OpenSearch\OpenSearchService.cs" />
    <Compile Include="Services\Robots\IRobotsService.cs" />
    <Compile Include="Services\Robots\RobotsService.cs" />
    <Compile Include="Services\SitemapPinger\ISitemapPingerService.cs" />
    <Compile Include="Services\SitemapPinger\SitemapPingerService.cs" />
    <Compile Include="Services\Sitemap\ISitemapService.cs" />
    <Compile Include="Services\Sitemap\SitemapService.cs">
      <ExcludeFromStyleCop>False</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Services\Feed\IFeedService.cs" />
    <Compile Include="Services\Feed\FeedService.cs" />
    <Compile Include="SqlServerTypes\Loader.cs" />
    <Compile Include="Startup.cs" />
    <Compile Include="Validations\ValidateMimeMultipartContentFilter.cs" />
    <Compile Include="Validations\ValidateModelAttribute .cs" />
    <Compile Include="Versioning\BIOMEHttpControllerTypeCache.cs" />
    <Compile Include="Versioning\BIOMERouteVersionedControllerSelector.cs" />
    <Compile Include="Versioning\BIOMEVersionedControllerSelector.cs" />
    <Compile Include="Versioning\ExceptionStrings.Designer.cs" />
    <Compile Include="Versioning\LockValue.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="..\packages\Microsoft.SqlServer.Types.11.0.2\nativeBinaries\x64\msvcr100.dll">
      <Link>SqlServerTypes\x64\msvcr100.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\packages\Microsoft.SqlServer.Types.11.0.2\nativeBinaries\x64\SqlServerSpatial110.dll">
      <Link>SqlServerTypes\x64\SqlServerSpatial110.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\packages\Microsoft.SqlServer.Types.11.0.2\nativeBinaries\x86\msvcr100.dll">
      <Link>SqlServerTypes\x86\msvcr100.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\packages\Microsoft.SqlServer.Types.11.0.2\nativeBinaries\x86\SqlServerSpatial110.dll">
      <Link>SqlServerTypes\x86\SqlServerSpatial110.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Admin\Views\Reports\ResearchNumberPermitsByOrg.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\ResearchReportsSubmitted.cshtml" />
    <Content Include="BingSiteAuth.xml" />
    <Content Include="Content\adjustment.css" />
    <Content Include="Content\adjustment.min.css" />
    <Content Include="Content\animate.css" />
    <Content Include="Content\animate.min.css" />
    <Content Include="Content\Badge\1-sightings-via-app.png" />
    <Content Include="Content\Badge\1-submission-bukit-timah-nature-reserve.png" />
    <Content Include="Content\Badge\1-submission-central-catchment-nature-reserve.png" />
    <Content Include="Content\Badge\1-submission-labrador-nature-reserve.png" />
    <Content Include="Content\Badge\1-submission-singapore-botanic-gardens.png" />
    <Content Include="Content\Badge\1-submission-sungei-buloh-nature-reserve.png" />
    <Content Include="Content\Badge\10-sightings-amphibian.png" />
    <Content Include="Content\Badge\10-sightings-arachnid--insect.png" />
    <Content Include="Content\Badge\10-sightings-bird.png" />
    <Content Include="Content\Badge\10-sightings-fish.png" />
    <Content Include="Content\Badge\10-sightings-fungus.png" />
    <Content Include="Content\Badge\10-sightings-mammal.png" />
    <Content Include="Content\Badge\10-sightings-mollusc.png" />
    <Content Include="Content\Badge\10-sightings-plants.png" />
    <Content Include="Content\Badge\10-sightings-reptile.png" />
    <Content Include="Content\Badge\10-sightings-via-app.png" />
    <Content Include="Content\Badge\100-sightings-amphibian.png" />
    <Content Include="Content\Badge\100-sightings-arachnid--insect.png" />
    <Content Include="Content\Badge\100-sightings-bird.png" />
    <Content Include="Content\Badge\100-sightings-fish.png" />
    <Content Include="Content\Badge\100-sightings-fungus.png" />
    <Content Include="Content\Badge\100-sightings-mammal.png" />
    <Content Include="Content\Badge\100-sightings-mollusc.png" />
    <Content Include="Content\Badge\100-sightings-plants.png" />
    <Content Include="Content\Badge\100-sightings-reptile.png" />
    <Content Include="Content\Badge\100-sightings-via-app.png" />
    <Content Include="Content\Badge\100-social-internal-likes.png" />
    <Content Include="Content\Badge\20-social-shares-facebook.png" />
    <Content Include="Content\Badge\50-sightings-amphibian.png" />
    <Content Include="Content\Badge\50-sightings-arachnid--insect.png" />
    <Content Include="Content\Badge\50-sightings-bird.png" />
    <Content Include="Content\Badge\50-sightings-fish.png" />
    <Content Include="Content\Badge\50-sightings-fungus.png" />
    <Content Include="Content\Badge\50-sightings-mammal.png" />
    <Content Include="Content\Badge\50-sightings-mollusc.png" />
    <Content Include="Content\Badge\50-sightings-plants.png" />
    <Content Include="Content\Badge\50-sightings-reptile.png" />
    <Content Include="Content\Badge\50-sightings-via-app.png" />
    <Content Include="Content\Badge\50-social-internal-likes.png" />
    <Content Include="Content\Badge\50-special-sg50-sightings-2015.png" />
    <Content Include="Content\Badge\project-community-garden-bird-count.png" />
    <Content Include="Content\Badge\project-greening-schools-for-biodiversity.png" />
    <Content Include="Content\Badge\project-npark-butterfly-count-2015.png" />
    <Content Include="Content\Badge\special-earth-day.png" />
    <Content Include="Content\Badge\special-international-day-biological-diversity.png" />
    <Content Include="Content\Badge\special-national-day.png" />
    <Content Include="Content\bootstrap-datetimepicker.min.css" />
    <Content Include="Content\bootstrap\bootstrap-453.css" />
    <Content Include="Content\bootstrap\bootstrap.css" />
    <Content Include="Content\cms\cms-layout.css" />
    <Content Include="Content\cms\cms_custom_style.css" />
    <Content Include="Content\cms\cms_report.css" />
    <Content Include="Content\cms\creative.css" />
    <Content Include="Content\cms\images\home.png" />
    <Content Include="Content\cms\images\search.png" />
    <Content Include="Content\customisedFileInput.css" />
    <Content Include="Content\fileinput_v5.min.css" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Content\fonts\sgds-icons.svg" />
    <Content Include="Content\global-site.css" />
    <Content Include="Content\icons\ajax-loader.gif" />
    <Content Include="Content\icons\government_building.svg" />
    <Content Include="Content\icons\icon-sg-crest.svg" />
    <Content Include="Content\icons\lock.svg" />
    <Content Include="Content\images\alert.png" />
    <Content Include="Content\images\base_map.png" />
    <Content Include="Content\images\bird.png" />
    <Content Include="Content\images\blanktag.jpg" />
    <Content Include="Content\images\btn_fb.jpg" />
    <Content Include="Content\images\butterfly.png" />
    <Content Include="Content\images\changeicon.png" />
    <Content Include="Content\images\downloadfeatures.png" />
    <Content Include="Content\images\drawcircle.png" />
    <Content Include="Content\images\drawingreset.png" />
    <Content Include="Content\images\drawline.png" />
    <Content Include="Content\images\drawpolygon.png" />
    <Content Include="Content\images\drawrectangle.png" />
    <Content Include="Content\images\firstsighting.jpg" />
    <Content Include="Content\images\geosearch.png" />
    <Content Include="Content\images\gotosightings.png" />
    <Content Include="Content\images\grid-cluster.png" />
    <Content Include="Content\images\help-hover.png" />
    <Content Include="Content\images\help.png" />
    <Content Include="Content\images\helpguide-01.jpg" />
    <Content Include="Content\images\helpguide-02.jpg" />
    <Content Include="Content\images\helpguide-03.jpg" />
    <Content Include="Content\images\helpguide-04.jpg" />
    <Content Include="Content\images\helpguide-05.jpg" />
    <Content Include="Content\images\home bkp.png" />
    <Content Include="Content\images\home.png" />
    <Content Include="Content\images\insect.png" />
    <Content Include="Content\images\layers-2x.png" />
    <Content Include="Content\images\layers.png" />
    <Content Include="Content\images\loading-sm.gif" />
    <Content Include="Content\images\loading.gif" />
    <Content Include="Content\images\map-collaps.png" />
    <Content Include="Content\images\map-collaps3.png" />
    <Content Include="Content\images\map_icon_attribute.png" />
    <Content Include="Content\images\map_icon_spatial.png" />
    <Content Include="Content\images\marker-icon-2x.png" />
    <Content Include="Content\images\marker-icon.png" />
    <Content Include="Content\images\marker-shadow.png" />
    <Content Include="Content\images\measurement.png" />
    <Content Include="Content\images\ornothologiacal.jpg" />
    <Content Include="Content\images\overlaykml.png" />
    <Content Include="Content\images\perfectten.jpg" />
    <Content Include="Content\images\placemarker.png" />
    <Content Include="Content\images\plant.png" />
    <Content Include="Content\images\profile-default.jpg" />
    <Content Include="Content\images\Red_glow.gif" />
    <Content Include="Content\images\search_input.png" />
    <Content Include="Content\images\snapshot.png" />
    <Content Include="Content\images\spinner.gif" />
    <Content Include="Content\images\time-slider.png" />
    <Content Include="Content\images\turtle.png" />
    <Content Include="Content\images\ui-bg_diagonals-thick_18_b81900_40x40.png" />
    <Content Include="Content\images\ui-bg_diagonals-thick_20_666666_40x40.png" />
    <Content Include="Content\images\ui-bg_flat_10_000000_40x100.png" />
    <Content Include="Content\images\ui-bg_glass_100_f6f6f6_1x400.png" />
    <Content Include="Content\images\ui-bg_glass_100_fdf5ce_1x400.png" />
    <Content Include="Content\images\ui-bg_glass_65_ffffff_1x400.png" />
    <Content Include="Content\images\ui-bg_gloss-wave_35_f6a828_500x100.png" />
    <Content Include="Content\images\ui-bg_highlight-soft_100_eeeeee_1x100.png" />
    <Content Include="Content\images\ui-bg_highlight-soft_75_ffe45c_1x100.png" />
    <Content Include="Content\images\ui-icons_222222_256x240.png" />
    <Content Include="Content\images\ui-icons_228ef1_256x240.png" />
    <Content Include="Content\images\ui-icons_ef8c08_256x240.png" />
    <Content Include="Content\images\ui-icons_ffd27a_256x240.png" />
    <Content Include="Content\images\ui-icons_ffffff_256x240.png" />
    <Content Include="Content\images\user-login.png" />
    <Content Include="Content\images\userlogin-hover.png" />
    <Content Include="Content\images\zoomin.png" />
    <Content Include="Content\images\zoomout.png" />
    <Content Include="Content\jquery-ui.css" />
    <Content Include="Content\jquery.mCustomScrollbar.min.css" />
    <Content Include="Content\jquery.mThumbnailScroller.min.css" />
    <Content Include="Content\jstreeInternal.css" />
    <Content Include="Content\jstree\intro.js" />
    <Content Include="Content\jstree\jstree.changed.js" />
    <Content Include="Content\jstree\jstree.checkbox.js" />
    <Content Include="Content\jstree\jstree.conditionalselect.js" />
    <Content Include="Content\jstree\jstree.contextmenu.js" />
    <Content Include="Content\jstree\jstree.dnd.js" />
    <Content Include="Content\jstree\jstree.js" />
    <Content Include="Content\jstree\jstree.massload.js" />
    <Content Include="Content\jstree\jstree.search.js" />
    <Content Include="Content\jstree\jstree.sort.js" />
    <Content Include="Content\jstree\jstree.state.js" />
    <Content Include="Content\jstree\jstree.types.js" />
    <Content Include="Content\jstree\jstree.unique.js" />
    <Content Include="Content\jstree\jstree.wholerow.js" />
    <Content Include="Content\jstree\misc.js" />
    <Content Include="Content\jstree\outro.js" />
    <Content Include="Content\jstree\sample.js" />
    <Content Include="Content\jstree\themes\default-dark\32px.png" />
    <Content Include="Content\jstree\themes\default-dark\40px.png" />
    <Content Include="Content\jstree\themes\default-dark\style.css" />
    <Content Include="Content\jstree\themes\default-dark\throbber.gif" />
    <Content Include="Content\jstree\themes\default\32px.png" />
    <Content Include="Content\jstree\themes\default\40px.png" />
    <Content Include="Content\jstree\themes\default\style.css" />
    <Content Include="Content\jstree\themes\default\throbber.gif" />
    <Content Include="Content\jstree\vakata-jstree.js" />
    <Content Include="Content\leaflet\easy-button.css" />
    <Content Include="Content\leaflet\easy-button.js" />
    <Content Include="Content\leaflet\esri-leaflet-cluster-debug.js" />
    <Content Include="Content\leaflet\esri-leaflet-geocoder-debug.js" />
    <Content Include="Content\leaflet\fgdb.js" />
    <Content Include="Content\leaflet\fgdb_prod_intra.js" />
    <Content Include="Content\leaflet\fgdb_uat_inter.js" />
    <Content Include="Content\leaflet\fgdb_uat_intra.js" />
    <Content Include="Content\leaflet\images\helpguide.png" />
    <Content Include="Content\leaflet\leaflet-image.js" />
    <Content Include="Content\leaflet\leaflet-jquerydialog.css" />
    <Content Include="Content\leaflet\leaflet-jquerydialog.js" />
    <Content Include="Content\leaflet\leaflet-src.min.js" />
    <Content Include="Content\leaflet\leaflet.fgdb.js" />
    <Content Include="Content\leaflet\leaflet.shpfile.js" />
    <Content Include="Content\leaflet\leaflet.timeline.js" />
    <Content Include="Content\leaflet\leaflet.timeline.min.css" />
    <Content Include="Content\leaflet\map-init-intranet.js" />
    <Content Include="Content\leaflet\map-init.js" />
    <Content Include="Content\leaflet\esri-leaflet-geocoder.css" />
    <Content Include="Content\leaflet\images\cancel.png" />
    <Content Include="Content\leaflet\images\cancel_%402X.png" />
    <Content Include="Content\leaflet\images\check.png" />
    <Content Include="Content\leaflet\images\check_%402X.png" />
    <Content Include="Content\leaflet\images\focus.png" />
    <Content Include="Content\leaflet\images\focus_%402X.png" />
    <Content Include="Content\leaflet\images\loading%402x.gif" />
    <Content Include="Content\leaflet\images\loading.gif" />
    <Content Include="Content\leaflet\images\rulers.png" />
    <Content Include="Content\leaflet\images\rulers_%402X.png" />
    <Content Include="Content\leaflet\images\search%402x-disabled.png" />
    <Content Include="Content\leaflet\images\search%402x.png" />
    <Content Include="Content\leaflet\images\search-disabled.png" />
    <Content Include="Content\leaflet\images\search.png" />
    <Content Include="Content\leaflet\images\start.png" />
    <Content Include="Content\leaflet\images\start_%402X.png" />
    <Content Include="Content\leaflet\images\trash.png" />
    <Content Include="Content\leaflet\images\trash_%402X.png" />
    <Content Include="Content\leaflet\leaflet-measure.css" />
    <Content Include="Content\leaflet\leaflet-measure.js" />
    <Content Include="Content\leaflet\Leaflet.Dialog.css" />
    <Content Include="Content\leaflet\Leaflet.Dialog.js" />
    <Content Include="Content\leaflet\leaflet.filelayer.js" />
    <Content Include="Content\leaflet\Leaflet.gridCluster.css" />
    <Content Include="Content\leaflet\Leaflet.gridCluster.js" />
    <Content Include="Content\leaflet\leaflet.spin.js" />
    <Content Include="Content\leaflet\proj4-src.min.js" />
    <Content Include="Content\leaflet\proj4leaflet.min.js" />
    <Content Include="Content\leaflet\spin.js" />
    <Content Include="Content\magnific-popup\jquery.magnific-popup.js" />
    <Content Include="Content\magnific-popup\jquery.magnific-popup.min.js" />
    <Content Include="Content\magnific-popup\magnific-popup.css" />
    <Content Include="Content\profile.css" />
    <Content Include="Content\public-layout.css" />
    <Content Include="Content\sgds.css" />
    <Content Include="Content\site_main.css" />
    <Content Include="Content\cms\cmsSite.css" />
    <Content Include="Content\cms\images\blome-centerLogo.png" />
    <Content Include="Content\cms\images\nationalPark-Left1.png" />
    <Content Include="Content\creative.css" />
    <Content Include="Content\leaflet\customize.css" />
    <Content Include="Content\leaflet\images\layers-2x.png" />
    <Content Include="Content\leaflet\images\layers.png" />
    <Content Include="Content\leaflet\images\marker-icon-2x.png" />
    <Content Include="Content\leaflet\images\marker-icon.png" />
    <Content Include="Content\leaflet\images\marker-shadow.png" />
    <Content Include="Content\leaflet\Leaflet.Editable.js" />
    <Content Include="Content\leaflet\MarkerCluster.css" />
    <Content Include="Content\leaflet\MarkerCluster.Default.css" />
    <Content Include="Content\custom_style.css" />
    <Content Include="Content\datepicker.css" />
    <Content Include="Content\fileinput.css" />
    <Content Include="Content\font-awesome.min.css" />
    <Content Include="Content\ie10-viewport-bug-workaround.css" />
    <Content Include="Content\images\nophotoavailable.jpg" />
    <Content Include="Content\jquery.mCustomScrollbar.css" />
    <Content Include="Content\jquery.mThumbnailScroller.css" />
    <Content Include="Content\leaflet\leaflet.css" />
    <Content Include="Content\fonts\bootstrap\glyphicons-halflings-regular.woff2" />
    <Content Include="Content\fonts\flexslider-icon.svg" />
    <Content Include="Content\fonts\fontawesome-webfont.svg" />
    <Content Include="Content\icons\android-chrome-144x144.png" />
    <Content Include="Content\icons\android-chrome-192x192.png" />
    <Content Include="Content\icons\android-chrome-36x36.png" />
    <Content Include="Content\icons\android-chrome-48x48.png" />
    <Content Include="Content\icons\android-chrome-72x72.png" />
    <Content Include="Content\icons\android-chrome-96x96.png" />
    <Content Include="Content\icons\atom-icon-48x48.png" />
    <Content Include="Content\icons\atom-logo-96x48.png" />
    <Content Include="Content\icons\open-graph-1200x630.png" />
    <Content Include="Content\fonts\FontAwesome.otf" />
    <Content Include="Content\fonts\fontawesome-webfont.woff2" />
    <Content Include="Content\fonts\fontawesome-webfont.woff" />
    <Content Include="Content\fonts\fontawesome-webfont.ttf" />
    <Content Include="Content\fonts\fontawesome-webfont.eot" />
    <Content Include="Content\fontawesome\variables.less" />
    <Content Include="Content\fontawesome\stacked.less" />
    <Content Include="Content\fontawesome\rotated-flipped.less" />
    <Content Include="Content\fontawesome\path.less" />
    <Content Include="Content\fontawesome\mixins.less" />
    <Content Include="Content\fontawesome\list.less" />
    <Content Include="Content\fontawesome\larger.less" />
    <Content Include="Content\fontawesome\icons.less" />
    <Content Include="Content\fontawesome\font-awesome.less" />
    <Content Include="Content\fontawesome\fixed-width.less" />
    <Content Include="Content\fontawesome\core.less" />
    <Content Include="Content\fontawesome\bordered-pulled.less" />
    <Content Include="Content\fontawesome\animated.less" />
    <Content Include="Areas\Admin\Views\web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Content\fonts\flexslider-icon.eot" />
    <Content Include="Content\fonts\flexslider-icon.ttf" />
    <Content Include="Content\fonts\flexslider-icon.woff" />
    <Content Include="Areas\Admin\Views\_ViewStart.cshtml" />
    <Content Include="Content\navbar-fixed-top.css" />
    <Content Include="PDF Templates\feedbackapi00123.html" />
    <!--
    <Content Include="Content\Survey\ImageUpload\Asian Glossy Starling.jpg" />
    <Content Include="Content\Survey\ImageUpload\Black-naped Oriole.jpg" />
    <Content Include="Content\Survey\ImageUpload\Brown-throated Sunbird.jpg" />
    <Content Include="Content\Survey\ImageUpload\Common Hill Myna Starling.jpg" />
    <Content Include="Content\Survey\ImageUpload\Common Myna Starling.jpg" />
    <Content Include="Content\Survey\ImageUpload\Crimson Sunbird.jpg" />
    <Content Include="Content\Survey\ImageUpload\Javan Myna Starling.jpg" />
    <Content Include="Content\Survey\ImageUpload\Little Spiderhunter Sunbird.jpg" />
    <Content Include="Content\Survey\ImageUpload\Olive-backed Sunbird.jpg" />
    <Content Include="Content\Survey\ImageUpload\Van Hasselt%27s Sunbird.jpg" />
    <Content Include="google4843b269cc4a147b.html" />
    <Content Include="PDF Templates\border_img.jpg" />
    <Content Include="PDF Templates\Corporate-logo2.jpg" />
    <Content Include="PDF Templates\PermitApplicationTemplate.html" />
	-->
    <Content Include="robots.txt" />
    <Content Include="Scripts\assignMainApplicant.js" />
    <Content Include="Scripts\banner.js" />
    <Content Include="Scripts\bootstrap.bundle.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\csv2geojson.js" />
    <Content Include="Scripts\esm\popper-utils.js" />
    <Content Include="Scripts\esm\popper-utils.min.js" />
    <Content Include="Scripts\esm\popper.js" />
    <Content Include="Scripts\esm\popper.min.js" />
    <Content Include="Scripts\fileinput_v5.min.js" />
    <Content Include="Scripts\JQFix.js" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\AssignMainApplicant.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\CoralCollectionSummary.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\CoralCollectionDetails.cshtml" />
    <Content Include="Scripts\index.js.flow" />
    <Content Include="Scripts\esm\popper.min.js.map" />
    <Content Include="Scripts\esm\popper.js.map" />
    <Content Include="Scripts\esm\popper-utils.min.js.map" />
    <Content Include="Scripts\esm\popper-utils.js.map" />
    <Content Include="Scripts\bootstrap.min.js.map" />
    <Content Include="Scripts\bootstrap.js.map" />
    <Content Include="Scripts\bootstrap.bundle.min.js.map" />
    <Content Include="Scripts\bootstrap.bundle.js.map" />
    <Content Include="Areas\Admin\Views\AuditTrail\AuditTrail.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\_PartialUnAuthorize.cshtml" />
    <Content Include="Content\fonts\sgds-icons.ttf" />
    <Content Include="Content\fonts\sgds-icons.woff" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\DetailsPermitDraft.cshtml" />
    <Content Include="Content\Download\AcknowledgementSection.pdf" />
    <Content Include="Content\Download\survey_kml_sample.kml" />
    <Content Include="Content\images\appstore.webp" />
    <Content Include="Content\images\googleplay.webp" />
    <Content Include="Content\images\helpguide-01.webp" />
    <Content Include="Content\images\helpguide-02.webp" />
    <Content Include="Content\images\helpguide-03.webp" />
    <Content Include="Content\images\helpguide-04.webp" />
    <Content Include="Content\images\helpguide-05.webp" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Areas\Admin\Views\Resource\ErrorPage.cshtml" />
    <None Include="NLog.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\PublishProfiles\UAT Internet.pubxml" />
    <None Include="Scripts\jquery-3.5.1.intellisense.js" />
    <Content Include="Scripts\jquery-3.5.1.js" />
    <Content Include="Scripts\jquery-3.5.1.min.js" />
    <Content Include="Scripts\jquery-3.5.1.slim.js" />
    <Content Include="Scripts\jquery-3.5.1.slim.min.js" />
    <Content Include="Scripts\jquery-ajax-native.js" />
    <Content Include="Scripts\jquery-ui.css" />
    <Content Include="Scripts\jquery-ui.min.css" />
    <Content Include="Scripts\jquery-ui.min.js" />
    <Content Include="Scripts\jquery-ui.structure.css" />
    <Content Include="Scripts\jquery-ui.structure.min.css" />
    <Content Include="Scripts\jquery-ui.theme.css" />
    <Content Include="Scripts\jquery-ui.theme.min.css" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\maskedSites.js" />
    <Content Include="Scripts\member-ack.js" />
    <Content Include="Scripts\moment-with-locales.min.js" />
    <Content Include="Scripts\moment.js" />
    <Content Include="Scripts\moment.min.js" />
    <Content Include="Scripts\popper-utils.js" />
    <Content Include="Scripts\popper-utils.min.js" />
    <Content Include="Scripts\popper.js" />
    <Content Include="Scripts\popper.min.js" />
    <Content Include="Scripts\sgds.js" />
    <Content Include="Scripts\shp.js" />
    <Content Include="Scripts\maintenance.js" />
    <Content Include="Scripts\src\index.js" />
    <Content Include="Scripts\src\methods\defaults.js" />
    <Content Include="Scripts\src\methods\destroy.js" />
    <Content Include="Scripts\src\methods\disableEventListeners.js" />
    <Content Include="Scripts\src\methods\enableEventListeners.js" />
    <Content Include="Scripts\src\methods\placements.js" />
    <Content Include="Scripts\src\methods\update.js" />
    <Content Include="Scripts\src\modifiers\applyStyle.js" />
    <Content Include="Scripts\src\modifiers\arrow.js" />
    <Content Include="Scripts\src\modifiers\computeStyle.js" />
    <Content Include="Scripts\src\modifiers\flip.js" />
    <Content Include="Scripts\src\modifiers\hide.js" />
    <Content Include="Scripts\src\modifiers\index.js" />
    <Content Include="Scripts\src\modifiers\inner.js" />
    <Content Include="Scripts\src\modifiers\keepTogether.js" />
    <Content Include="Scripts\src\modifiers\offset.js" />
    <Content Include="Scripts\src\modifiers\preventOverflow.js" />
    <Content Include="Scripts\src\modifiers\shift.js" />
    <Content Include="Scripts\src\utils\clockwise.js" />
    <Content Include="Scripts\src\utils\computeAutoPlacement.js" />
    <Content Include="Scripts\src\utils\debounce.js" />
    <Content Include="Scripts\src\utils\find.js" />
    <Content Include="Scripts\src\utils\findCommonOffsetParent.js" />
    <Content Include="Scripts\src\utils\findIndex.js" />
    <Content Include="Scripts\src\utils\getBordersSize.js" />
    <Content Include="Scripts\src\utils\getBoundaries.js" />
    <Content Include="Scripts\src\utils\getBoundingClientRect.js" />
    <Content Include="Scripts\src\utils\getClientRect.js" />
    <Content Include="Scripts\src\utils\getFixedPositionOffsetParent.js" />
    <Content Include="Scripts\src\utils\getOffsetParent.js" />
    <Content Include="Scripts\src\utils\getOffsetRect.js" />
    <Content Include="Scripts\src\utils\getOffsetRectRelativeToArbitraryNode.js" />
    <Content Include="Scripts\src\utils\getOppositePlacement.js" />
    <Content Include="Scripts\src\utils\getOppositeVariation.js" />
    <Content Include="Scripts\src\utils\getOuterSizes.js" />
    <Content Include="Scripts\src\utils\getParentNode.js" />
    <Content Include="Scripts\src\utils\getPopperOffsets.js" />
    <Content Include="Scripts\src\utils\getReferenceNode.js" />
    <Content Include="Scripts\src\utils\getReferenceOffsets.js" />
    <Content Include="Scripts\src\utils\getRoot.js" />
    <Content Include="Scripts\src\utils\getRoundedOffsets.js" />
    <Content Include="Scripts\src\utils\getScroll.js" />
    <Content Include="Scripts\src\utils\getScrollParent.js" />
    <Content Include="Scripts\src\utils\getStyleComputedProperty.js" />
    <Content Include="Scripts\src\utils\getSupportedPropertyName.js" />
    <Content Include="Scripts\src\utils\getViewportOffsetRectRelativeToArtbitraryNode.js" />
    <Content Include="Scripts\src\utils\getWindow.js" />
    <Content Include="Scripts\src\utils\getWindowSizes.js" />
    <Content Include="Scripts\src\utils\includeScroll.js" />
    <Content Include="Scripts\src\utils\index.js" />
    <Content Include="Scripts\src\utils\isBrowser.js" />
    <Content Include="Scripts\src\utils\isFixed.js" />
    <Content Include="Scripts\src\utils\isFunction.js" />
    <Content Include="Scripts\src\utils\isIE.js" />
    <Content Include="Scripts\src\utils\isModifierEnabled.js" />
    <Content Include="Scripts\src\utils\isModifierRequired.js" />
    <Content Include="Scripts\src\utils\isNumeric.js" />
    <Content Include="Scripts\src\utils\isOffsetContainer.js" />
    <Content Include="Scripts\src\utils\removeEventListeners.js" />
    <Content Include="Scripts\src\utils\runModifiers.js" />
    <Content Include="Scripts\src\utils\setAttributes.js" />
    <Content Include="Scripts\src\utils\setStyles.js" />
    <Content Include="Scripts\src\utils\setupEventListeners.js" />
    <Content Include="Scripts\survey.js" />
    <Content Include="Scripts\sweetalert.min.js" />
    <Content Include="Scripts\tinymce-custom-init.js" />
    <Content Include="Content\toggle-switch.css" />
    <Content Include="Areas\Admin\Views\Shared\_Layout.cshtml" />
    <Content Include="Areas\Admin\Views\Home\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Home\Header.cshtml" />
    <Content Include="Areas\Admin\Views\Shared\_PartialHeader.cshtml" />
    <Content Include="Areas\Admin\Views\Shared\_PartialFooter.cshtml" />
    <Content Include="Areas\Admin\Views\Shared\_PartialMenu.cshtml" />
    <Content Include="Areas\Admin\Views\Home\BackgroundImage.cshtml" />
    <Content Include="Areas\Admin\Views\Home\MainPageTitle.cshtml" />
    <Content Include="Areas\Admin\Views\Home\SGBioAtlasRow.cshtml" />
    <Content Include="Areas\Admin\Views\Home\SelectedSightingsRow.cshtml" />
    <Content Include="Areas\Admin\Views\Home\HighlightsRow.cshtml" />
    <Content Include="Areas\Admin\Views\Home\IntranetHighlightsRow.cshtml" />
    <Content Include="Areas\Admin\Views\Home\Footer.cshtml" />
    <Content Include="Areas\Admin\Views\Shared\_PartialBreadcrumbs.cshtml" />
    <Content Include="Areas\Admin\Views\Home\_PartialAddHighlights.cshtml" />
    <Content Include="Areas\Admin\Views\Home\_PartialPagingHighlights.cshtml" />
    <Content Include="Areas\Admin\Views\Shared\DisplayTemplates\HighlightViewModel.cshtml" />
    <Content Include="Areas\Admin\Views\Home\_PartialEditHighlights.cshtml" />
    <Content Include="Areas\Admin\Views\Home\_PartialPagedSightingsSelected.cshtml" />
    <Content Include="Areas\Admin\Views\Shared\DisplayTemplates\SightingsSelectedItem.cshtml" />
    <Content Include="Areas\Admin\Views\SystemParameters\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Shared\_LoginPartial.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\FeaturedSightings.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\FlaggedSightings.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\InappropriateSightings.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\AutofilteredSightings.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\CategoriesList.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\SightingEdit.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\SightingVerify.cshtml" />
    <Content Include="Areas\Admin\Views\Projects\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Projects\ProjectDetail.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\AllSightings.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\ThreatenedSpecies.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\SightingParameters.cshtml" />
    <Content Include="Areas\Admin\Views\Projects\AllProjects.cshtml" />
    <Content Include="Areas\Admin\Views\Resource\ResourceStepOne.cshtml" />
    <Content Include="Areas\Admin\Views\Resource\ResourceStepTwo.cshtml" />
    <Content Include="Areas\Admin\Views\Resource\ResourceStepThree.cshtml" />
    <Content Include="Areas\Admin\Views\Resource\ResourceStepFour.cshtml" />
    <Content Include="Areas\Admin\Views\DocumentType\DocumentType.cshtml" />
    <Content Include="Areas\Admin\Views\StructuredDataTemplate\StructuredDataTemplate.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\Index.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\_PartialPagingApplicationStatuses.cshtml" />
    <Content Include="Areas\Admin\Views\Resource\NewPermissionGroup.cshtml" />
    <Content Include="Areas\Admin\Views\Resource\ResourceDocumentList.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\DetailsPermit.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\DisplayTemplates\FieldSurveyTeamViewModel.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\_PartialQuickViewModal.cshtml" />
    <Content Include="Areas\Admin\Views\MaintainableList\MaintainableListPage.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\DetailsSite.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\EditorTemplates\EditSitePermissionViewModel.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\EditorTemplates\EditSitePermissionSiteViewModel.cshtml" />
    <Content Include="Areas\Admin\Views\EmailTemplate\EmailTemplate.cshtml" />
    <Content Include="Areas\Admin\Views\Users\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Users\ManageUser.cshtml" />
    <Content Include="Areas\Admin\Views\Users\ManageUserGroup.cshtml" />
    <Content Include="Areas\Admin\Views\Users\_PartialUserDetail.cshtml" />
    <Content Include="Areas\Admin\Views\Users\_PartialUserGroupRoot.cshtml" />
    <Content Include="Areas\Admin\Views\Users\_PartialUserGroupSubGroup.cshtml" />
    <Content Include="Areas\Admin\Views\Users\_PartialUserGroupSubSubGroup.cshtml" />
    <Content Include="Areas\Admin\Views\Users\_PartialUserGroupTree.cshtml" />
    <Content Include="Areas\Admin\Views\Users\_PartialUserGroupAddNewParent.cshtml" />
    <Content Include="Areas\Admin\Views\Users\_PartialUserGroupAddNewSubGroup.cshtml" />
    <Content Include="PDF Templates\AccessPassTemplate.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="PDF Templates\PermitLetterTemplate_Part2.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="PDF Templates\PermitLetterTemplate_Part1.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Scripts\applicationstatusDetails.js" />
    <Content Include="Scripts\cms\researchTemplates\researchPermitTemplate.js" />
    <Content Include="Scripts\cms\research\permitletterCreateEdit.js" />
    <Content Include="Scripts\tinymce\icons\default\icons.js" />
    <Content Include="Scripts\tinymce\icons\default\icons.min.js" />
    <Content Include="Scripts\tinymce\models\dom\model.js" />
    <Content Include="Scripts\tinymce\models\dom\model.min.js" />
    <Content Include="Scripts\tinymce\plugins\accordion\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\accordion\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\advlist\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\advlist\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\anchor\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\anchor\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\autolink\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\autolink\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\autoresize\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\autoresize\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\autosave\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\autosave\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\charmap\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\charmap\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\codesample\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\codesample\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\code\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\code\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\directionality\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\directionality\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\emoticons\js\emojiimages.js" />
    <Content Include="Scripts\tinymce\plugins\emoticons\js\emojiimages.min.js" />
    <Content Include="Scripts\tinymce\plugins\emoticons\js\emojis.js" />
    <Content Include="Scripts\tinymce\plugins\emoticons\js\emojis.min.js" />
    <Content Include="Scripts\tinymce\plugins\emoticons\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\emoticons\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\fullscreen\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\fullscreen\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\ar.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\bg_BG.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\ca.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\cs.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\da.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\de.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\el.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\en.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\es.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\eu.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\fa.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\fi.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\fr_FR.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\he_IL.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\hi.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\hr.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\hu_HU.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\id.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\it.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\ja.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\kk.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\ko_KR.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\ms.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\nb_NO.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\nl.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\pl.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\pt_BR.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\pt_PT.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\ro.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\ru.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\sk.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\sl_SI.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\sv_SE.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\th_TH.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\tr.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\uk.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\vi.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\zh_CN.js" />
    <Content Include="Scripts\tinymce\plugins\help\js\i18n\keynav\zh_TW.js" />
    <Content Include="Scripts\tinymce\plugins\help\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\help\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\image\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\image\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\importcss\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\importcss\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\insertdatetime\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\insertdatetime\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\link\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\link\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\lists\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\lists\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\media\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\media\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\nonbreaking\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\nonbreaking\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\pagebreak\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\pagebreak\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\placeholder\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\placeholder\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\preview\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\preview\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\quickbars\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\quickbars\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\save\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\save\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\searchreplace\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\searchreplace\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\table\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\table\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\visualblocks\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\visualblocks\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\visualchars\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\visualchars\plugin.min.js" />
    <Content Include="Scripts\tinymce\plugins\wordcount\plugin.js" />
    <Content Include="Scripts\tinymce\plugins\wordcount\plugin.min.js" />
    <Content Include="Scripts\tinymce\skins\content\dark\content.css" />
    <Content Include="Scripts\tinymce\skins\content\dark\content.js" />
    <Content Include="Scripts\tinymce\skins\content\dark\content.min.css" />
    <Content Include="Scripts\tinymce\skins\content\default\content.css" />
    <Content Include="Scripts\tinymce\skins\content\default\content.js" />
    <Content Include="Scripts\tinymce\skins\content\default\content.min.css" />
    <Content Include="Scripts\tinymce\skins\content\document\content.css" />
    <Content Include="Scripts\tinymce\skins\content\document\content.js" />
    <Content Include="Scripts\tinymce\skins\content\document\content.min.css" />
    <Content Include="Scripts\tinymce\skins\content\tinymce-5-dark\content.css" />
    <Content Include="Scripts\tinymce\skins\content\tinymce-5-dark\content.js" />
    <Content Include="Scripts\tinymce\skins\content\tinymce-5-dark\content.min.css" />
    <Content Include="Scripts\tinymce\skins\content\tinymce-5\content.css" />
    <Content Include="Scripts\tinymce\skins\content\tinymce-5\content.js" />
    <Content Include="Scripts\tinymce\skins\content\tinymce-5\content.min.css" />
    <Content Include="Scripts\tinymce\skins\content\writer\content.css" />
    <Content Include="Scripts\tinymce\skins\content\writer\content.js" />
    <Content Include="Scripts\tinymce\skins\content\writer\content.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\content.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\content.inline.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\content.inline.js" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\content.inline.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\content.js" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\content.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\skin.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\skin.js" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\skin.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\skin.shadowdom.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\skin.shadowdom.js" />
    <Content Include="Scripts\tinymce\skins\ui\oxide-dark\skin.shadowdom.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\content.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\content.inline.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\content.inline.js" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\content.inline.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\content.js" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\content.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\skin.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\skin.js" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\skin.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\skin.shadowdom.css" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\skin.shadowdom.js" />
    <Content Include="Scripts\tinymce\skins\ui\oxide\skin.shadowdom.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\content.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\content.inline.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\content.inline.js" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\content.inline.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\content.js" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\content.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\skin.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\skin.js" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\skin.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\skin.shadowdom.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\skin.shadowdom.js" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5-dark\skin.shadowdom.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\content.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\content.inline.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\content.inline.js" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\content.inline.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\content.js" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\content.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\skin.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\skin.js" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\skin.min.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\skin.shadowdom.css" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\skin.shadowdom.js" />
    <Content Include="Scripts\tinymce\skins\ui\tinymce-5\skin.shadowdom.min.css" />
    <Content Include="Scripts\tinymce\themes\silver\theme.js" />
    <Content Include="Scripts\tinymce\themes\silver\theme.min.js" />
    <Content Include="Scripts\tinymce\tinymce.js" />
    <Content Include="Scripts\tinymce\tinymce.min.js" />
    <Content Include="Scripts\turf.min.js" />
    <Content Include="Scripts\umd\popper-utils.js" />
    <Content Include="Scripts\umd\popper-utils.min.js" />
    <Content Include="Scripts\umd\popper.js" />
    <Content Include="Scripts\umd\popper.min.js" />
    <Content Include="Scripts\userSuggest.js" />
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="Views\Shared\_PartialGenerateAntiForgeryToken.cshtml" />
    <Content Include="Areas\Admin\Views\Users\_PartialUserGroupEditParent.cshtml" />
    <Content Include="Areas\Admin\Views\Users\_PartialPagingUsers.cshtml" />
    <Content Include="Areas\Admin\Views\Sites\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Sites\DisplayTemplates\SiteAssignment.cshtml" />
    <Content Include="Areas\Admin\Views\Sites\_PartialAddLocation.cshtml" />
    <Content Include="Areas\Admin\Views\ResearchPermitTemplate\HeaderFooterTemplate.cshtml" />
    <Content Include="Areas\Admin\Views\ResearchPermitTemplate\PermitLetterTemplate.cshtml" />
    <Content Include="Areas\Admin\Views\ResearchPermitTemplate\TermsConditionTemplate.cshtml" />
    <Content Include="Areas\Admin\Views\Passes\Edit.cshtml" />
    <Content Include="Areas\Admin\Views\Passes\EditorTemplates\PermitPassEditViewModel.cshtml" />
    <Content Include="Areas\Admin\Views\Passes\Create.cshtml" />
    <Content Include="Areas\Admin\Views\Passes\EditorTemplates\PermitPassCreateViewModel.cshtml" />
    <Content Include="Areas\Admin\Views\Letters\Create.cshtml" />
    <Content Include="Areas\Admin\Views\Letters\Edit.cshtml" />
    <Content Include="Areas\Admin\Views\Shared\_Notifications.cshtml" />
    <Content Include="Areas\Admin\Views\Research\MaintainableListPage.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\Users.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\Sightings.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\Resource.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\Projects.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\ResearchPermitApplication.cshtml" />
    <Content Include="Areas\Admin\Views\Discussions\_PartialUnsubscribe.cshtml" />
    <Content Include="Areas\Admin\Views\Discussions\_PartialUserName.cshtml" />
    <Content Include="Areas\Admin\Views\Discussions\_PartialUserProfilePicture.cshtml" />
    <Content Include="Areas\Admin\Views\Discussions\DiscussionForum.cshtml" />
    <Content Include="Areas\Admin\Views\AuditTrail\AuditTrailPage.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\Guide.cshtml" />
    <Content Include="Areas\Admin\Views\Sites\SiteAssignmentDetail.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\SightingsPerUser.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\SightingVotesPerUser.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\SightingTop10.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\ProjectMember.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\ProjectSighting.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\ProjectPopularCategories.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\SightingsNumber.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\NewUsers.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\ActiveUsers.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\LoginNumber.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\BadgeUserNumber.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\GroupUserNumber.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\ResourcePopularLocations.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\ResourcePopularCategories.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\ResourceNumberOfUploads.cshtml" />
    <Content Include="Areas\Admin\Views\MaintenanceNotice\Index.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\_PartialSiteStatus.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\ResponseTimeToIssuePermit.cshtml" />
    <Content Include="Areas\Admin\Views\Reports\ResponseTimeToApplication.cshtml" />
    <Content Include="Areas\Admin\Views\Resource\MyResourceList.cshtml" />
    <Content Include="Areas\Admin\Views\Resource\ResourceList.cshtml" />
    <Content Include="Areas\Admin\Views\Resource\ViewMetadata.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\_PartialUploadCsvSpecies.cshtml" />
    <Content Include="Areas\Admin\Views\Resource\_PartialGetUploaderName.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\MaskedSites.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\Calendar.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\Inbox.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\_PartialCalendarRow.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\_PartialSiteVisiteProcess.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\_PartialSiteVisiteProcessForm.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\_PartialTotalUnreadCount.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\ErrorPage.cshtml" />
    <Content Include="Areas\Admin\Views\Sightings\_ListSightingComment.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\_PartialUploadReport.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\_PartialQuickView.cshtml" />
    <Content Include="Areas\Admin\Views\Resource\ApproveDownloadRequest.cshtml" />
    <Content Include="Areas\Admin\Views\ApplicationStatus\_PartialNotFound.cshtml" />
    <Content Include="Areas\Admin\Views\EmailTemplate\SendEmailTemplate.cshtml" />
    <Content Include="Areas\Admin\Views\Surveys\AllSurveys.cshtml" />
    <Content Include="Areas\Admin\Views\Surveys\SurveyDetail.cshtml" />
    <Content Include="Areas\Admin\Views\Surveys\_AdminSpeciesListPartial.cshtml" />
    <Content Include="Areas\Admin\Views\Home\EnterOTP.cshtml" />
    <Content Include="Areas\Admin\Views\Feedback\AllFeedbacks.cshtml" />
    <Content Include="Areas\Admin\Views\AuditTrail\AuditLogPage.cshtml" />
    <None Include="compilerconfig.json" />
    <None Include="compilerconfig.json.defaults">
      <DependentUpon>compilerconfig.json</DependentUpon>
    </None>
    <Content Include="Content\locations\318" />
    <Content Include="Content\locations\3189" />
    <Content Include="Content\locations\31899" />
    <Content Include="Content\locations\318995" />
    <Content Include="Content\locations\beach" />
    <Content Include="Content\locations\park" />
    <Content Include="Content\locations\reservoir" />
    <Content Include="Content\locations\toa payoh" />
    <Content Include="Content\jstree\themes\base.less" />
    <Content Include="Content\jstree\themes\default-dark\style.less" />
    <Content Include="Content\jstree\themes\default\style.less" />
    <Content Include="Content\jstree\themes\main.less" />
    <Content Include="Content\jstree\themes\mixins.less" />
    <Content Include="Content\jstree\themes\responsive.less" />
    <Content Include="Content\leaflet\leaflet-src.map" />
    <Content Include="Content\Download\AcknowledgementSection.doc" />
    <Content Include="nlog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="NWebsecConfig\HttpHeaderSecurityModuleConfig.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\PublishProfiles\AWS_Internet.pubxml" />
    <None Include="Properties\PublishProfiles\AWS_Intranet.pubxml" />
    <None Include="Properties\PublishProfiles\Live_Internet.pubxml" />
    <None Include="Properties\PublishProfiles\Live_Intranet.pubxml" />
    <None Include="Properties\PublishProfiles\Release Internet.pubxml" />
    <None Include="Properties\PublishProfiles\Release Intranet.pubxml" />
    <None Include="Properties\PublishProfiles\Release_Internet.pubxml" />
    <None Include="Properties\PublishProfiles\Release_Intranet.pubxml" />
    <None Include="Properties\PublishProfiles\Staging AWS Internet.pubxml" />
    <None Include="Properties\PublishProfiles\Staging AWS Intranet.pubxml" />
    <None Include="Properties\PublishProfiles\StagingIntranet.pubxml" />
    <None Include="Properties\PublishProfiles\Staging_Internet.pubxml" />
    <None Include="Properties\PublishProfiles\Staging_Intranet.pubxml" />
    <None Include="Properties\PublishProfiles\UAT Intranet.pubxml" />
    <None Include="Properties\PublishProfiles\UATIntranet.pubxml" />
    <None Include="Properties\PublishProfiles\UAT_Internet.pubxml" />
    <None Include="Properties\PublishProfiles\UAT_Intranet.pubxml" />
    <Content Include="Content\bootstrap\site.css">
      <DependentUpon>site.less</DependentUpon>
    </Content>
    <Content Include="Content\bootstrap\site.min.css">
      <DependentUpon>site.css</DependentUpon>
    </Content>
    <Content Include="Content\fontawesome\Site.css">
      <DependentUpon>Site.less</DependentUpon>
    </Content>
    <Content Include="Content\fontawesome\Site.min.css">
      <DependentUpon>Site.css</DependentUpon>
    </Content>
    <Content Include="Content\fonts\fontawesome\fontawesome-webfont.svg" />
    <Content Include="Content\fonts\bootstrap\glyphicons-halflings-regular.svg" />
    <Content Include="Content\icons\apple-touch-icon-114x114.png" />
    <Content Include="Content\icons\apple-touch-icon-120x120.png" />
    <Content Include="Content\icons\apple-touch-icon-144x144.png" />
    <Content Include="Content\icons\apple-touch-icon-152x152.png" />
    <Content Include="Content\icons\apple-touch-icon-180x180.png" />
    <Content Include="Content\icons\apple-touch-icon-57x57.png" />
    <Content Include="Content\icons\apple-touch-icon-60x60.png" />
    <Content Include="Content\icons\apple-touch-icon-72x72.png" />
    <Content Include="Content\icons\apple-touch-icon-76x76.png" />
    <Content Include="Content\icons\apple-touch-icon-precomposed.png" />
    <Content Include="Content\icons\apple-touch-icon.png" />
    <Content Include="Content\icons\apple-touch-startup-image-1496x2048.png" />
    <Content Include="Content\icons\apple-touch-startup-image-1536x2008.png" />
    <Content Include="Content\icons\apple-touch-startup-image-320x460.png" />
    <Content Include="Content\icons\apple-touch-startup-image-640x1096.png" />
    <Content Include="Content\icons\apple-touch-startup-image-640x920.png" />
    <Content Include="Content\icons\apple-touch-startup-image-748x1024.png" />
    <Content Include="Content\icons\apple-touch-startup-image-768x1004.png" />
    <Content Include="Content\icons\favicon-16x16.png" />
    <Content Include="Content\icons\favicon-192x192.png" />
    <Content Include="Content\icons\favicon-32x32.png" />
    <Content Include="Content\icons\favicon-96x96.png" />
    <Content Include="Content\images\about-hover.png" />
    <Content Include="Content\images\about.png" />
    <Content Include="Content\images\appstore.png" />
    <Content Include="Content\images\border_img.jpg" />
    <Content Include="Content\images\comm_icon1.png" />
    <Content Include="Content\images\comm_icon2.png" />
    <Content Include="Content\images\Corporate-logo.jpg" />
    <Content Include="Content\images\Corporate-logo2.jpg" />
    <Content Include="Content\images\dummy-user.jpg" />
    <Content Include="Content\images\fb.jpg" />
    <Content Include="Content\images\featured1.png" />
    <Content Include="Content\images\featured2.png" />
    <Content Include="Content\images\featured3.png" />
    <Content Include="Content\images\featured4.png" />
    <Content Include="Content\images\featured5.png" />
    <Content Include="Content\images\featured6.png" />
    <Content Include="Content\images\featured7.png" />
    <Content Include="Content\images\filter.png" />
    <Content Include="Content\images\gallery-1.jpg" />
    <Content Include="Content\images\googleplay.png" />
    <Content Include="Content\images\govt-logo.jpg" />
    <Content Include="Content\images\headerbg.jpg" />
    <Content Include="Content\images\highlight-bg.png" />
    <Content Include="Content\images\Highlights1.jpg" />
    <Content Include="Content\images\Highlights2.jpg" />
    <Content Include="Content\images\Highlights3.jpg" />
    <Content Include="Content\images\Highlights4.jpg" />
    <Content Include="Content\images\img1.jpg" />
    <Content Include="Content\images\img2.jpg" />
    <Content Include="Content\images\img3.jpg" />
    <Content Include="Content\images\img4.jpg" />
    <Content Include="Content\images\inHighlights1.jpg" />
    <Content Include="Content\images\inHighlights2.jpg" />
    <Content Include="Content\images\inHighlights3.jpg" />
    <Content Include="Content\images\inHighlights4.jpg" />
    <Content Include="Content\images\liked.png" />
    <Content Include="Content\images\login-hover.png" />
    <Content Include="Content\images\login.png" />
    <Content Include="Content\images\logo.png" />
    <Content Include="Content\images\logok.png" />
    <Content Include="Content\images\map-hover.png" />
    <Content Include="Content\images\map-image.jpg" />
    <Content Include="Content\images\map.png" />
    <Content Include="Content\images\map_icon.png" />
    <Content Include="Content\images\map_search.png" />
    <Content Include="Content\images\more-hover.png" />
    <Content Include="Content\images\more.png" />
    <Content Include="Content\images\navi.png" />
    <Content Include="Content\images\nparks.png" />
    <Content Include="Content\images\popup-map.jpg" />
    <Content Include="Content\images\research-hover.png" />
    <Content Include="Content\images\research.png" />
    <Content Include="Content\images\resource-hover.png" />
    <Content Include="Content\images\resource.png" />
    <Content Include="Content\images\s1.jpg" />
    <Content Include="Content\images\s2.jpg" />
    <Content Include="Content\images\s3.jpg" />
    <Content Include="Content\images\s4.jpg" />
    <Content Include="Content\images\s5.jpg" />
    <Content Include="Content\images\s6.jpg" />
    <Content Include="Content\images\s7.jpg" />
    <Content Include="Content\images\sdnaglobal-jpeg.jpg" />
    <Content Include="Content\images\search-hover.png" />
    <Content Include="Content\images\search.png" />
    <Content Include="Content\images\sighting-hover.png" />
    <Content Include="Content\images\sighting.png" />
    <Content Include="Content\images\sightingdetail.jpg" />
    <Content Include="Content\images\sight_map.jpg" />
    <Content Include="Content\images\speciens-1.jpg" />
    <Content Include="Content\images\speciens-2.jpg" />
    <Content Include="Content\images\speciens-3.jpg" />
    <Content Include="Content\images\speciens-4.jpg" />
    <Content Include="Content\images\speciens-5.jpg" />
    <Content Include="Content\images\speciens-6.jpg" />
    <Content Include="Content\images\title-bg.png" />
    <Content Include="Content\images\tools.png" />
    <Content Include="Content\images\Untitled-1.png" />
    <Content Include="favicon.ico" />
    <Content Include="Content\icons\mstile-144x144.png" />
    <Content Include="Content\icons\mstile-150x150.png" />
    <Content Include="Content\icons\mstile-310x150.png" />
    <Content Include="Content\icons\mstile-310x310.png" />
    <Content Include="Content\icons\mstile-70x70.png" />
    <Content Include="Content\Site.css">
      <DependentUpon>Site.less</DependentUpon>
    </Content>
    <Content Include="Content\Site.min.css">
      <DependentUpon>Site.css</DependentUpon>
    </Content>
    <Content Include="Error\Forbidden.html" />
    <Content Include="Error\GatewayTimeout.html" />
    <Content Include="Error\ServiceUnavailable.html" />
    <Content Include="Global.asax" />
    <Content Include="Content\Site.css.map">
      <DependentUpon>Site.css</DependentUpon>
    </Content>
    <Content Include="Content\bootstrap\site.less" />
    <Content Include="Content\bootstrap\site.css.map">
      <DependentUpon>site.css</DependentUpon>
    </Content>
    <None Include="Content\Site.less" />
    <Content Include="humans.txt" />
    <Content Include="Scripts\advanceSearch.js" />
    <Content Include="Scripts\applicationstatus.js" />
    <Content Include="Scripts\applicationstatusEdit.js" />
    <Content Include="Scripts\applicationstatusIndex.js" />
    <Content Include="Scripts\bootstrap-datetimepicker.min.js" />
    <Content Include="Scripts\cbpHorizontalMenu.min.js" />
    <Content Include="Scripts\cms\home\fileupload.js" />
    <Content Include="Scripts\cms\home\highlights.js" />
    <Content Include="Scripts\cms\home\sightings.js" />
    <Content Include="Scripts\cms\research\applicationstatusIndex.js" />
    <Content Include="Scripts\cms\systemparams.js" />
    <Content Include="Scripts\cms\user\userEdit.js" />
    <Content Include="Scripts\cms\user\userGroupIndex.js" />
    <Content Include="Scripts\cms\user\userGroupItem.js" />
    <Content Include="Scripts\customjavascriptcode.js" />
    <Content Include="Scripts\Fallback\styles.js" />
    <Content Include="Scripts\Fallback\scripts.js" />
    <Content Include="Content\fonts\fontawesome\fontawesome-webfont.woff" />
    <Content Include="Content\fonts\fontawesome\fontawesome-webfont.ttf" />
    <Content Include="Content\fonts\fontawesome\fontawesome-webfont.eot" />
    <Content Include="Content\fonts\fontawesome\FontAwesome.otf" />
    <Content Include="Content\fonts\bootstrap\glyphicons-halflings-regular.woff" />
    <Content Include="Content\fonts\bootstrap\glyphicons-halflings-regular.ttf" />
    <Content Include="Content\fonts\bootstrap\glyphicons-halflings-regular.eot" />
    <Content Include="Content\fontawesome\Site.less" />
    <Content Include="Content\fontawesome\Site.css.map">
      <DependentUpon>Site.css</DependentUpon>
    </Content>
    <Content Include="Scripts\global-Js.js" />
    <Content Include="Scripts\homepage.js" />
    <Content Include="Scripts\ie-emulation-modes-warning.js" />
    <Content Include="Scripts\ie10-viewport-bug-workaround.js" />
    <Content Include="Scripts\fileinput.js" />
    <Content Include="Scripts\highlight.js" />
    <Content Include="Scripts\jquery-3.5.1.min.map" />
    <Content Include="Scripts\jquery-3.5.1.slim.min.map" />
    <Content Include="Scripts\jquery-ui.js" />
    <Content Include="Scripts\jquery.hoverCarousel.js" />
    <Content Include="Scripts\jquery.mCustomScrollbar.concat.min.js" />
    <Content Include="Scripts\jquery.mThumbnailScroller.js" />
    <Content Include="Scripts\jquery.unobtrusive-ajax.js" />
    <Content Include="Scripts\jquery.unobtrusive-ajax.min.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Content\leaflet\esri-leaflet-debug.js" />
    <Content Include="Content\leaflet\leaflet.markercluster-src.js" />
    <Content Include="Scripts\main.js" />
    <Content Include="Scripts\map.js" />
    <Content Include="Scripts\menuDropdown.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\moment-with-locales.js" />
    <Content Include="Scripts\npm.js" />
    <Content Include="Scripts\permit.js" />
    <Content Include="Scripts\permitonline.js" />
    <Content Include="Scripts\permitterms.js" />
    <Content Include="Scripts\profile.js" />
    <Content Include="Scripts\sighting.js" />
    <Content Include="Scripts\signUp.js" />
    <Content Include="Scripts\site.js" />
    <Content Include="Content\leaflet\leaflet-src.js" />
    <Content Include="Content\leaflet\proj4-src.js" />
    <Content Include="Content\leaflet\proj4leaflet.js" />
    <Content Include="Scripts\togeojson.js" />
    <Content Include="Scripts\tokml.js" />
    <Content Include="Scripts\validation-add-on.js" />
    <Content Include="Scripts\wellknown.js" />
    <Content Include="Scripts\_references.js" />
    <Content Include="Views\Error\BadRequest.cshtml" />
    <Content Include="Views\Error\InternalServerError.cshtml" />
    <Content Include="Views\Search\AdvanceSearchResult.cshtml" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Home\Contact.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Error\NotFound.cshtml" />
    <Content Include="Views\Error\Unauthorized.cshtml" />
    <Content Include="Views\Error\Forbidden.cshtml" />
    <Content Include="Views\Error\MethodNotAllowed.cshtml" />
    <Content Include="Views\Shared\_PartialHeader.cshtml" />
    <Content Include="Views\Shared\_PartialFooter.cshtml" />
    <Content Include="Views\Sightings\Index.cshtml" />
    <Content Include="Views\Sightings\MySightings.cshtml" />
    <Content Include="Views\Sightings\SubmitSightings.cshtml" />
    <Content Include="Views\Shared\_Notifications.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\Bootstrap3Pagination.cshtml" />
    <Content Include="Views\Shared\_LayoutError.cshtml" />
    <Content Include="Views\Shared\_LoginPartial.cshtml" />
    <Content Include="Views\Shared\_ValidationErrorNotifications.cshtml" />
    <Content Include="Views\Account\Login.cshtml" />
    <Content Include="Views\Sightings\SubmitSightingsBulk.cshtml" />
    <Content Include="Views\Sightings\SubmitSightingsIndividual.cshtml" />
    <Content Include="Views\Map\Index.cshtml" />
    <Content Include="Views\Shared\_PartialMenu.cshtml" />
    <Content Include="Views\Sightings\SightingDetails.cshtml" />
    <Content Include="Views\Home\_PartialSelectedSightings.cshtml" />
    <Content Include="Views\Home\_PartialHighlights.cshtml" />
    <Content Include="Views\Home\_PartialHighlightCarouselItem.cshtml" />
    <Content Include="Views\Account\MyProfile.cshtml" />
    <Content Include="Views\Account\SignUp.cshtml" />
    <Content Include="Views\Shared\_PartialMapMenu.cshtml" />
    <Content Include="Views\Sightings\_ListSightingVerification.cshtml" />
    <Content Include="Views\Sightings\_ListSightingComment.cshtml" />
    <Content Include="Areas\Admin\Views\Shared\_PartialAdminBreadcrumbs.cshtml" />
    <Content Include="Views\Shared\_PartialPagination.cshtml" />
    <Content Include="Views\Sightings\_SightingDetailFollow.cshtml" />
    <Content Include="Views\Sightings\_SightingDetailLike.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\Date.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\DateYear.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\DateMonth.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\DateDay.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\DateMonthShort.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\DateMonthFull.cshtml" />
    <Content Include="Views\Shared\EditorTemplates\UserGroupTree.cshtml" />
    <Content Include="Views\Shared\_PartialBreadcrumbs.cshtml" />
    <Content Include="Views\Account\ResetPassword.cshtml" />
    <Content Include="Views\Account\ForgetPassword.cshtml" />
    <Content Include="Views\Projects\JoinProject.cshtml" />
    <Content Include="Views\Projects\ManageProject.cshtml" />
    <Content Include="Views\Projects\MyProjects.cshtml" />
    <Content Include="Views\Projects\ProjectMembers.cshtml" />
    <Content Include="Views\Projects\RequestProject.cshtml" />
    <Content Include="Views\Account\Reactivate.cshtml" />
    <Content Include="Views\Account\ChangePassword.cshtml" />
    <Content Include="Views\Account\Activate.cshtml" />
    <Content Include="Views\Account\ActivateExt.cshtml" />
    <Content Include="Views\Account\ReactivateLoggedIn.cshtml" />
    <Content Include="Views\Account\ResendActivation.cshtml" />
    <Content Include="Views\Sightings\_PartialDraftSubmit.cshtml" />
    <Content Include="Views\Sightings\_PartialAllSightings.cshtml" />
    <Content Include="Views\Sightings\_SightingInappropriate.cshtml" />
    <Content Include="Views\Shared\_PartialSubMenu.cshtml" />
    <Content Include="Views\Profile\Details.cshtml" />
    <Content Include="Views\Profile\_PartialFollower.cshtml" />
    <Content Include="Views\Permits\Terms.cshtml" />
    <Content Include="Views\Permits\OnlineApplication.cshtml" />
    <Content Include="Views\Sightings\SightingDetailsAnonymous.cshtml" />
    <Content Include="Views\Permits\FAQ.cshtml" />
    <Content Include="Views\Shared\_PartialSpinner.cshtml" />
    <Content Include="Views\Shared\_PartialNotification.cshtml" />
    <Content Include="Views\Shared\EditorTemplates\Date.cshtml" />
    <Content Include="Views\Shared\EditorTemplates\DateDay.cshtml" />
    <Content Include="Views\Shared\EditorTemplates\DateMonth.cshtml" />
    <Content Include="Views\Shared\EditorTemplates\DateMonthFull.cshtml" />
    <Content Include="Views\Shared\EditorTemplates\DateMonthShort.cshtml" />
    <Content Include="Views\Shared\EditorTemplates\DateYear.cshtml" />
    <Content Include="Views\Permits\Contact.cshtml" />
    <Content Include="Views\Help\Terms.cshtml" />
    <Content Include="Views\Discussions\DiscussionForum.cshtml" />
    <Content Include="Views\Discussions\_PartialUserName.cshtml" />
    <Content Include="Views\Discussions\_PartialUserProfilePicture.cshtml" />
    <Content Include="Views\Discussions\_PartialUnsubscribe.cshtml" />
    <Content Include="Views\AboutUs\AboutUs.cshtml" />
    <Content Include="Views\AboutUs\Partners.cshtml" />
    <Content Include="Views\AboutUs\RelatedLinks.cshtml" />
    <Content Include="Views\Help\FAQ.cshtml" />
    <Content Include="Views\Home\Sitemap.cshtml" />
    <Content Include="Views\Search\AdvanceSearch.cshtml" />
    <Content Include="Views\Permits\EditorTemplates\FieldSurveyTeamCreateViewModel.cshtml" />
    <Content Include="Views\Permits\_PartialFieldSurveyTeamViewModel.cshtml" />
    <Content Include="Views\Permits\OnlineApplicationDraft.cshtml" />
    <Content Include="Views\Permits\_PartialFieldSurveyTeamEditViewModel.cshtml" />
    <Content Include="Views\Permits\EditorTemplates\FieldSurveyTeamEditViewModel.cshtml" />
    <Content Include="Views\Shared\_PartialSubBreadcrumbs.cshtml" />
    <Content Include="Views\ApplicationStatus\Index.cshtml" />
    <Content Include="Views\ApplicationStatus\_PartialPagingApplicationStatuses.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\DateFullMonth.cshtml" />
    <Content Include="Views\ApplicationStatus\Details.cshtml" />
    <Content Include="Views\ApplicationStatus\DisplayTemplates\FieldSurveyTeamViewModel.cshtml" />
    <Content Include="Views\ApplicationStatus\Edit.cshtml" />
    <Content Include="Views\ApplicationStatus\EditorTemplates\FieldSurveyTeamEditViewModel.cshtml" />
    <Content Include="Views\Shared\_PartialUploadReport.cshtml" />
    <Content Include="Views\ApplicationStatus\DisplayTemplates\DetailsReportItemViewModel.cshtml" />
    <Content Include="Views\Sightings\_PartialNoNotification.cshtml" />
    <Content Include="Views\Passes\Index.cshtml" />
    <Content Include="Views\Passes\DisplayTemplates\PermitPassViewModel.cshtml" />
    <Content Include="Views\Letters\Index.cshtml" />
    <Content Include="Views\FileDownload\FileDownloadError.cshtml" />
    <Content Include="Views\Sightings\_PartialDetailSubmit.cshtml" />
    <Content Include="Views\Sightings\ErrorPage.cshtml" />
    <Content Include="Views\Resources\MyResourceList.cshtml" />
    <Content Include="Views\Resources\ResourceList.cshtml" />
    <Content Include="Views\Resources\ViewMetadata.cshtml" />
    <Content Include="Views\Projects\ProjectDetail.cshtml" />
    <Content Include="Views\Shared\_PartialAddSiteRequest.cshtml" />
    <Content Include="Views\Projects\InviteMembers.cshtml" />
    <Content Include="Views\Projects\_PartialInviteMembers.cshtml" />
    <Content Include="Views\Resources\_PartialGetUploaderName.cshtml" />
    <Content Include="Views\Resources\ApproveDownloadRequest.cshtml" />
    <Content Include="Views\Surveys\JoinSurvey.cshtml" />
    <Content Include="Views\Surveys\ManageSurvey.cshtml" />
    <Content Include="Views\Surveys\MySurveys.cshtml" />
    <Content Include="Views\Surveys\RequestSurvey.cshtml" />
    <Content Include="Views\Surveys\SubmitSurvey.cshtml" />
    <Content Include="Views\Surveys\SurveyDetail.cshtml" />
    <Content Include="Views\Surveys\SurveyDetailViewOnly.cshtml" />
    <Content Include="Views\Surveys\_PartialSurveyQuestionCreateViewBSModel.cshtml" />
    <Content Include="Views\Surveys\EditorTemplates\SurveyQuestionCreateViewBSModel.cshtml" />
    <Content Include="Views\Surveys\EditorTemplates\SurveyQuestionCreateViewASModel.cshtml" />
    <Content Include="Views\Surveys\_PartialSurveyQuestionCreateViewASModel.cshtml" />
    <Content Include="Views\ApplicationStatus\_PartialFieldSurveyTeamEditViewModel.cshtml" />
    <Content Include="Views\ApplicationStatus\List.cshtml" />
    <Content Include="Views\Sightings\_PartialDeleteSighting.cshtml" />
    <Content Include="Views\Surveys\_SpeciesPartial.cshtml" />
    <Content Include="Views\Surveys\MySubmission.cshtml" />
    <Content Include="Views\Surveys\MySurveyOtherSubmission.cshtml" />
    <Content Include="Views\Surveys\SurveyMembers.cshtml" />
    <Content Include="Views\Surveys\_PartialCheckboxQuestionDetailViewModel.cshtml" />
    <Content Include="Views\Surveys\_PartialRadioQuestionDetailViewModel.cshtml" />
    <Content Include="Views\Surveys\EditorTemplates\CheckboxQuestionDetailViewModel.cshtml" />
    <Content Include="Views\Surveys\EditorTemplates\RadioQuestionDetailViewModel.cshtml" />
    <Content Include="Views\Surveys\_SpeciesListPartial.cshtml" />
    <Content Include="Views\ApplicationStatus\_PartialSiteStatus.cshtml" />
    <Content Include="Views\Surveys\_PartialPagingSurvey.cshtml" />
    <Content Include="Views\Surveys\_PartialSpeciesDetail.cshtml" />
    <Content Include="Views\Surveys\InviteMembers.cshtml" />
    <Content Include="Views\Surveys\_PartialInviteMembers.cshtml" />
    <Content Include="Views\Surveys\_PartialPagingJoinSurvey.cshtml" />
    <Content Include="Views\Account\EnterOTP.cshtml" />
    <Content Include="Views\Surveys\DisplayTemplates\SurveySpeciesViewModel.cshtml" />
    <Content Include="Views\Surveys\DisplayTemplates\SpeciesByCategoryViewModel.cshtml" />
    <Content Include="Views\Feedbacks\Feedback.cshtml" />
    <Content Include="Views\Error\RequestValidationError.cshtml" />
    <Content Include="Views\ApplicationStatus\AssignMainApplicant.cshtml" />
    <Content Include="Views\Help\Guides.cshtml" />
    <Content Include="Views\Help\_SpeciesListPartial.cshtml" />
    <Content Include="Views\Account\adfs_l.cshtml" />
    <Content Include="Views\Account\ActivateExt_ADFS.cshtml" />
    <Content Include="Views\Help\SpeciesDetails.cshtml" />
    <Content Include="Scripts\umd\popper.min.js.map" />
    <Content Include="Scripts\umd\popper.js.map" />
    <Content Include="Scripts\umd\popper.js.flow" />
    <Content Include="Scripts\umd\popper-utils.min.js.map" />
    <Content Include="Scripts\umd\popper-utils.js.map" />
    <Content Include="Scripts\README.md" />
    <Content Include="Scripts\popper.min.js.map" />
    <Content Include="Scripts\popper.js.map" />
    <Content Include="Scripts\popper-utils.min.js.map" />
    <Content Include="Scripts\popper-utils.js.map" />
    <Content Include="Views\TodoList\Index.cshtml" />
    <Content Include="Views\TodoList\_PartialPagingToDoList.cshtml" />
    <Content Include="Views\Permits\eAckSign.cshtml" />
    <Content Include="Views\Shared\_PermitMemberSignAckListPartial.cshtml" />
    <Content Include="Views\Permits\DetailsPermit_AckSign.cshtml" />
    <Content Include="Views\Permits\DetailsPermit_AckSign_NewSubmission.cshtml" />
    <Content Include="Views\Resources\ErrorPage.cshtml" />
    <Content Include="Scripts\tinymce\license.md" />
    <Content Include="Scripts\tinymce\langs\README.md" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Areas\Admin\Models\" />
    <Folder Include="Content\Resource\" />
    <Folder Include="Content\UserProfile\" />
    <Folder Include="Views\Categories\" />
    <Folder Include="Views\CMS\" />
    <Folder Include="Views\Reports\" />
    <Folder Include="Views\Submitted\" />
    <Folder Include="Views\Versioning\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BIOME.Constants\BIOME.Constants.csproj">
      <Project>{9fc0d184-1b89-4bd8-8c88-1ac2ee80b379}</Project>
      <Name>BIOME.Constants</Name>
    </ProjectReference>
    <ProjectReference Include="..\BIOME.Enumerations\BIOME.Enumerations.csproj">
      <Project>{b5bd07d6-1b98-4c4a-98bd-1226cdabc53f}</Project>
      <Name>BIOME.Enumerations</Name>
    </ProjectReference>
    <ProjectReference Include="..\BIOME.Errors\BIOME.Errors.csproj">
      <Project>{6e16aea0-1366-470f-ac8a-bf2681b75602}</Project>
      <Name>BIOME.Errors</Name>
    </ProjectReference>
    <ProjectReference Include="..\BIOME.Models\BIOME.Models.csproj">
      <Project>{6308a8e5-6cf9-487e-b387-a00f6605fb13}</Project>
      <Name>BIOME.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\BIOME.Services\BIOME.Services.csproj">
      <Project>{916af00a-98c4-4b0c-a11f-1e35ac426b6e}</Project>
      <Name>BIOME.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\BIOME.ViewModels\BIOME.ViewModels.csproj">
      <Project>{a89eef9a-0a37-4823-ba88-aaa34182e845}</Project>
      <Name>BIOME.ViewModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\NetPhantomJS\NetPhantomJS.csproj">
      <Project>{f108052c-7677-4215-bdfc-65852b2814ca}</Project>
      <Name>NetPhantomJS</Name>
    </ProjectReference>
    <ProjectReference Include="..\Utilities\Utilities.csproj">
      <Project>{b0f4deb8-b30e-4fd3-b4ed-6f75632d7c8d}</Project>
      <Name>Utilities</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Versioning\ExceptionStrings.resx" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <TypeScriptCompile Include="Scripts\index.d.ts" />
    <TypeScriptCompile Include="Scripts\tinymce\tinymce.d.ts" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="tsconfig.json" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Staging|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;STAGING;INTRANET</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug Intranet|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;DEBUG;INTRANET</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <UseVSHostingProcess>true</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Staging AWS Internet|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;STAGING;AWS;INTERNET</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Staging AWS Intranet|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;STAGING;AWS;INTRANET</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release Intranet|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;INTRANET</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UAT Internet|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;INTERNET;UAT</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UAT Intranet|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;INTRANET;UAT</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>33266</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44301/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets" Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets'))" />
    <Error Condition="!Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets'))" />
    <Error Condition="!Exists('..\packages\TinyMCE.7.2.0\build\TinyMCE.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\TinyMCE.7.2.0\build\TinyMCE.targets'))" />
    <Error Condition="!Exists('..\packages\BuildWebCompiler2022.1.14.15\build\BuildWebCompiler2022.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\BuildWebCompiler2022.1.14.15\build\BuildWebCompiler2022.targets'))" />
    <Error Condition="!Exists('..\packages\Magick.NET-Q16-AnyCPU.14.6.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Magick.NET-Q16-AnyCPU.14.6.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets'))" />
  </Target>
  <Import Project="..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
  <Import Project="..\packages\TinyMCE.7.2.0\build\TinyMCE.targets" Condition="Exists('..\packages\TinyMCE.7.2.0\build\TinyMCE.targets')" />
  <Import Project="..\packages\BuildWebCompiler2022.1.14.15\build\BuildWebCompiler2022.targets" Condition="Exists('..\packages\BuildWebCompiler2022.1.14.15\build\BuildWebCompiler2022.targets')" />
  <Import Project="..\packages\Magick.NET-Q16-AnyCPU.14.6.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets" Condition="Exists('..\packages\Magick.NET-Q16-AnyCPU.14.6.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>