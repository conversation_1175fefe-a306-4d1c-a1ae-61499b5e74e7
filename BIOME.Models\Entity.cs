﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace BIOME.Models
{
    public abstract class BaseEntity
    {
        
    }

    public abstract class Entity<TPrimaryKey> : BaseEntity, IEntity<TPrimaryKey>, IEquatable<Entity<TPrimaryKey>>
    {
        public virtual TPrimaryKey Id { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public DateTimeOffset UpdatedAt { get; set; }

        public Entity()
        {
            CreatedAt = DateTimeOffset.Now;
            UpdatedAt = DateTimeOffset.Now;
        }

        public bool Equals(Entity<TPrimaryKey> other)
        {
            if (other == null)
                return false;
            return Id.Equals(other.Id);
        }
    }
}