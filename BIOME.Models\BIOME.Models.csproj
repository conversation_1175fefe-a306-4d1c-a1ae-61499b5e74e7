﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6308A8E5-6CF9-487E-B387-A00F6605FB13}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>BIOME.Models</RootNamespace>
    <AssemblyName>BIOME.Models</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Staging|AnyCPU'">
    <OutputPath>bin\Staging\</OutputPath>
    <DefineConstants>TRACE;STAGING</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Autofac, Version=6.3.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.6.3.0\lib\netstandard2.0\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Elasticsearch.Net, Version=7.0.0.0, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\Elasticsearch.Net.7.17.5\lib\net461\Elasticsearch.Net.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.DynamicFilters, Version=1.4.10.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.DynamicFilters.1.4.10.1\lib\net40\EntityFramework.DynamicFilters.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="GeoJSON.Net, Version=0.1.47.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoJSON.Net.0.1.47\lib\portable-net40+sl5+wp80+win8+wpa81\GeoJSON.Net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.1\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.EntityFramework.2.2.1\lib\net45\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.1.1.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Nest, Version=7.0.0.0, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\NEST.7.17.5\lib\net461\Nest.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Entity" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=5.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.5.0.0\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.5.0.0\lib\net45\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.2\lib\netstandard2.0\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AuditLogAction.cs" />
    <Compile Include="AuditTrailLog.cs" />
    <Compile Include="AuditTrailLogging.cs" />
    <Compile Include="BadgeDetail.cs" />
    <Compile Include="ConfEnvironment.cs" />
    <Compile Include="ConfHabitat.cs" />
    <Compile Include="ConfListOther.cs" />
    <Compile Include="ConfThreatenedSpeciesName.cs" />
    <Compile Include="DeveloperInfo.cs" />
    <Compile Include="EmailOTP.cs" />
    <Compile Include="EmailTemplate.cs" />
    <Compile Include="EmailTemplateField.cs" />
    <Compile Include="EmailTemplates\DiscussionAnaswerNotificationEmailInfo.cs" />
    <Compile Include="EmailTemplates\DiscussionNotificationEmailInfo.cs" />
    <Compile Include="EmailTemplates\SendFeedbackEmailInfo.cs" />
    <Compile Include="Entity.cs" />
    <Compile Include="Feedback.cs" />
    <Compile Include="FileStoreTmp.cs" />
    <Compile Include="GISLocation.cs" />
    <Compile Include="GuideDetail.cs" />
    <Compile Include="Inbox.cs" />
    <Compile Include="LayerGroup.cs" />
    <Compile Include="MaintenanceNotice.cs" />
    <Compile Include="MapLayerGroup.cs" />
    <Compile Include="MaskedSite.cs" />
    <Compile Include="ArcgisMavenLayer.cs" />
    <Compile Include="Newsfeed.cs" />
    <Compile Include="ProjectMemberInvited.cs" />
    <Compile Include="ToDoList.cs" />
    <Compile Include="SurveyMemberInvited.cs" />
    <Compile Include="SurveyMembers.cs" />
    <Compile Include="SurveyQuestionsAnswer.cs" />
    <Compile Include="SurveySpeciesAnswer.cs" />
    <Compile Include="SurveyLocation.cs" />
    <Compile Include="SurveyMasterSpecies.cs" />
    <Compile Include="SurveyQuestionDetail.cs" />
    <Compile Include="SurveyQuestions.cs" />
    <Compile Include="SurveyQuestionType.cs" />
    <Compile Include="RecordedDates.cs" />
    <Compile Include="ResearchApplicationDraft.cs" />
    <Compile Include="ResearchFieldSurveyTeamMemberDraft.cs" />
    <Compile Include="ResearchFieldSurveyTeamMember.cs" />
    <Compile Include="ResearchHeaderFooterTemplate.cs" />
    <Compile Include="ResearchInstitutional.cs" />
    <Compile Include="ResearchLetterTemplate.cs" />
    <Compile Include="ResearchPermitStatus.cs" />
    <Compile Include="ResearchPermitStudyLocation.cs" />
    <Compile Include="ResearchSiteVisit.cs" />
    <Compile Include="ResearchStudyLocation.cs" />
    <Compile Include="ResearchTermsTemplate.cs" />
    <Compile Include="ResourceDocument.cs" />
    <Compile Include="ResourceDocumentType.cs" />
    <Compile Include="ResourceFilePermission.cs" />
    <Compile Include="ResourceMetaData.cs" />
    <Compile Include="ResourceMetaDataAuthor.cs" />
    <Compile Include="ResourceMetaLocation.cs" />
    <Compile Include="ResourceStructureTemplate.cs" />
    <Compile Include="ResearchType.cs" />
    <Compile Include="ResourceStructureTemplateColumn.cs" />
    <Compile Include="SurveyCategory.cs" />
    <Compile Include="Survey.cs" />
    <Compile Include="SurveySpecies.cs" />
    <Compile Include="SurveySubmission.cs" />
    <Compile Include="ConcurrentLoginAlert.cs" />
    <Compile Include="MobileUserSession.cs" />
    <Compile Include="Token.cs" />
    <Compile Include="ApplicationUser.cs" />
    <Compile Include="BackgroundJobTask.cs" />
    <Compile Include="BIOMERole.cs" />
    <Compile Include="BIOMEUserClaim.cs" />
    <Compile Include="BIOMEUserLogin.cs" />
    <Compile Include="BIOMEUserRole.cs" />
    <Compile Include="BIOMEUserStore.cs" />
    <Compile Include="ConfProjectCategory.cs" />
    <Compile Include="ConfSightingCategory.cs" />
    <Compile Include="EmailTemplates\EmailInfoBase.cs" />
    <Compile Include="EmailTemplates\ReactivateAccountEmailinfo.cs" />
    <Compile Include="EmailTemplates\ResetPasswordEmailinfo.cs" />
    <Compile Include="Group.cs" />
    <Compile Include="Highlight.cs" />
    <Compile Include="HomepageAppInfo.cs" />
    <Compile Include="HomepageContact.cs" />
    <Compile Include="PageFooter.cs" />
    <Compile Include="IEntity.cs" />
    <Compile Include="ISoftDelete.cs" />
    <Compile Include="PageHeader.cs" />
    <Compile Include="PreviousPassword.cs" />
    <Compile Include="ProjectDetail.cs" />
    <Compile Include="ProjectMember.cs" />
    <Compile Include="ProjectSighting.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ResearchApplication.cs" />
    <Compile Include="ResearchDiscussionUnsubscribe.cs" />
    <Compile Include="ResearchDiscussionAnswer.cs" />
    <Compile Include="ResearchDiscussionForum.cs" />
    <Compile Include="ResearchInbox.cs" />
    <Compile Include="ResearchPermitApplication.cs" />
    <Compile Include="ResearchPermitLetter.cs" />
    <Compile Include="ResearchPermitPass.cs" />
    <Compile Include="ResearchReport.cs" />
    <Compile Include="SightingComment.cs" />
    <Compile Include="SightingDetail.cs" />
    <Compile Include="SightingFile.cs" />
    <Compile Include="SightingInappropriate.cs" />
    <Compile Include="SightingLike.cs" />
    <Compile Include="SightingTaxonomy.cs" />
    <Compile Include="SightingTaxonomyClassification.cs" />
    <Compile Include="SightingVerificationName.cs" />
    <Compile Include="SightingVerificationVote.cs" />
    <Compile Include="SystemParameters.cs" />
    <Compile Include="UserAuthSessionStore.cs" />
    <Compile Include="UserInfoBadge.cs" />
    <Compile Include="UserInfoScore.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BIOME.Constants\BIOME.Constants.csproj">
      <Project>{9fc0d184-1b89-4bd8-8c88-1ac2ee80b379}</Project>
      <Name>BIOME.Constants</Name>
    </ProjectReference>
    <ProjectReference Include="..\BIOME.Enumerations\BIOME.Enumerations.csproj">
      <Project>{b5bd07d6-1b98-4c4a-98bd-1226cdabc53f}</Project>
      <Name>BIOME.Enumerations</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>