﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Constants
{
    public class Search
    {
        public class Elasticsearch
        {
            //public const string ServerURL = "http://localhost:9200"; //this should move to configation
            public const string SightingIndexName = "sightingbiome";
            //public const string SightingIndexOldNameAlias = "sightingbiomesearch-old";
            //public const string ProjectIndexNameAlias = "projectbiomesearch";
            //public const string ProjectIndexOldNameAlias = "projectbiomesearch-old";
            public const string ResourceIndexName = "resourcebiomesearch";
            //public const string ResourceIndexOldNameAlias = "resourcebiomesearch-old";
            public const string ResearchApplicationIndexName = "researchapplicationbiome";
            //public const string ApplicationIndexOldNameAlias = "applicationbiomesearch-old";
            public const string MapResourceIndexName = "mapbiome";


            public const int DefaultPageSize = 10;

            public const int DefaultSearchCount = 100;

            public const int DefaultDistanceKM = 5;

        }

        public class Highlight
        {
            public const string preTag = "<span class='keyword'>";
            public const string postTag = "</span>";
        }

        public class SearchFields
        {
            public class PublicSighting {

                public const string description = "description";
                public const string categoryName = "categoryName";
                public const string commonName = "commonName";
                public const string scientificName = "scientificName";
                public const string taxonomyScientificName = "sightingTaxonomy.sightingTaxonomyClassifications.scientificName";
                public const string comment = "sightingComments.commentString";
                //public const string owner = "owner.fullName";
                public const string owner = "ownerName";
                public const string updateAt = "updatedAt";
                public const string genbankCommonName = "sightingTaxonomy.genbankCommonName";
                public const string sightingLocation = "sightingLocation";
                public const string isInappropriate = "isInappropriate";

            }

            public class Project {

                public const string description = "projectSightings.project.description";
                public const string title = "projectSightings.project.title";
                //public const string member = "projectSightings.project.projectMembers.member.fullName";
                //public const string member = "projectSightings.project.projectMembers.fullName";
                public const string member = "projectSightings.project.projectMembers.personName";
                public const string projectStatus = "projectSightings.project.projectStatus";
                public const string updatedAt = "projectSightings.project.updatedAt";
                public const string comment = "sightingComments.commentString";
                public const string scientificName = "scientificName";
                public const string genbankCommonName = "sightingTaxonomy.genbankCommonName";
                public const string taxonomyScientificName = "sightingTaxonomy.sightingTaxonomyClassifications.scientificName";
                public const string commonName = "commonName";

            }

            public class Resource
            {
                public const string fileName = "fileName";
                public const string title = "atResourceMetaData.title";
                public const string author = "atResourceMetaData.resourceMetaDataAuthors.name";
                public const string organization = "atResourceMetaData.organization";
                public const string description = "atResourceMetaData.description";
                public const string file = "fileAttach.content";

                public const string updatedAt = "createdAt";
                //public const string locationField = "atResourceMetaData.resourceMetaLocations.metaLocation";
                public const string locationField = "multipileLocationDrawnGeo";
                public const string filterGroup = "atResourceMetaData.groups.id";
            }

            public class PermitApplication
            {
                public const string permitNo = "permitApplications.permitNumber";
                public const string institution = "InstitutionName";
                public const string teamMember = "fieldSurveyTeamMembers.name";
                public const string researchTitle = "title";
                public const string purpose = "purpose";

                public const string habitat = "habitatName";
                public const string typeOfResearch = "researchTypeName";

                public const string updatedAt = "updatedAt";
                public const string locationField = "studyLocationsDrawnGeo";


            }
        }
    }
}
