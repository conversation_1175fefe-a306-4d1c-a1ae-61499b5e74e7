﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace BIOME.Models
{
    public class SightingDetail : Entity<long>, IDescribableEntity 
    {
        public string CommonName { get; set; }
        public string ScientificName { get; set; }
        public int CategoryID { get; set; }
        public string CategoryName { get; set; }
        public string Description { get; set; }
        public string Habitat { get; set; }
        public string Behaviour { get; set; }
        public string PrevCommonName { get; set; }
        public string PrevScientificName { get; set; }

        public string Location { get; set; }
        public string SightingImage { get; set; }
        public string SightingImagePath { get; set; }
        public float Latitude { get; set; }
        public float Longitude { get; set; }

       
        //files currently not used, because one sighting only has one image.
        public virtual IList<SightingFile> SightingFiles { get; set; }
        public virtual IList<SightingComment> SightingComments { get; set; }
        public virtual IList<SightingLike> SightingLikes { get; set; }
        public virtual IList<SightingInappropriate> SightingInappropriates { get; set; }
        public virtual IList<SightingVerificationName> SightingVerificationNames { get; set; }
        public virtual IList<SightingVerificationVote> SightingVerificationVotes { get; set; }

        public virtual IList<SightingTaxonomy> SightingTaxonomy { get; set; }

        public int SubmitFrom { get; set; }
        public int LikeCount { get; set; }
        public int CommentCount { get; set; }
        public int NumberSpotted { get; set; }
        //IsFeatured IsInappropriate IsSensitive need add index
        public bool IsPrivate { get; set; } //For project
        public bool IsHomepageSelected { get; set; }
        public bool IsFeatured { get; set; }
        public bool IsInappropriate { get; set; }
        //Species Threatened Sensitive
        public bool IsSensitive { get; set; }
        //Admin Sensitive
        public bool IsAdminSensitive { get; set; }
        public long FeaturedBy { get; set; }
        public long InappropriateBy { get; set; }
        public long SensitiveBy { get; set; }
        public long AdminSensitiveBy { get; set; }
        //public string Status { get; set; }
        public int StatusRank { get; set; }
        public DateTimeOffset DateSpotted { get; set; }
        public DateTimeOffset TimeLiked { get; set; }
        public DateTimeOffset TimeCommented { get; set; }
        public DateTimeOffset TimeVerified { get; set; }

        public long ApprovedById { get; set; }
        

        public long OwnerId { get; set; }
        [ForeignKey("OwnerId")]
        public virtual ApplicationUser Owner { get; set; }

        public bool IsIndex { get; set; } //ElasticSearch
        public DateTimeOffset? IndexedDateTime { get; set; }

        [NotMapped]
        public int FlagCount { get; set; }

        [NotMapped]
        public string StatusString
        {
            get
            {
                if (StatusRank == (int)BIOME.Enumerations.Sighting.StatusRank.Draft) return "Draft";
                else if (StatusRank == (int)BIOME.Enumerations.Sighting.StatusRank.NeedVerification) return "Need Verification";
                else if (StatusRank == (int)BIOME.Enumerations.Sighting.StatusRank.Verified) return "Verified";
                return "";
            }
        }

        [NotMapped]
        public string APIStatusString
        {
            get
            {
                if (StatusRank == (int)BIOME.Enumerations.Sighting.StatusRank.Draft) return "Draft";
                else if (StatusRank == (int)BIOME.Enumerations.Sighting.StatusRank.NeedVerification) return "Pending";
                else if (StatusRank == (int)BIOME.Enumerations.Sighting.StatusRank.Verified) return "Approved";
                return "";
            }
        }

        [NotMapped]
        public Nest.GeoLocation SightingLocation
        {
             get { return new Nest.GeoLocation(Latitude, Longitude); }
            set { }

            //get { return Latitude.ToString() + "," + Longitude.ToString(); }
            //set { }
        }
        //[NotMapped]
        public virtual IList<ProjectSighting> ProjectSightings { get; set; }

        public string Describe()
        {
            return "{ CommonName : \"" + CommonName + "\", ScientificName : \"" + ScientificName + "\", CategoryName : \"" + CategoryName + "\", Description : \"" + Description
                + "\", Habitat : \"" + Habitat + "\", Behaviour : \"" + Behaviour + "\", PrevCommonName : \"" + PrevCommonName + "\", PrevScientificName : \"" + PrevScientificName
                + "\", Location : \"" + Location + "\", SightingImage : \"" + SightingImage + "\", Latitude : \"" + Latitude + "\", Longitude : \"" + Longitude + "}";
        }
        public SightingDetail()
        {
            LikeCount = 0;
            CommentCount = 0;
            IsHomepageSelected = false;
            IsFeatured = false;
            IsInappropriate = false;
            IsSensitive = false;
            FeaturedBy = 0;
            InappropriateBy = 0;
            SensitiveBy = 0;
            
            SightingFiles = new List<SightingFile>();
            SightingComments = new List<SightingComment>();
            SightingLikes = new List<SightingLike>();
            SightingInappropriates = new List<SightingInappropriate>();
            SightingVerificationNames = new List<SightingVerificationName>();
            SightingVerificationVotes = new List<SightingVerificationVote>();

            SightingTaxonomy = new List<SightingTaxonomy>();

            ProjectSightings = new List<ProjectSighting>();
        }

        public string GetOriginalImage()
        {
            var imagename = "/Content/images/nophotoavailable.jpg";
            if (!string.IsNullOrEmpty(SightingImage))
            {
                imagename = SightingImagePath + SightingImage;
            }

            return imagename;
        }

        public string GetCroppedImage()
        {
            var imagename = "/Content/images/nophotoavailable.jpg";
            if (!string.IsNullOrEmpty(SightingImage))
            {
                imagename = SightingImagePath + SightingImage.Replace(".", "_") + "_crop.jpg";
            }

            return imagename;
        }

        public string GetThumbnailImage()
        {
            var imagename = "/Content/images/nophotoavailable.jpg";
            if (!string.IsNullOrEmpty(SightingImage))
            {
                var thumbImage = SightingImagePath + SightingImage.Replace(".", "_") + "_thumb.jpg";
                // Check if thumbnail exists, otherwise use cropped
                imagename = thumbImage;
            }

            return imagename;
        }

        public string GetOptimizedImage(string size = "crop")
        {
            var imagename = "/Content/images/nophotoavailable.jpg";
            if (!string.IsNullOrEmpty(SightingImage))
            {
                string suffix = size == "thumb" ? "_thumb" : "_crop";
                imagename = SightingImagePath + SightingImage.Replace(".", "_") + suffix + ".jpg";
            }

            return imagename;
        }

        public string GetWebPImage()
        {
            var imagename = "/Content/images/nophotoavailable.jpg";
            if (!string.IsNullOrEmpty(SightingImage))
            {
                var webpImage = SightingImagePath + SightingImage.Replace(".", "_") + "_crop.webp";
                // Fallback to JPG if WebP not available
                imagename = webpImage;
            }

            return imagename;
        }
    }

    public class SightingLocation
    {
        public float lat { get; set; }
        public float lon { get; set;}
    }
}
