﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class AuditTrailLogging : Entity<long>
    {
        public int ActionId { get; set; }
        public string ActionName { get; set; }
        public string Module { get; set; }
        public string Event { get; set; }
        public long UserId { get; set; }
        [MaxLength(320)]
        public string UserEmail { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        [MaxLength(66)]
        public string PersonName { get; set; }
        public string IP { get; set; }
        public string SiteType { get; set; }
    }
}
