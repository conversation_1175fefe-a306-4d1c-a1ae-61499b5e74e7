﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchHeaderFooterTemplate : Entity<long>, ISoftDelete,IDescribableEntity
    {
        public string HeaderFileName { get; set; }
        public string FooterFileName { get; set; }
        public bool IsDeleted { get; set; }
        public string Describe()
        {
            return "{ HeaderFileName : \"" + HeaderFileName + "\", FooterFileName : \"" + FooterFileName + "\", IsDeleted : \"" + IsDeleted   + "}";
        }
    }
}
