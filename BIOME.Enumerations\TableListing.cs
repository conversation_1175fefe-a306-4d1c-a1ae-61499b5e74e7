﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Enumerations
{
    public class TableListing
    {
        public static class Sorting
        {
            public enum SortingOrder
            {
                Asc,
                Desc
            }
        }

        public static class Search
        {
            public enum OperationType
            {
                [Description("Equal")]
                equal,
                [Description("Not Equal")]
                notequal,
                [Description("Like")]
                like
            }

            public enum ConditionType
            {
                [Description("AND")]
                and,
                [Description("OR")]
                or
            }
        }
    }
}
