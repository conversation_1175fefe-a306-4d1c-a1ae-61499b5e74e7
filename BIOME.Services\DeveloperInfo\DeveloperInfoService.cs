﻿using BIOME.Models;
//using OpenSSL.Crypto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Utilities.Helpers;

namespace BIOME.Services
{
    public class DeveloperInfoService : IDeveloperInfoService
    {

        #region Fields

        private readonly ApplicationDbContext dbcontext;
        private static readonly Random random = new Random();

        #endregion

        #region Constructors

        public DeveloperInfoService(ApplicationDbContext dbcontext)
        {
            this.dbcontext = dbcontext;
        }

        #endregion

        #region Public Methods

        public DeveloperInfo FindByDeveloperKeyAndIsActive(string developerKey)
        {
            return dbcontext.DevelopersInfos.FirstOrDefault(i => i.Developer<PERSON>ey == developerKey && i.IsActive);
        }

        public string ConstructDeveloperTokenHash(DeveloperInfo devInfo)
        {
            return ConstructDeveloperTokenHashInternal(devInfo.DeveloperSalt, devInfo.DeveloperKey);
        }

        public string ConstructDeveloperTokenHash(string data, string developerKey)
        {
            var devInfo = FindByDeveloperKeyAndIsActive(developerKey);
            if (devInfo == null)
            {
                return string.Empty;
            }

            return ConstructDeveloperTokenHashInternal(devInfo.DeveloperSalt, data);
        }

        public bool IsDeveloperTokenCorrect(string developerKey, string hashedTokenToCompare)
        {
            var devInfo = FindByDeveloperKeyAndIsActive(developerKey);
            if (devInfo == null)
            {
                return false;
            }

            var devTokenHash = ConstructDeveloperTokenHash(devInfo);
            if (string.IsNullOrEmpty(devTokenHash))
            {
                return false;
            }

            return devTokenHash == hashedTokenToCompare;
        }

        #endregion

        #region Private Methods

        /*Unused because OpenSSL.Crypto is removed as part of Remediation for dependencies vulnerabilities.
        private string ConstructDeveloperTokenHashInternal(string developerSalt, string developerKey)
        {
            string tokenHash = string.Empty;
            var digest = HMAC.Digest(MessageDigest.SHA256, ASCIIEncoding.Default.GetBytes(developerSalt), ASCIIEncoding.Default.GetBytes(developerKey));
            if (digest != null)
            {
                var hexDigest = Convert.ToBase64String(ASCIIEncoding.Default.GetBytes(ByteHelper.ConvertByteArrayToHexString(digest).ToLower())).Replace("\n", "");
                tokenHash = hexDigest;
            }

            return tokenHash;
        }*/

        private string ConstructDeveloperTokenHashInternal(string developerSalt, string developerKey)
        {
            string tokenHash = string.Empty;
            System.Security.Cryptography.HMACSHA256 hmac = new System.Security.Cryptography.HMACSHA256(ASCIIEncoding.Default.GetBytes(developerSalt));
            var digest = hmac.ComputeHash(ASCIIEncoding.Default.GetBytes(developerKey));
            if (digest != null)
            {
                var hexDigest = Convert.ToBase64String(ASCIIEncoding.Default.GetBytes(ByteHelper.ConvertByteArrayToHexString(digest).ToLower())).Replace("\n", "");
                tokenHash = hexDigest;
            }

            return tokenHash;
        }

        public string ConstructHashForLoginFormRandomRoute(string input)
        {
            string key = System.Configuration.ConfigurationManager.AppSettings["LoginFormRandomKey"];
            string tokenHash = string.Empty;
            if(string.IsNullOrEmpty(key))
            {
                key = "ac5939efgd33939bk9029kkef73839201923772fglkkk33ffefb99020292fbdkdk293933"; //Set default key
            }

            System.Security.Cryptography.HMACSHA256 hmac = new System.Security.Cryptography.HMACSHA256(ASCIIEncoding.Default.GetBytes(key));
            var digest = hmac.ComputeHash(ASCIIEncoding.Default.GetBytes(input));
            if (digest != null)
            {
                var hexDigest = Convert.ToBase64String(ASCIIEncoding.Default.GetBytes(ByteHelper.ConvertByteArrayToHexString(digest).ToLower())).Replace("\n", "");
                tokenHash = hexDigest;
            }

            return tokenHash;
        }

        public string GenerateRandomFormRoute()
        {
            int length = 8;
            char[] chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".ToCharArray();
            //Random random = new Random();
            char[] result = new char[length];
            for (int i = 0; i<length; i++)
            {
                result[i] = chars[random.Next(chars.Length)];
            }
            return new string (result);
        }

        #endregion
    }
}
