﻿using BIOME.Constants;
using BIOME.Models;
using BIOME.Services;
using Microsoft.AspNet.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static BIOME.Constants.Configuration.Server;

namespace BIOME.Services
{
    public class MigrationService : ServiceBase, IMigrationService
    {
        #region Fields

        private readonly ApplicationUserManager applicationUserManager;
        private readonly ApplicationRoleManager applicationRoleManager;
        private readonly ApplicationDbContext dbContext;

        #endregion

        #region Constructors

        public MigrationService(ApplicationUserManager applicationUserManager, ApplicationRoleManager applicationRoleManager, ApplicationDbContext dbContext)
        {
            this.applicationUserManager = applicationUserManager;
            this.applicationRoleManager = applicationRoleManager;
            this.dbContext = dbContext;
        }

        #endregion

        #region Public Methods

        public void AddSystemAdmin(string email, string password)
        {
            var systemAdmin = applicationUserManager.FindByName(email);
            IdentityResult createResult = IdentityResult.Success;
            if (systemAdmin == null)
            {
                systemAdmin = new ApplicationUser();
                //systemAdmin.FirstName = "System";
                //systemAdmin.LastName = "Admin";
                systemAdmin.PersonName = "System Admin";
                systemAdmin.UserName = email;
                systemAdmin.Email = email;
                systemAdmin.IsActive = true;
                systemAdmin.IsFirstLogin = true;
                systemAdmin.EmailConfirmed = true;
                createResult = applicationUserManager.Create(systemAdmin, password);
            }

            if (!createResult.Succeeded)
            {
                return;
            }

            if (systemAdmin == null)
            {
                systemAdmin = applicationUserManager.FindByName(email);
            }

            if (!applicationUserManager.IsInRole(systemAdmin.Id, UserRoles.SystemAdmin))
            {
                applicationUserManager.AddToRole(systemAdmin.Id, UserRoles.SystemAdmin);
            }
        }

        public void AddUsers(string email, string password, string role)
        {
            var user = applicationUserManager.FindByName(email);
            IdentityResult createResult = IdentityResult.Success;
            if (user == null)
            {
                user = new ApplicationUser();
                //user.FirstName = "NPark";
                //user.LastName = role.ToLower();
                user.PersonName = "NPark " + role.ToLower();
                user.UserName = email;
                user.Email = email;
                user.IsActive = true;
                user.IsFirstLogin = true;
                user.EmailConfirmed = true;
                createResult = applicationUserManager.Create(user, password);
            }

            if (!createResult.Succeeded)
            {
                return;
            }

            if (user == null)
            {
                user = applicationUserManager.FindByName(email);
            }

            if (!applicationUserManager.IsInRole(user.Id, role))
            {
                applicationUserManager.AddToRole(user.Id, role);
            }
        }

        public void AddUsersForPerformanceTest(string email, string password, string role, string personName)
        {
            
            var user = applicationUserManager.FindByName(email);
            IdentityResult createResult = IdentityResult.Success;
            if (user == null)
            {
                user = new ApplicationUser();
                user.PersonName = personName;
                user.UserName = email;
                user.Email = email;
                user.IsActive = true;
                user.IsFirstLogin = true;
                user.EmailConfirmed = true;
                user.HasAgreedPermitApplicationTnC = true;
                DynamicPasswordCipher.IV = Utilities.Helpers.StringHelper.RandomString(32);
                DynamicPasswordCipher.Key = Utilities.Helpers.StringHelper.RandomString(64);
                user.IV = DynamicPasswordCipher.IV;
                user.Key = DynamicPasswordCipher.Key;
                createResult = applicationUserManager.Create(user, password);
            }

            if (!createResult.Succeeded)
            {
                return;
            }

            if (user == null)
            {
                user = applicationUserManager.FindByName(email);
            }

            if (!applicationUserManager.IsInRole(user.Id, role))
            {
                applicationUserManager.AddToRole(user.Id, role);
            }
        }

        public void AddRole(string roleName)
        {
            IdentityResult createResult = IdentityResult.Success;
            if (!applicationRoleManager.RoleExists(roleName))
            {
                var role = new BIOMERole();
                role.Name = roleName;
                createResult = applicationRoleManager.Create(role);
            }
        }

        public bool AddBadge(BadgeDetail badgeDetail)
        {
            var badgeDetailsFromDB = dbContext.BadgeDetails.FirstOrDefault(b => b.Title == badgeDetail.Title);
            if (badgeDetailsFromDB != null)
            {
                return true;
            }

            dbContext.Database.ExecuteSqlCommand(String.Format("DBCC CHECKIDENT ('BadgeDetails', RESEED, {0})", badgeDetail.Id - 1));
            dbContext.BadgeDetails.Add(badgeDetail);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddGuide(GuideDetail guideDetail)
        {
            var guideDetailsFromDB = dbContext.GuideDetails.FirstOrDefault(b => b.Title == guideDetail.Title);
            if (guideDetailsFromDB != null)
            {
                return true;
            }

            dbContext.Database.ExecuteSqlCommand(String.Format("DBCC CHECKIDENT ('GuideDetails', RESEED, {0})", guideDetail.Id - 1));
            dbContext.GuideDetails.Add(guideDetail);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddSightingCategory(ConfSightingCategory confSightingCategory)
        {
            var confSightingCategoryFromDB = dbContext.ConfSightingCategories.FirstOrDefault(b => b.CategoryName == confSightingCategory.CategoryName);
            if (confSightingCategoryFromDB != null)
            {
                return true;
            }

            dbContext.Database.ExecuteSqlCommand(String.Format("DBCC CHECKIDENT ('ConfSightingCategories', RESEED, {0})", confSightingCategory.Id - 1));
            dbContext.ConfSightingCategories.Add(confSightingCategory);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddMainGroup(MainGroup mainGroup)
        {
            var mainGroupFromDB = dbContext.Groups.FirstOrDefault(g => g.Name == mainGroup.Name && g is MainGroup);
            if (mainGroupFromDB != null)
            {
                return true;
            }

            dbContext.Groups.Add(mainGroup);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddSubGroup(SubGroup subGroup, string mainGroupName)
        {
            var genericGroup = dbContext.Groups.FirstOrDefault(g => g.Name == mainGroupName && g is MainGroup);
            if (genericGroup == null)
            {
                return false;
            }

            var mainGroup = (MainGroup)genericGroup;
            
            var subGroupFromDB = dbContext.Groups.FirstOrDefault(sg => sg.Name == subGroup.Name && sg is SubGroup);
            if (subGroupFromDB != null)
            {
                return true;
            }

            subGroup.ParentGroup = mainGroup;
            subGroup.MainGroupId = mainGroup.Id;
            //dbContext.SubGroups.Add(subGroup);
            mainGroup.SubGroups.Add(subGroup);

            return dbContext.SaveChanges() > 0;
        }

        public IList<MapLayerGroup> GetResourceGroups(string groupName)
        {
            groupName = groupName.ToLower();
            return dbContext.LayerGroup.Where(lg => lg.GroupName.ToLower().StartsWith(groupName)).GroupBy(lg => lg.GroupName).Select(lg => lg.FirstOrDefault()).ToList();
        }

        public bool AddLayerGroup(MapLayerGroup layerGroup)
        {            
            var LayerGroupFromDB = dbContext.LayerGroup.FirstOrDefault(lg => lg.LayerName == layerGroup.LayerName);
            if (LayerGroupFromDB != null)
            {
                dbContext.LayerGroup.Remove(LayerGroupFromDB);
            }

            dbContext.LayerGroup.Add(layerGroup);
            return dbContext.SaveChanges() > 0;
        }

        public bool RemoveLayerGroup(string groupName)
        {
            if (groupName != null)
            {
                var layers = dbContext.LayerGroup.Where(lg => lg.GroupName == groupName);
                dbContext.LayerGroup.RemoveRange(layers);
                return dbContext.SaveChanges() > 0;
            }
            else
            {
                return dbContext.Database.ExecuteSqlCommand("delete from MapLayerGroups") > 0;
            }            
        }

        public bool AddSubSubGroup(SubSubGroup subSubGroup, string subGroupName)
        {
            var genericGroup = dbContext.Groups.FirstOrDefault(sg => sg.Name == subGroupName && sg is SubGroup);
            if (genericGroup == null)
            {
                return false;
            }

            var subGroup = (SubGroup)genericGroup;

            var subSubGroupFromDB = dbContext.Groups.FirstOrDefault(ssg => ssg.Name == subSubGroup.Name && ssg is SubSubGroup);
            if (subSubGroupFromDB != null)
            {
                return true;
            }

            subSubGroup.ParentGroup = subGroup;
            subSubGroup.SubGroupId = subGroup.Id;
            //dbContext.SubSubGroups.Add(subSubGroup);
            subGroup.SubSubGroups.Add(subSubGroup);

            return dbContext.SaveChanges() > 0;
        }

        public bool AddDeveloperInfo(DeveloperInfo devInfo)
        {
            var existingDevInfo = dbContext.DevelopersInfos.FirstOrDefault();
            if (existingDevInfo != null)
            {
                return true;
            }

            dbContext.DevelopersInfos.Add(devInfo);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddFirstSystemParameters(SystemParameters parameters)
        {
            var existingSystemParameter = dbContext.SystemParameters.FirstOrDefault();
            if (existingSystemParameter != null)
            {
                return true;
            }

            dbContext.SystemParameters.Add(parameters);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddFirstMaintenanceNotice(MaintenanceNotice parameters)
        {
            var existingMaintenanceNotice = dbContext.MaintenanceNotices.FirstOrDefault();
            if (existingMaintenanceNotice != null)
            {
                return true;
            }

            dbContext.MaintenanceNotices.Add(parameters);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddPageHeader(PageHeader header)
        {
            var existingPageHeader = dbContext.PageHeaders.FirstOrDefault();
            if (existingPageHeader != null)
            {
                return true;
            }

            dbContext.PageHeaders.Add(header);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddPageFooter(PageFooter footer)
        {
            var existingPageFooter = dbContext.PageFooters.FirstOrDefault();
            if (existingPageFooter != null)
            {
                return true;
            }

            dbContext.PageFooters.Add(footer);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddHomepageContact(HomepageContact contact)
        {
            var existingHomepageContacts = dbContext.HomepageContacts.FirstOrDefault();
            if (existingHomepageContacts != null)
            {
                return true;
            }

            dbContext.HomepageContacts.Add(contact);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddHomepageAppInfo(HomepageAppInfo appInfo)
        {
            var existingHomepageAppInfo = dbContext.HomepageAppInfos.FirstOrDefault();
            if (existingHomepageAppInfo != null)
            {
                return true;
            }

            dbContext.HomepageAppInfos.Add(appInfo);
            return dbContext.SaveChanges() > 0;
        }

        public bool ChangePassword(string email, string password, bool forced = false)
        {
            var user = applicationUserManager.FindByEmail(email);
            if (user == null)
            {
                return false;
            }

            var samePassword = applicationUserManager.CheckPassword(user, password);
            if (samePassword == true && !forced)
            {
                return false;
            }

            var removeResult = applicationUserManager.RemovePassword(user.Id);
            if (!removeResult.Succeeded)
            {
                return false;
            }
            var addResult = applicationUserManager.AddPassword(user.Id, password);
            if (!addResult.Succeeded)
            {
                return false;
            }

            return true;
        }

        public bool AddResearchApplication(ResearchApplication researchApplication)
        {
            var existingResearchApplication = dbContext.ResearchApplications.FirstOrDefault();
            if (existingResearchApplication != null)
            {
                return true;
            }

            dbContext.ResearchApplications.Add(researchApplication);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddResearchPermitStatus(ResearchPermitStatus status)
        {
            var existingResearchPermitStatus = dbContext.ResearchPermitStatuses.FirstOrDefault(rs => rs.StatusName == status.StatusName);
            if (existingResearchPermitStatus != null)
            {
                return true;
            }

            dbContext.ResearchPermitStatuses.Add(status);
            return dbContext.SaveChanges() > 0;
        }
        
        public bool AddResearchType(ResearchType type)
        {
            var existingType = dbContext.ResearchTypes.FirstOrDefault(t => t.Name == type.Name);
            if (existingType != null)
            {
                return true;
            }
            dbContext.ResearchTypes.Add(type);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddHabitit(ConfHabitat habitat)
        {
            var existingHabitat = dbContext.ConfHabitats.FirstOrDefault(h => h.Name == habitat.Name);
            if (existingHabitat != null)
            {
                return true;
            }
            dbContext.ConfHabitats.Add(habitat);
            return dbContext.SaveChanges() > 0;
        }

        public bool AddEmailTemplate(EmailTemplate emailTemplate)
        {
            var existingEmailTemplate = dbContext.EmailTemplates.FirstOrDefault(h => h.TemplateName == emailTemplate.TemplateName);
            if (existingEmailTemplate != null)
            {
                return true;
            }
            dbContext.EmailTemplates.Add(emailTemplate);
            return dbContext.SaveChanges() > 0;
        }
        public bool UpdateEmailTemplateName(string oldTemplateName,string newTemplateName)
        {
            var existingEmailTemplate = dbContext.EmailTemplates.FirstOrDefault(h => h.TemplateName == oldTemplateName);
            if (existingEmailTemplate != null)
            {
                existingEmailTemplate.TemplateName = newTemplateName;
                return dbContext.SaveChanges() > 0;
            }
            
            return false;
            
        }
        public bool AddEmailTemplateField(EmailTemplateField emailTemplateField)
        {
            //var existingEmailTemplate = dbContext.EmailTemplateFields.FirstOrDefault(h => h. == emailTemplateField.TemplateName);
            //if (existingEmailTemplate != null)
            //{
            //    return true;
            //}
            dbContext.EmailTemplateFields.Add(emailTemplateField);
            return dbContext.SaveChanges() > 0;
        }        

        #endregion
    }
}
