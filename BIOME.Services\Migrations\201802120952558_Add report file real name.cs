namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class Addreportfilerealname : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.ResearchApplications", "RealMiscFileName", c => c.String());
            AddColumn("dbo.ResearchReports", "RealFileName", c => c.String());
            AddColumn("dbo.ResearchApplicationDrafts", "RealMiscFileName", c => c.String());
        }
        
        public override void Down()
        {
            DropColumn("dbo.ResearchApplicationDrafts", "RealMiscFileName");
            DropColumn("dbo.ResearchReports", "RealFileName");
            DropColumn("dbo.ResearchApplications", "RealMiscFileName");
        }
    }
}
