// <auto-generated />
namespace BIOME.Services
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class addRenewPermitBeforeExpiryPeriod : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(addRenewPermitBeforeExpiryPeriod));
        
        string IMigrationMetadata.Id
        {
            get { return "202005060208115_addRenewPermitBeforeExpiryPeriod"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return Resources.GetString("Source"); }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
