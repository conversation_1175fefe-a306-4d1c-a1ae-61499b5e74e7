﻿using System;
using System.ComponentModel.DataAnnotations;

namespace BIOME.Models
{
    public interface IEntity<TPrimaryKey> : IEntityAuditable
    {
        [Key]
        TPrimaryKey Id { get; set; }
    }

    public interface IEntityAuditable  
    {
        [ScaffoldColumn(false)]
        DateTimeOffset CreatedAt { get; set; }
        [ScaffoldColumn(false)]
        DateTimeOffset UpdatedAt { get; set; }
    }
    public interface IDescribableEntity
    {
        // Override this method to provide a description of the entity for audit purposes
        string Describe();
    }
}