﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Enumerations
{
    public class Account
    {

        public class API
        {
            public enum AccountStatus
            {
                UserNotFound = Constants.Account.API.AccountStatus.UserNotFound,
                UserNotFoundOrInactive = Constants.Account.API.AccountStatus.UserNotFoundOrInactive,
                InvalidLogin = Constants.Account.API.AccountStatus.InvalidLogin,
                AccountExpired = Constants.Account.API.AccountStatus.AccountExpired,
                Unverified = Constants.Account.API.AccountStatus.Unverified,
                Locked = Constants.Account.API.AccountStatus.Locked,
                PendingApproval = Constants.Account.API.AccountStatus.PendingApproval,
                PasswordExpired = Constants.Account.API.AccountStatus.PasswordExpired,

                FacebookUserInvalid = Constants.Account.API.AccountStatus.FacebookUserInvalid,
                NotEmailConfirmed = Constants.Account.API.AccountStatus.NotEmailConfirmed,
                AccountWithoutRole = Constants.Account.API.AccountStatus.AccountWithoutRole

            }
        }
    }
}
