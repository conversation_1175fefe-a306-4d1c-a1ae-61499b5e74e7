﻿using BIOME.Models;
using EntityFramework.DynamicFilters;
using Microsoft.AspNet.Identity.EntityFramework;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Validation;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Transactions;
using Azure.Security.KeyVault.Secrets;

namespace BIOME.Services
{
    public static class BiomeAzureVault
    {

        public static string GetConnectionStringFromAzureKeyVault()
        {
            string keyVaultURL = System.Configuration.ConfigurationManager.AppSettings["AzurekeyVaultURL"];
            if (!string.IsNullOrEmpty(keyVaultURL))
            {
                string keyVaultSecrect = System.Configuration.ConfigurationManager.AppSettings["AzurekeyVaultDBSecrect"];
                var keyVaultUri = new Uri(keyVaultURL);
                var client = new SecretClient(keyVaultUri, new Azure.Identity.DefaultAzureCredential());

                KeyVaultSecret connectionStringSecret = client.GetSecret(keyVaultSecrect);
                return connectionStringSecret.Value;
            }
            else {
               return System.Configuration.ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString;
            }

        }
    }
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser, BIOMERole, long, BIOMEUserLogin, BIOMEUserRole, BIOMEUserClaim>
    {
        /*public ApplicationDbContext() : base("DefaultConnection")
        {
            Database.SetInitializer(new MigrateDatabaseToLatestVersion<ApplicationDbContext, Configuration>());
        }*/

        public ApplicationDbContext() : base(BiomeAzureVault.GetConnectionStringFromAzureKeyVault())
        {
            this.Database.CommandTimeout = 300; //5 min //Fix for NPARKS-112
            Database.SetInitializer(new MigrateDatabaseToLatestVersion<ApplicationDbContext, Configuration>());
        }
        

        public static ApplicationDbContext Create()
        {
            return new ApplicationDbContext();
        }

        public DbSet<Models.Group> Groups { get; set; }

        //public DbSet<MainGroup> MainGroups { get; set; }
        //public DbSet<SubGroup> SubGroups { get; set; }
        //public DbSet<SubSubGroup> SubSubGroups { get; set; }
        public DbSet<SightingDetail> SightingDetails { get; set; }
        public DbSet<SightingFile> SightingFiles { get; set; }
        public DbSet<SightingComment> SightingComments { get; set; }
        public DbSet<SightingLike> SightingLikes { get; set; }
        public DbSet<SightingInappropriate> SightingInappropriates { get; set; }
        public DbSet<SightingVerificationName> SightingVerificationNames { get; set; }
        public DbSet<SightingVerificationVote> SightingVerificationVotes { get; set; }
        public DbSet<SightingTaxonomy> SightingToxonomies { get; set; }
        public DbSet<SightingTaxonomyClassification> SightingTaxonomyClassifications { get; set; }
        public DbSet<GuideDetail> GuideDetails { get; set; }
        public DbSet<Newsfeed> Newsfeeds { get; set; }
        public DbSet<ProjectDetail> ProjectDetails { get; set; }
        public DbSet<ProjectMember> ProjectMembers { get; set; }
        public DbSet<ProjectMemberInvited> ProjectMemberInviteds { get; set; }
        public DbSet<ProjectSighting> ProjectSightings { get; set; }
        public DbSet<BadgeDetail> BadgeDetails { get; set; }
        public DbSet<UserInfoBadge> UserInfoBadges { get; set; }
        public DbSet<UserInfoScore> UserInfoScores { get; set; }
        public DbSet<ConfSightingCategory> ConfSightingCategories { get; set; }
        public DbSet<ConfProjectCategory> ConfProjectCategories { get; set; }
        public DbSet<ConfThreatenedSpeciesName> ConfThreatenedSpeciesNames { get; set; }
        public DbSet<ConfHabitat> ConfHabitats { get; set; }
        public DbSet<ConfEnvironment> ConfEnvironments { get; set; }
        public DbSet<ConfListOther> ConfListOthers { get; set; }
        public DbSet<SystemParameters> SystemParameters { get; set; }
        public DbSet<MaskedSite> MaskedSites { get; set; }
        public DbSet<PageHeader> PageHeaders { get; set; }
        public DbSet<HomepageContact> HomepageContacts { get; set; }
        public DbSet<HomepageAppInfo> HomepageAppInfos { get; set; }
        public DbSet<PageFooter> PageFooters { get; set; }
        public DbSet<InternetHighlight> InternetHighlights { get; set; }
        public DbSet<IntranetHighlight> IntranetHighlights { get; set; }
        public DbSet<ResearchApplication> ResearchApplications { get; set; }
        public DbSet<ResearchPermitApplication> ResearchPermitApplications { get; set; }
        public DbSet<ResearchDiscussionForum> ResearchDiscussionForums { get; set; }
        public DbSet<ResearchDiscussionAnswer> ResearchDiscussionAnswers { get; set; }
        public DbSet<ResearchDiscussionUnsubscribe> ResearchDiscussionUnsubscribes { get; set; }
        public DbSet<ResearchPermitStatus> ResearchPermitStatuses { get; set; }
        public DbSet<ResearchPermitPass> ResearchPermitPasses { get; set; }
        public DbSet<ResearchPermitLetter> ResearchPermitLetters { get; set; }
        public DbSet<ResearchInbox> ResearchInboxes { get; set; }
        public DbSet<ResearchType> ResearchTypes { get; set; }
        public DbSet<Token> Tokens { get; set; }
        public DbSet<DeveloperInfo> DevelopersInfos { get; set; }
        public DbSet<ResourceDocumentType> ResourceDocumentTypes { get; set; }
        public DbSet<ResearchApplicationDraft> ResearchApplicationDrafts { get; set; }
        public DbSet<ResearchFieldSurveyTeamMember> ResearchFieldSurveyTeamMembers { get; set; }
        public DbSet<ResearchFieldSurveyTeamMemberDraft> ResearchFieldSurveyTeamMembersDraft { get; set; }

        public DbSet<ResourceStructureTemplate> ResourceStructureTemplates { get; set; }
        public DbSet<ResourceStructureTemplateColumn> ResourceStructureTemplateColumns { get; set; }

        public DbSet<ResourceDocument> ResourceDocuments { get; set; }

        public DbSet<EmailTemplate> EmailTemplates { get; set; }
        public DbSet<EmailTemplateField> EmailTemplateFields { get; set; }
        public DbSet<EmailOTP> EmailOTPs { get; set; }
        public DbSet<AuditTrailLogging> AuditTrailLoggings { get; set; }

        public DbSet<BackgroundJobTask> BackgroundJobTasks { get; set; }

        public DbSet<GISLocation> GISLocation { get; set; }
        public DbSet<MapLayerGroup> LayerGroup { get; set; }
        public DbSet<ResearchHeaderFooterTemplate> ResearchHeaderFooterTemplates { get; set; }
        public DbSet<ResearchTermsTemplate> ResearchTermsTemplates { get; set; }
        public DbSet<ResearchLetterTemplate> ResearchLetterTemplates { get; set; }
        public DbSet<ResearchPermitStudyLocation> ResearchPermitStudyLocationAccesses { get; set; }
        public DbSet<ResearchStudyLocation> ResearchStudyLocationAccesses { get; set; }
        public DbSet<ResearchInstitutional> ResearchInstitutionals { get; set; }

        public DbSet<MaintenanceNotice> MaintenanceNotices { get; set; }

        public DbSet<ResearchSiteVisit> ResearchSiteVisits { get; set; }

        public DbSet<Inbox> Inboxes { get; set; }

        public DbSet<ResourceMetaData> ResourceMetaDatas { get; set; }

        public DbSet<ResearchReport> ResearchReports { get; set; }

        public DbSet<Survey> Survey { get; set; }
        public DbSet<SurveyCategory> SurveyCategory { get; set; }
        public DbSet<SurveyLocation> SurveyLocation { get; set; }
     //   public DbSet<SurveyLocationAnswer> SurveyLocationAnswer { get; set; }
        public DbSet<SurveyMasterSpecies> SurveyMasterSpecies { get; set; }
        public DbSet<SurveyMembers> SurveyMembers { get; set; }
        public DbSet<SurveyQuestionDetail> SurveyQuestionDetail { get; set; }
        public DbSet<SurveyQuestions> SurveyQuestions { get; set; }
        public DbSet<SurveyQuestionsAnswer> SurveyQuestionsAnswer { get; set; }
        public DbSet<SurveyQuestionType> SurveyQuestionType { get; set; }
        public DbSet<SurveySpecies> SurveySpecies { get; set; }
        public DbSet<SurveySpeciesAnswer> SurveySpeciesAnswer { get; set; }
        public DbSet<SurveySubmission> SurveySubmission { get; set; }
        public DbSet<SurveyMemberInvited> SurveyMemberInviteds { get; set; }
        public DbSet<Feedback> FeedBacks { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<AuditLogAction> AuditLogActions { get; set; }

        public DbSet<UserAuthSessionStore> UserAuthSessionStore { get; set; } //Used sessoin store to solve logout vulnerability issue.

        public DbSet<ToDoList> ToDoLists { get; set; } //ToDoList CR - BIOME Email Templates impacted by removal of URL
        public DbSet<ConcurrentLoginAlert> ConcurrentLoginAlerts { get; set; } //Prevent concurrent login CR

        public DbSet<MobileUserSession> MobileUserSessions { get; set; } //SgBioAtlas session timeout CR
        public DbSet<FileStoreTmp> FileStoreTmps{ get; set; }
        //public DbSet<ApplicationListingView> ApplicationListingViews { get; set; }
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ApplicationUser>()
                .HasMany(m => m.Followers)
                .WithMany(m => m.Following)
                .Map(x => x.MapLeftKey("UserId")
                .MapRightKey("FollowerId")
                .ToTable("UserFollowers"));

            modelBuilder.Entity<ResearchApplication>()
            .HasMany(e => e.StudySubjects)
            .WithMany(m => m.ResearchApplications)
            .Map(mc =>
            {
                mc.ToTable("ResearchApplicationConfSightingCategories");
                mc.MapLeftKey("ResearchApplication_Id");
                mc.MapRightKey("ConfSightingCategory_Id");
            });

            // Many to many: A group has admins, and a person has groups where he is admin
            modelBuilder.Entity<ResourceMetaData>()
                .HasMany(e => e.Categories)
                .WithMany(m => m.ResourceMetaDatas)
                .Map(mc =>
                {
                    mc.ToTable("ResourceMetaDataConfSightingCategories");
                    mc.MapLeftKey("ResourceMetaData_Id");
                    mc.MapRightKey("ConfSightingCategory_Id");
                });

            modelBuilder.Filter("IsDeleted", (ISoftDelete d) => d.IsDeleted, false);

            //CR3&CR4 Phase2
            modelBuilder.Entity<ResearchPermitApplication>().Property(t => t.CoralCollectionAmountInCubicMeter).HasPrecision(18, 10);
            modelBuilder.Entity<ResearchApplication>().Property(t => t.CoralCollectionAmountInCubicMeter).HasPrecision(18, 10);
            modelBuilder.Entity<ResearchApplicationDraft>().Property(t => t.CoralCollectionAmountInCubicMeter).HasPrecision(18, 10);
            modelBuilder.Entity<ApplicationUser>().Property(t => t.Email).HasMaxLength(320);

            //modelBuilder.Entity<ApplicationListingView>().ToTable("PermitApplicationsAll");

            base.OnModelCreating(modelBuilder);
        }

        public override int SaveChanges()
        {
            return SaveChanges(false);
        }

        public override Task<int> SaveChangesAsync()
        {
            return SaveChangesAsync(false);
        }
        public Task<int> SaveChangesAsyncWithoutLog()
        {
            return base.SaveChangesAsync();
        }

        public int SaveChanges(bool forceDelete = false, int userId = 0, int maxid = 0)
        {
            try
            {
                foreach (var entry in ChangeTracker.Entries<IEntityAuditable>()
                  .Where(e => (e.State == EntityState.Modified)))
                {
                    entry.Property("UpdatedAt").CurrentValue = DateTimeOffset.Now;
                    entry.Property("UpdatedAt").IsModified = true;
                }

                if (!forceDelete)
                {
                    foreach (var entry in ChangeTracker.Entries()
                              .Where(p => p.State == EntityState.Deleted))
                        SoftDelete(entry); 
                }



                int changes = 0;
                if (userId != 0)
                {
                    try
                    {
                        using (var scope = new TransactionScope())
                        {
                            var addedEntries = ChangeTracker.Entries().Where(e => e.State == EntityState.Added).ToList();
                            var modifiedEntries = ChangeTracker.Entries().Where(e => e.State == EntityState.Modified).ToList();
                            var deleteEntried = ChangeTracker.Entries().Where(e => e.State == EntityState.Deleted).ToList();
                            foreach (var ent in modifiedEntries)
                            {

                                foreach (AuditLog x in GetAuditRecordsForChange(ent, userId, maxid, EntityState.Modified))
                                {
                                    this.AuditLogs.Add(x);
                                }
                            }


                            foreach (var ent in deleteEntried)
                            {

                                foreach (AuditLog x in GetAuditRecordsForChange(ent, userId, maxid, EntityState.Deleted))
                                {
                                    this.AuditLogs.Add(x);
                                }
                            }
                            changes = base.SaveChanges();

                            foreach (var ent in addedEntries)
                            {

                                foreach (AuditLog x in GetAuditRecordsForChange(ent, userId, maxid, EntityState.Added))
                                {
                                    this.AuditLogs.Add(x);
                                }
                            }

                            base.SaveChanges();
                            scope.Complete();
                            return changes;

                        }

                    }
                    catch (Exception ex)
                    {
                        throw ex;

                    }


                    return changes;
                }
                else
                {
                    return base.SaveChanges();
                }


            }
            catch (DbEntityValidationException ex)
            {
                var sb = new StringBuilder();

                foreach (var failure in ex.EntityValidationErrors)
                {
                    sb.AppendFormat("{0} failed validation\n", failure.Entry.Entity.GetType());
                    foreach (var error in failure.ValidationErrors)
                    {
                        sb.AppendFormat("- {0} : {1}", error.PropertyName, error.ErrorMessage);
                        sb.AppendLine();
                    }
                }

                throw new DbEntityValidationException(
                    "Entity Validation Failed - errors follow:\n" +
                    sb.ToString(), ex
                    ); // Add the original exception as the innerException
            }
        }

        public int SaveChangesOnly()
        {
            return base.SaveChanges();
        }

        public Task<int> SaveChangesAsync(bool forceDelete = false, int userId = 0, int maxid = 0)
        {
            try
            {
                foreach (var entry in ChangeTracker.Entries<IEntityAuditable>()
                  .Where(e => (e.State == EntityState.Modified)))
                {
                    entry.Property("UpdatedAt").CurrentValue = DateTimeOffset.Now;
                    entry.Property("UpdatedAt").IsModified = true;
                }

                if (!forceDelete)
                {
                    foreach (var entry in ChangeTracker.Entries()
                              .Where(p => p.State == EntityState.Deleted))
                        SoftDelete(entry); 
                }

                try
                {
                    if (userId != 0)
                    {
                        foreach (var ent in this.ChangeTracker.Entries().Where(p => p.State == System.Data.Entity.EntityState.Added || p.State == System.Data.Entity.EntityState.Deleted || p.State == System.Data.Entity.EntityState.Modified))
                        {
                            // For each changed record, get the audit record entries and add them
                            foreach (AuditLog x in GetAuditRecordsForChange(ent, userId, maxid, ent.State))
                            {
                                this.AuditLogs.Add(x);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {


                }

                return base.SaveChangesAsync();
            }
            catch (DbEntityValidationException ex)
            {
                var sb = new StringBuilder();

                foreach (var failure in ex.EntityValidationErrors)
                {
                    sb.AppendFormat("{0} failed validation\n", failure.Entry.Entity.GetType());
                    foreach (var error in failure.ValidationErrors)
                    {
                        sb.AppendFormat("- {0} : {1}", error.PropertyName, error.ErrorMessage);
                        sb.AppendLine();
                    }
                }

                throw new DbEntityValidationException(
                    "Entity Validation Failed - errors follow:\n" +
                    sb.ToString(), ex
                    ); // Add the original exception as the innerException
            }
        }

        private void SoftDelete(DbEntityEntry entry)
        {
            //Type entryEntityType = entry.Entity.GetType();

            var isSoftDeleteType = typeof(ISoftDelete).IsInstanceOfType(entry.Entity);
            if (isSoftDeleteType)
            {

                entry.State = EntityState.Unchanged;
                entry.Property("IsDeleted").IsModified = true;
                entry.Property("IsDeleted").CurrentValue = true;
            }
        }

        private bool IsInstanceOfIEntity(DbEntityEntry entry)
        {
            return typeof(IEntity<>).IsInstanceOfType(entry.Entity);
        }

        public System.Data.Entity.DbSet<BIOME.ViewModels.MaskedSiteViewModel> MaskedSiteViewModels { get; set; }

        public System.Data.Entity.DbSet<BIOME.Models.ResourceFilePermission> ResourceFilePermissions { get; set; }

        private List<AuditLog> GetAuditRecordsForChange(DbEntityEntry dbEntry, int userId, int maxid , System.Data.Entity.EntityState state)
        {
            List<AuditLog> result = new List<AuditLog>();

            DateTime changeTime = DateTime.Now;

            // Get the Table() attribute, if one exists
            TableAttribute tableAttr = dbEntry.Entity.GetType().GetCustomAttributes(typeof(TableAttribute), false).SingleOrDefault() as TableAttribute;

            // Get table name (if it has a Table attribute, use that, otherwise get the pluralized name)
            string tableName = tableAttr != null ? tableAttr.Name : dbEntry.Entity.GetType().Name;
            var index = tableName.IndexOf("_");
            if ( index >0)
            tableName = tableName.Substring(0,index);
            // Get primary key value (If you have more than one key column, this will need to be adjusted)
            string keyName = "Id";// dbEntry.Entity.GetType().GetProperties().Single(p => p.GetCustomAttributes(typeof(KeyAttribute), false).Count() > 0).Name;

            if (state == System.Data.Entity.EntityState.Added)
            {
                
                if (tableName != "AuditTrailLogging" && tableName != "BackgroundJobTask")
                {
                    // For Inserts, just add the whole record
                    // If the entity implements IDescribableEntity, use the description from Describe(), otherwise use ToString()
                    result.Add(new AuditLog()
                    {
                        AuditLogID = Guid.NewGuid(),
                        UserID = userId,
                        EventDate = changeTime,
                        EventType = "A", // Added
                        TableName = tableName,
                        RecordID = dbEntry.CurrentValues.GetValue<object>(keyName).ToString(),  // Again, adjust this if you have a multi-column key
                        ColumnName = "*ALL",    // Or make it nullable, whatever you want
                        NewValue = (dbEntry.CurrentValues.ToObject() is IDescribableEntity) ? (dbEntry.CurrentValues.ToObject() as IDescribableEntity).Describe() : dbEntry.CurrentValues.ToObject().ToString()
                    }
                    );
                }
            }
            else if (state == System.Data.Entity.EntityState.Deleted)
            {
                if (tableName != "AuditTrailLogging" && tableName != "BackgroundJobTask")
                {
                    // Same with deletes, do the whole record, and use either the description from Describe() or ToString()
                    result.Add(new AuditLog()
                    {
                        AuditLogID = Guid.NewGuid(),
                        UserID = userId,
                        EventDate = changeTime,
                        EventType = "D", // Deleted
                        TableName = tableName,
                        RecordID = dbEntry.OriginalValues.GetValue<object>(keyName).ToString(),
                        ColumnName = "*ALL",
                        NewValue = (dbEntry.OriginalValues.ToObject() is IDescribableEntity) ? (dbEntry.OriginalValues.ToObject() as IDescribableEntity).Describe() : dbEntry.OriginalValues.ToObject().ToString()
                    }
                    );
                }
            }
            else if (state == System.Data.Entity.EntityState.Modified)
            {
                foreach (string propertyName in dbEntry.OriginalValues.PropertyNames)
                {
                    // For updates, we only want to capture the columns that actually changed
                    if (!object.Equals(dbEntry.OriginalValues.GetValue<object>(propertyName), dbEntry.CurrentValues.GetValue<object>(propertyName)))
                    {
                        if (tableName != "AuditTrailLogging" && tableName != "BackgroundJobTask")

                        {
                            if (propertyName != "UpdatedAt")
                            {
                                result.Add(new AuditLog()
                                {
                                    AuditLogID = Guid.NewGuid(),
                                    UserID = userId,
                                    EventDate = changeTime,
                                    EventType = "M",    // Modified
                                    TableName = tableName,
                                    RecordID = dbEntry.OriginalValues.GetValue<object>(keyName).ToString(),
                                    ColumnName = propertyName,
                                    OriginalValue = dbEntry.OriginalValues.GetValue<object>(propertyName) == null ? null : dbEntry.OriginalValues.GetValue<object>(propertyName).ToString(),
                                    NewValue = dbEntry.CurrentValues.GetValue<object>(propertyName) == null ? null : dbEntry.CurrentValues.GetValue<object>(propertyName).ToString()
                                }
                                );
                            }
                        }
                    }
                }
            }
            // Otherwise, don't do anything, we don't care about Unchanged or Detached entities

            return result;
        }

    }
}
