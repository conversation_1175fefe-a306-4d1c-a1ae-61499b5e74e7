﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations.Schema;
namespace BIOME.Models
{
    public class SurveySpeciesAnswer : Entity<long>,IDescribableEntity 
    {
        public long? SurveySubmissionId { get; set; } //need index
        [ForeignKey("SurveySubmissionId")]
        public virtual SurveySubmission SurveySubmission { get; set; }
        public long? SurveySpeciesId { get; set; }
        [ForeignKey("SurveySpeciesId")]
        public virtual SurveySpecies SurveySpecies { get; set; }
        public Int32 Quantity { get; set; }
        public string Species { get; set; }
        public string CommonName { get; set; }
        public string Description { get; set; }
        public string Photo1 { get; set; }
        public string ImagePath { get; set; }

        public string Describe()
        {
            return "{ SurveySubmissionId : \"" + SurveySubmissionId + "\", SurveySpeciesId : \"" + SurveySpeciesId + "\", Quantity : \"" + Quantity + "\", Species : \"" + Species
                + "\", CommonName : \"" + CommonName + "\", Description : \"" + Description + "\", Photo1 : \"" + Photo1 + "\", ImagePath : \"" + ImagePath + "}";
        }
        public string GetImage()
        {
            var imagename = "/Content/images/nophotoavailable.jpg";
            if (!string.IsNullOrEmpty(Photo1))
            {
                imagename = ImagePath + Photo1;
            }

            return imagename;
        }
    }
}
