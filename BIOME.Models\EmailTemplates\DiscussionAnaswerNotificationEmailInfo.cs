﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models.EmailTemplates
{
    public class DiscussionAnaswerNotificationEmailInfo : EmailInfoBase
    {
        public string NewAnswerLink { get; set; }
        public string Answer { get; set; }

        public DiscussionAnaswerNotificationEmailInfo(string senderName, string senderEmail, string subject, List<string> recipients, string title) : base(senderName, senderEmail, subject, recipients, title)
        {

        }
    }
}
