﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BIOME.Models;

namespace BIOME.Services
{
    public class MapService : ServiceBase, IMapService
    {
        private readonly ApplicationDbContext dbContext;

        public MapService(ApplicationDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public List<MapLayerGroup> GetLayerGroups()
        {
            return (from lg in dbContext.LayerGroup
                    orderby lg.GroupName ascending
                    select lg).ToList();
        }
    }
}
