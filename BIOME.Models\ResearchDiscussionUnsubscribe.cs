﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class ResearchDiscussionUnsubscribe : Entity<long>, IDescribableEntity
    {
        public long AtDiscussionForumId { get; set; }
        [ForeignKey("AtDiscussionForumId")]
        public virtual ResearchDiscussionForum AtResearchDiscussionForum { get; set; }


        public long UnsubscribeByUserId { get; set; }

        public string Describe()
        {
            return "{ AtDiscussionForumId : \"" + AtDiscussionForumId + "\", UnsubscribeByUserId : \"" + UnsubscribeByUserId +  "}";
        }
        public ResearchDiscussionUnsubscribe()
        {
        }
    }
}
