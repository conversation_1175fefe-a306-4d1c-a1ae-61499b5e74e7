namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class issuefixed : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.SurveyLocations", "Location_Id", "dbo.GISLocations");
            DropForeignKey("dbo.SurveyLocations", "Survey_Id", "dbo.Surveys");
            DropForeignKey("dbo.SurveyMembers", "MemberId", "dbo.AspNetUsers");
            DropForeignKey("dbo.SurveyMembers", "SurveyId", "dbo.Surveys");
            DropForeignKey("dbo.SurveyQuestions", "Survey_Id", "dbo.Surveys");
            DropForeignKey("dbo.SurveyQuestionDetails", "SurveyQuestion_Id", "dbo.SurveyQuestions");
            DropForeignKey("dbo.SurveySpecies", "SurveyId", "dbo.Surveys");
            DropForeignKey("dbo.SurveySpecies", "SpeciesId", "dbo.SurveyMasterSpecies");
            DropFore<PERSON><PERSON><PERSON>("dbo.SurveySubmissions", "SurveyId", "dbo.Surveys");
            DropForeignKey("dbo.SurveySubmissions", "SurveyLocationID", "dbo.SurveyLocations");
            DropForeignKey("dbo.SurveyQuestionsAnswers", "QuestionDetailId", "dbo.SurveyQuestionDetails");
            DropForeignKey("dbo.SurveyQuestionsAnswers", "QuestionId", "dbo.SurveyQuestions");
            DropForeignKey("dbo.SurveyQuestionsAnswers", "SurveySubmissionId", "dbo.SurveySubmissions");
            DropForeignKey("dbo.SurveySpeciesAnswers", "SurveySpeciesId", "dbo.SurveySpecies");
            DropForeignKey("dbo.SurveySpeciesAnswers", "SurveySubmissionId", "dbo.SurveySubmissions");
            DropForeignKey("dbo.SurveySubmissions", "UserId", "dbo.AspNetUsers");
            DropIndex("dbo.SurveyLocations", new[] { "Location_Id" });
            DropIndex("dbo.SurveyLocations", new[] { "Survey_Id" });
            DropIndex("dbo.SurveyMembers", new[] { "SurveyId" });
            DropIndex("dbo.SurveyMembers", new[] { "MemberId" });
            DropIndex("dbo.SurveyQuestions", new[] { "Survey_Id" });
            DropIndex("dbo.SurveyQuestionDetails", new[] { "SurveyQuestion_Id" });
            DropIndex("dbo.SurveySpecies", new[] { "SurveyId" });
            DropIndex("dbo.SurveySpecies", new[] { "SpeciesId" });
            DropIndex("dbo.SurveySubmissions", new[] { "UserId" });
            DropIndex("dbo.SurveySubmissions", new[] { "SurveyId" });
            DropIndex("dbo.SurveySubmissions", new[] { "SurveyLocationID" });
            DropIndex("dbo.SurveyQuestionsAnswers", new[] { "SurveySubmissionId" });
            DropIndex("dbo.SurveyQuestionsAnswers", new[] { "QuestionId" });
            DropIndex("dbo.SurveyQuestionsAnswers", new[] { "QuestionDetailId" });
            DropIndex("dbo.SurveySpeciesAnswers", new[] { "SurveySubmissionId" });
            DropIndex("dbo.SurveySpeciesAnswers", new[] { "SurveySpeciesId" });
            DropTable("dbo.Surveys");
            DropTable("dbo.SurveyLocations");
            DropTable("dbo.SurveyMembers");
            DropTable("dbo.SurveyQuestions");
            DropTable("dbo.SurveyQuestionDetails");
            DropTable("dbo.SurveySpecies");
            DropTable("dbo.SurveyMasterSpecies");
            DropTable("dbo.SurveySubmissions");
            DropTable("dbo.SurveyQuestionsAnswers");
            DropTable("dbo.SurveySpeciesAnswers");
            DropTable("dbo.SurveyCategories");
            DropTable("dbo.SurveyQuestionTypes");
        }
        
        public override void Down()
        {
            CreateTable(
                "dbo.SurveyQuestionTypes",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Name = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveyCategories",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Name = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveySpeciesAnswers",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        SurveySubmissionId = c.Long(),
                        SurveySpeciesId = c.Long(),
                        Quantity = c.Int(nullable: false),
                        Species = c.String(),
                        CommonName = c.String(),
                        Description = c.String(),
                        Photo1 = c.String(),
                        ImagePath = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveyQuestionsAnswers",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        SurveySubmissionId = c.Long(nullable: false),
                        QuestionId = c.Long(nullable: false),
                        QuestionDetailId = c.Long(),
                        AnswerText = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveySubmissions",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        UserId = c.Long(nullable: false),
                        SurveyId = c.Long(nullable: false),
                        SubmissionDate = c.DateTime(nullable: false),
                        HasAdditionalSpecies = c.Boolean(nullable: false),
                        SubmissionStatus = c.Int(nullable: false),
                        SurveyLocationID = c.Long(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveyMasterSpecies",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        FamilySuborder = c.String(),
                        Category = c.String(),
                        CategoryName = c.String(),
                        Grouping = c.String(),
                        Species = c.String(),
                        CommonName = c.String(),
                        Description = c.String(),
                        Photo1 = c.String(),
                        Photo2 = c.String(),
                        Photo3 = c.String(),
                        Photo4 = c.String(),
                        Attributes = c.String(),
                        Others = c.String(),
                        ImagePath = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveySpecies",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        SurveyId = c.Long(nullable: false),
                        SpeciesId = c.Long(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveyQuestionDetails",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Description = c.String(),
                        isAnswer = c.Boolean(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        SurveyQuestion_Id = c.Long(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveyQuestions",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Title = c.String(),
                        IsBefore = c.Boolean(nullable: false),
                        QuestionTypeID = c.Long(nullable: false),
                        IsCompulsory = c.Boolean(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        Survey_Id = c.Long(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveyMembers",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        SurveyId = c.Long(nullable: false),
                        MemberId = c.Long(nullable: false),
                        MemberRank = c.Int(nullable: false),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SurveyLocations",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Remarks = c.String(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        Location_Id = c.Long(),
                        Survey_Id = c.Long(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.Surveys",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        Title = c.String(),
                        Description = c.String(),
                        ViewPermission = c.Int(),
                        DownloadPermission = c.Int(),
                        IsAutoApproveM = c.Boolean(nullable: false),
                        IsSearchable = c.Boolean(nullable: false),
                        IsActive = c.Boolean(nullable: false),
                        SurveyStatus = c.Int(),
                        ApprovedById = c.Long(),
                        Duration = c.Int(),
                        StartDate = c.DateTime(),
                        EndDate = c.DateTime(),
                        OwnerId = c.Long(nullable: false),
                        SurveyCategoryId = c.Long(),
                        LogoName = c.String(),
                        LogoPath = c.String(),
                        MemberCount = c.Int(),
                        CreatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                        UpdatedAt = c.DateTimeOffset(nullable: false, precision: 7),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateIndex("dbo.SurveySpeciesAnswers", "SurveySpeciesId");
            CreateIndex("dbo.SurveySpeciesAnswers", "SurveySubmissionId");
            CreateIndex("dbo.SurveyQuestionsAnswers", "QuestionDetailId");
            CreateIndex("dbo.SurveyQuestionsAnswers", "QuestionId");
            CreateIndex("dbo.SurveyQuestionsAnswers", "SurveySubmissionId");
            CreateIndex("dbo.SurveySubmissions", "SurveyLocationID");
            CreateIndex("dbo.SurveySubmissions", "SurveyId");
            CreateIndex("dbo.SurveySubmissions", "UserId");
            CreateIndex("dbo.SurveySpecies", "SpeciesId");
            CreateIndex("dbo.SurveySpecies", "SurveyId");
            CreateIndex("dbo.SurveyQuestionDetails", "SurveyQuestion_Id");
            CreateIndex("dbo.SurveyQuestions", "Survey_Id");
            CreateIndex("dbo.SurveyMembers", "MemberId");
            CreateIndex("dbo.SurveyMembers", "SurveyId");
            CreateIndex("dbo.SurveyLocations", "Survey_Id");
            CreateIndex("dbo.SurveyLocations", "Location_Id");
            AddForeignKey("dbo.SurveySubmissions", "UserId", "dbo.AspNetUsers", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveySpeciesAnswers", "SurveySubmissionId", "dbo.SurveySubmissions", "Id");
            AddForeignKey("dbo.SurveySpeciesAnswers", "SurveySpeciesId", "dbo.SurveySpecies", "Id");
            AddForeignKey("dbo.SurveyQuestionsAnswers", "SurveySubmissionId", "dbo.SurveySubmissions", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveyQuestionsAnswers", "QuestionId", "dbo.SurveyQuestions", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveyQuestionsAnswers", "QuestionDetailId", "dbo.SurveyQuestionDetails", "Id");
            AddForeignKey("dbo.SurveySubmissions", "SurveyLocationID", "dbo.SurveyLocations", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveySubmissions", "SurveyId", "dbo.Surveys", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveySpecies", "SpeciesId", "dbo.SurveyMasterSpecies", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveySpecies", "SurveyId", "dbo.Surveys", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveyQuestionDetails", "SurveyQuestion_Id", "dbo.SurveyQuestions", "Id");
            AddForeignKey("dbo.SurveyQuestions", "Survey_Id", "dbo.Surveys", "Id");
            AddForeignKey("dbo.SurveyMembers", "SurveyId", "dbo.Surveys", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveyMembers", "MemberId", "dbo.AspNetUsers", "Id", cascadeDelete: true);
            AddForeignKey("dbo.SurveyLocations", "Survey_Id", "dbo.Surveys", "Id");
            AddForeignKey("dbo.SurveyLocations", "Location_Id", "dbo.GISLocations", "Id");
        }
    }
}
