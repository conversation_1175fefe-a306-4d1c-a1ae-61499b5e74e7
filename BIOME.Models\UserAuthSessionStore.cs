﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class UserAuthSessionStore : IDescribableEntity
    {
        [Key]
        public string Key { get; set; }
        public string TicketString { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public long UserID { get; set; }
        public int SiteType { get; set; } //Added for prevent concurrent login CR Aug-2022
        public bool ToRemoved { get; set; } //Added for prevent concurrent login CR Aug-2022
        public UserAuthSessionStore()
        {
            CreatedAt = DateTimeOffset.Now;
        }
        public string Describe()
        {
            return "{ Key : \"" + Key + "\", TicketString : \"" + TicketString + "}";
        }
    }
}
