﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNet.Identity;
using BIOME.Models;
using Microsoft.AspNet.Identity.Owin;
using Microsoft.Owin.Security;
using System.Security.Claims;
using System.Data.Entity.Utilities;
using static BIOME.Constants.Configuration.Server;

namespace BIOME.Services
{
    public class ApplicationUserManager : UserManager<ApplicationUser, long>
    {
        public Microsoft.Owin.Security.DataProtection.IDataProtectionProvider dataProtectionProvider;
        public Microsoft.Owin.Security.DataProtection.IDataProtector Protector { get; set; }

        public ApplicationUserManager(IUserStore<ApplicationUser, long> store, IdentityFactoryOptions<ApplicationUserManager> options)
            : base(store)
        {
            PasswordHasher = new BIOMEPasswordHasher();
        
            // Configure validation logic for usernames
            UserValidator = new UserValidator<ApplicationUser, long>(this)
            {
                AllowOnlyAlphanumericUserNames = false,
                RequireUniqueEmail = true
            };

            // Configure validation logic for passwords
            PasswordValidator = new PasswordValidator
            { 
                RequiredLength = 12,
                RequireDigit = true
            };

            // Configure user lockout defaults
            UserLockoutEnabledByDefault = true;
            DefaultAccountLockoutTimeSpan = TimeSpan.FromMinutes(1);
            //DefaultAccountLockoutTimeSpan = TimeSpan.FromMinutes(5);
            MaxFailedAccessAttemptsBeforeLockout = 6;

            //// Register two factor authentication providers. This application uses Phone and Emails as a step of receiving a code for verifying the user
            //// You can write your own provider and plug it in here.
            //RegisterTwoFactorProvider("Phone Code", new PhoneNumberTokenProvider<ApplicationUser>
            //{
            //    MessageFormat = "Your security code is {0}"
            //});
            //RegisterTwoFactorProvider("Email Code", new EmailTokenProvider<ApplicationUser>
            //{
            //    Subject = "Security Code",
            //    BodyFormat = "Your security code is {0}"
            //});
            EmailService = new IdentityEmailService();
            //SmsService = new SmsService();

            

            dataProtectionProvider = options.DataProtectionProvider;
            if (dataProtectionProvider != null)
            {
                UserTokenProvider = new DataProtectorTokenProvider<ApplicationUser, long>(dataProtectionProvider.Create("ASP.NET Identity"))
                {
                    //change token expire date
                    //TokenLifespan = TimeSpan.FromMinutes(15)
                    //Fixes for NPARK/BIOME/NCODE/2020_0089. Increased to 45 mins
                    TokenLifespan = TimeSpan.FromMinutes(Constants.Account.Identity_TokenLifeSpanInMinutes)
                    //TokenLifespan = TimeSpan.FromMinutes(45)
                };
                Protector = dataProtectionProvider.Create("ASP.NET Identity");
            }

            /*Protector = dataProtectionProvider.Create("ASP.NET Identity");*/
        }

        internal string GeneratePasswordResetTokenAsync()
        {
            throw new NotImplementedException();
        }

        public override async Task<IdentityResult> ChangePasswordAsync(long userId, string currentPassword, string newPassword)
        {
            var usr = await FindByIdAsync(userId);

            if (string.IsNullOrEmpty(usr.IV) || string.IsNullOrEmpty(usr.Key))
            {
                DynamicPasswordCipher.IV = Utilities.Helpers.StringHelper.RandomString(32);
                DynamicPasswordCipher.Key = Utilities.Helpers.StringHelper.RandomString(64);
            }
            else
            {
                DynamicPasswordCipher.IV = usr.IV;
                DynamicPasswordCipher.Key = usr.Key;
            }
            
            
            if (await IsPreviousPasswordAsync(userId, newPassword))
            {
                return await Task.FromResult(IdentityResult.Failed("Cannot reuse old password"));
            }
            
            /*DynamicPasswordCipher.IV =usr.IV;
            DynamicPasswordCipher.Key = usr.Key;*/
            var result = await base.ChangePasswordAsync(userId, currentPassword, newPassword);
            if (result.Succeeded)
            {
                var store = Store as BIOMEUserStore;
                var user = await FindByIdAsync(userId);
                
                await store.AddToPreviousPasswordsAsync(user, PasswordHasher.HashPassword(newPassword));
                if (!string.IsNullOrEmpty(newPassword))
                    await UpdateDateLastChangePassword(await FindByIdAsync(userId));
            }

            return result;
        }

        public override async Task<IdentityResult> ResetPasswordAsync(long userId, string token, string newPassword)
        {
            //Fixes for resetting of Password issue

            var usr = await FindByIdAsync(userId);
            if (string.IsNullOrEmpty(usr.IV) || string.IsNullOrEmpty(usr.Key)){

                DynamicPasswordCipher.IV = Utilities.Helpers.StringHelper.RandomString(32);
                DynamicPasswordCipher.Key = Utilities.Helpers.StringHelper.RandomString(64);
            }
            else {
                DynamicPasswordCipher.IV = usr.IV;
                DynamicPasswordCipher.Key = usr.Key;
            }
            
            
            if (await IsPreviousPasswordAsync(userId, newPassword))
            {
                return await Task.FromResult(IdentityResult.Failed("Cannot reuse old password"));
            }
            /*DynamicPasswordCipher.IV = Utilities.Helpers.StringHelper.RandomString(32);
            DynamicPasswordCipher.Key = Utilities.Helpers.StringHelper.RandomString(64);*/
            var result = await base.ResetPasswordAsync(userId, token, newPassword);
            if (result.Succeeded)
            {
                var store = Store as BIOMEUserStore;
                var user = await FindByIdAsync(userId);
                user.IV = DynamicPasswordCipher.IV;
                user.Key = DynamicPasswordCipher.Key;
                await store.AddToPreviousPasswordsAsync(user, PasswordHasher.HashPassword(newPassword));
                if (!string.IsNullOrEmpty(newPassword))
                    await UpdateDateLastChangePassword(await FindByIdAsync(userId));
            }

            return result;
        }

        private async Task<bool> IsPreviousPasswordAsync(long userId, string newPassword)
        {
            var user = await FindByIdAsync(userId);
            if (user.PreviousUserPasswords.OrderByDescending(x => x.CreatedAt)
                .Select(x => x.PasswordHash).Take(BIOME.Constants.Account.Password.PasswordHistoryLimit).Where(x => PasswordHasher.VerifyHashedPassword(x, newPassword) != PasswordVerificationResult.Failed
                ).Any())
            {
                return true;
            }
            return false;
        }

        private Task UpdateDateLastChangePassword(ApplicationUser user)
        {
            user.DateLastChangePassword = DateTimeOffset.Now;
            user.AccessFailedCount = 0;
            user.DateLastLogin = DateTimeOffset.Now; 
            return UpdateAsync(user);
        }
    }
    public class ApplicationSignInManager : SignInManager<ApplicationUser, long>
    {
        public ApplicationSignInManager(ApplicationUserManager userManager, IAuthenticationManager authenticationManager) :
            base(userManager, authenticationManager)
        { }

        public override Task<ClaimsIdentity> CreateUserIdentityAsync(ApplicationUser user)
        {
            return user.GenerateUserIdentityAsync((ApplicationUserManager)UserManager);
        }

        //public static ApplicationSignInManager Create(IdentityFactoryOptions<ApplicationSignInManager> options, IOwinContext context)
        //{
        //    return new ApplicationSignInManager(context.GetUserManager<ApplicationUserManager>(), context.Authentication);
        //}
    }

    public class ApplicationRoleManager : RoleManager<BIOMERole, long>
    {
        public ApplicationRoleManager(IRoleStore<BIOMERole, long> store) : base(store)
        {

        }
    }

}
