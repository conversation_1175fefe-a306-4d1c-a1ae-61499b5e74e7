﻿using AutoMapper;
using BIOME.Models;
using BIOME.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public class ResearchContactPersonResolver : ValueResolver<PermitViewModel.ApplicationFormViewModelBase, string>
    {
        protected override string ResolveCore(PermitViewModel.ApplicationFormViewModelBase source)
        {
            if (!source.CollaboratingWithNParks)
            {
                return null;
            }

            return source.NParksContactPersonEmailWithoutDomain;
        }
    }

    public class ResearchContactPersonDraftResolver : ValueResolver<ResearchApplicationDraft, string>
    {
        protected override string ResolveCore(ResearchApplicationDraft source)
        {
            if (!source.CollaboratingWithNParks)
            {
                return null;
            }

            return source.NParksContactPersonEmailWithoutDomain;
        }
    }
}
