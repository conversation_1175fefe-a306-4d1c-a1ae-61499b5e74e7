﻿using AutoMapper;
using BIOME.ViewModels;
using BIOME.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public class ResearchPresentFindingsDateStartResolver : ValueResolver<PermitViewModel.ApplicationFormViewModelBase, DateTimeOffset?>
    {
        protected override DateTimeOffset? ResolveCore(PermitViewModel.ApplicationFormViewModelBase source)
        {
            if (!source.PresentFindings)
            {
                return null;
            }

            return source.PresentFindingsDateStart;
        }
    }

    public class ResearchPresentFindingsDateStartDraftResolver : ValueResolver<ResearchApplicationDraft, DateTimeOffset?>
    {
        protected override DateTimeOffset? ResolveCore(ResearchApplicationDraft source)
        {
            if (!source.PresentFindings)
            {
                return null;
            }

            return source.PresentFindingsDateStart;
        }
    }
}
