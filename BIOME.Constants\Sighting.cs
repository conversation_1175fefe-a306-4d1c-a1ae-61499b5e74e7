﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Constants
{
    public class Sighting
    {
        /*
        public class Status
        {
            public const string Draft = "Draft";        //only show in my sightings
            public const string Pending = "Pending";    //need verification
            public const string Approved = "Approved";  //verified by vote
            //public const string Expert = "Expert";      //verified by expert
        }*/

        public class MySightingsOrderBy
        {
            public const string Submitted = "Submitted";
            public const string Liked = "Liked";
            public const string Commented = "Commented";
            public const string Verified = "Verified";
        }

        public const int SightingCategoryPlant = 2;
        public const int SightingCategoryAnimal = 3;
        public const int SightingCategoryOthers = 4;

        //public const int DefaultPageSize = 20;
        public const int AllPageSize = 100;
        public const int AdminListSize = 10000;
        public const int HomePageSize = 50;

        /*
        public static List<string> CategoryList = new List<String> { "Amphibian", "Arachnid / Insect", "Bird",
                "Coral", "Crustacean", "Fish", "Mammal", "Mollusc", "Other Animals", "Reptile", "Algae",
                "All Other Taxa", "Fungi", "Lichen", "Aquatic Plants", "Creepers / Climbers", "Cycads",
                "Epiphytes", "Ferns & Fern Allies", "Hemiepiphytes", "Herbs", "Mosses", "Other Plants",
                "Palms", "Shrubs", "Trees" };

        public static List<object> CategoryData = new List<object>
        {
            new { value = "Amphibian" , text = "Amphibian"},
            new { value = "Arachnid / Insect" , text = "Arachnid / Insect"},
            new { value = "Bird" , text = "Bird"},
            new { value = "Coral" , text = "Coral"},
            new { value = "Crustacean" , text = "Crustacean"},
            new { value = "Fish" , text = "Fish"},
            new { value = "Mammal" , text = "Mammal"},
            new { value = "Mollusc" , text = "Mollusc"},
            new { value = "Other Animals" , text = "Other Animals"},
            new { value = "Reptile" , text = "Reptile"},
            new { value = "Algae" , text = "Algae"},
            new { value = "All Other Taxa" , text = "All Other Taxa"},
            new { value = "Fungi" , text = "Fungi"},
            new { value = "Lichen" , text = "Lichen"},
            new { value = "Aquatic Plants" , text = "Aquatic Plants"},
            new { value = "Creepers / Climbers" , text = "Creepers / Climbers"},
            new { value = "Cycads" , text = "Cycads"},
            new { value = "Epiphytes" , text = "Epiphytes"},
            new { value = "Ferns & Fern Allies" , text = "Ferns & Fern Allies"},
            new { value = "Hemiepiphytes" , text = "Hemiepiphytes"},
            new { value = "Herbs" , text = "Herbs"},
            new { value = "Mosses" , text = "Mosses"},
            new { value = "Other Plants" , text = "Other Plants"},
            new { value = "Palms" , text = "Palms"},
            new { value = "Shrubs" , text = "Shrubs"},
            new { value = "Trees" , text = "Trees"}
        };*/

    }
}
