﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Enumerations
{
    public class Survey
    {
        public enum QuestionType
        {
            TEXT=1,
            CHECKBOX,
            RADIO,
            IMAGE 
            
        }
        public enum PermissionRank
        {
            PublicUsers = 10,
            SurveyMembers = 20,
            SurveyAdmins = 30
        }

        public enum SurveyStatusRank
        {
             Rejected = 10,
            Draft = 15,
            Pending = 20,
            Approved = 30,
        }
        public enum SurveySubmittedStatusRank
        {
            Draft = 10,
            //Suspend = 15,
            Completed = 20,
            Deleted = 30,
        }
        public enum MemberRank
        {
            Rejected = 10,
            Pending = 20,
            Member = 30,
            Admin = 40
        }
        public static List<object> ViewPermissionOption = new List<object>
        {
            new { value = (int)PermissionRank.SurveyMembers , text = "Survey members" },
            new { value = (int)PermissionRank.SurveyAdmins , text = "Survey admins" },
        };

        public static List<object> DownloadPermissionOption = new List<object>
        {
            new { value = (int)PermissionRank.SurveyMembers , text = "Survey members"},
            new { value = (int)PermissionRank.SurveyAdmins , text = "Survey admins"},
        };

        public static List<object> MemberRankOption = new List<object>
        {
           new { value = (int)MemberRank.Admin , text = "Admin"},
            new { value = (int)MemberRank.Member , text = "Member"},
            new { value = (int)MemberRank.Pending , text = "Pending"},
            new { value = (int)MemberRank.Rejected , text = "Rejected"},
        };
        public static List<object> SurveyStatusRankOption = new List<object>
        {
            new { value = (int)SurveyStatusRank.Approved , text = "Approved"},
            new { value = (int)SurveyStatusRank.Pending , text = "Pending"},
           // new { value = (int)SurveyStatusRank.Suspend , text = "Suspend"},
            new { value = (int)SurveyStatusRank.Rejected , text = "Rejected"},
        };

        public static List<object> SurveyStatusRankOptionPending = new List<object>
        {
            new { value = (int)SurveyStatusRank.Approved , text = "Approved"},
            new { value = (int)SurveyStatusRank.Pending , text = "Pending"},
            new { value = (int)SurveyStatusRank.Rejected , text = "Rejected"},
        };

        public static List<object> SurveyStatusRankOptionApproved = new List<object>
        {
            new { value = (int)SurveyStatusRank.Approved , text = "Approved"},
            //new { value = (int)SurveyStatusRank.Suspend , text = "Suspend"},
        };

        public static List<object> SurveyStatusRankOptionRejected = new List<object>
        {
            new { value = (int)SurveyStatusRank.Approved , text = "Approved"},
            new { value = (int)SurveyStatusRank.Rejected , text = "Rejected"},
        };

        //public static List<object> SurveyStatusRankOptionSuspend = new List<object>
        //{
        //    new { value = (int)SurveyStatusRank.Approved , text = "Approved"},
        //    new { value = (int)SurveyStatusRank.Suspend , text = "Suspend"},
        //};
    }
}
