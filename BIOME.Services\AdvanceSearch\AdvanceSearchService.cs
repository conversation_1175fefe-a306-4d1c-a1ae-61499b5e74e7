﻿using BIOME.Models;
using BIOME.ViewModels;
using Elasticsearch.Net;
using Nest;
using Nest.JsonNetSerializer;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using Utilities.Helpers;

namespace BIOME.Services
{
    public class AdvanceSearchService : IAdvanceSearchService
    {
        private readonly string elasticsearchServer;
        private readonly IFileDownloadService fileDownloadService;
        private readonly IResourcesService resourcesService;
        private readonly ISightingsService sightingsService;
        private static ElasticClient Client { get; set; } //Commented because Elastic Search libs have been removed.

        public string ElasticsearchServer
        {
            get
            {
                return elasticsearchServer;
            }
        }

        public AdvanceSearchService(string elasticsearchServer, IFileDownloadService fileDownloadService, IResourcesService resourcesService, ISightingsService sightingsService)
        {
            this.elasticsearchServer = elasticsearchServer;
            this.fileDownloadService = fileDownloadService;
            this.resourcesService = resourcesService;
            this.sightingsService = sightingsService;
        }
        public bool CanElasticsearchServerConnect() {

            //check sighting index, 
            InitialElasticsearch(Constants.Search.Elasticsearch.SightingIndexName);
            return Client.Ping().IsValid;
        }
        public string GetElasticSearchIndices()
        {

            //check sighting index, 
            InitialElasticsearch(Constants.Search.Elasticsearch.SightingIndexName);
            var response = Client.Cat.Indices();

            if (response.IsValid)
            {
                //return response.Records.ToJson();
                return response.Records.Count.ToJson();
            }
            else
                return response.DebugInformation.ToJson();
                //return "{\"Error\":\"Unable to get ES indices\"}";

        }
        

        public List<SightingDetail> SearchSightingsList(AdvanceSearchViewModel.AdvanceQueryViewModel advanceQueryViewModel)
        {

            //return new List<SightingDetail>();
            //Commented because Elastic Search libs have been removed.

            InitialElasticsearch(Constants.Search.Elasticsearch.SightingIndexName);

            
            
            //var _result = Client.Ping();

            List<string> fields = new List<string>();

            List<QueryContainer> filterList = new List<QueryContainer>();

            //List<SightingDetail> sightingList = new List<SightingDetail>();

            List<long> sightingListID = new List<long>();

            #region "Preparing Filter"

            int filterCount = advanceQueryViewModel.SightingList.Where(s => s.IsFilterType.Equals(true)
                                        && s.Selected.Equals(true)).Count();

            //if 2 filter than both Verfied status and Non Verification status that mean no filter need for this logic
            if (filterCount < 2)
            {
                foreach (var filter in advanceQueryViewModel
                                    .SightingList
                                    .Where(s => s.IsFilterType.Equals(true)
                                        && s.Selected.Equals(true)))
                {
                    QueryContainer filterQuery = new MatchQuery
                    {
                        Field = filter.ElasticSearchMappingField,
                        Query = filter.ElasticSearchValueField
                    };

                    filterList.Add(filterQuery);
                }
            }

            //there more good solution is update in sighting detail table
            //QueryContainer hasProject = new HasChildQuery
            //{
            //    MinChildren = 0,
            //    Type = "projectSightings",

            //};

            //filterList.Add(hasProject);

            //filterList.Add(dateFilterQuery);

            #endregion "Preparing Filter"

            #region "Preparing Search Fields"

            //Initialized Fields
            foreach (var item in advanceQueryViewModel
                                    .SightingList
                                    .Where(s => s.Selected.Equals(true)))
            {
                if (item.ItemId != 0 && item.IsFilterType == false)
                {
                    fields.Add(item.ElasticSearchMappingField);
                }
            }

            if (advanceQueryViewModel.SelectedTaxonomyList != null)
            {
                foreach (var itemTaxonomyField in advanceQueryViewModel.SelectedTaxonomyList)
                {
                    if (itemTaxonomyField.Contains(","))
                    {
                        string[] allfields = itemTaxonomyField.Split(",".ToCharArray());

                        foreach (var item in allfields)
                        {
                            if (!fields.Contains(item))
                            {
                                fields.Add(item);
                            }
                        }
                    }
                    else
                    {
                        if (!fields.Contains(itemTaxonomyField))
                        {
                            fields.Add(itemTaxonomyField);
                        }
                    }
                }
            }

            //no field count then select default field count
            if (fields.Count == 0)
            {
                fields = DefaultSightingFieldList();
            }

            #endregion "Preparing Search Fields"

            #region "Preparing HighLight Fields"

            Nest.Highlight sightingHighlight = PrepareHighlight(fields);

            #endregion "Preparing HighLight Fields"

            #region "Main Query"

            //Main Query
            QueryContainer searchQueryContainer = new MultiMatchQuery
            {
                Fields = fields.ToArray(),
                Query = advanceQueryViewModel.SearchQuery
            };

            QueryContainer inappropriateFilter = new TermQuery
            {
                Field = Constants.Search.SearchFields.PublicSighting.isInappropriate,
                Value = "false"
            };

            QueryContainer dateRangeQueryContainer = new QueryContainer();

            if (advanceQueryViewModel.DateRange.Equals("optionYear"))
            {
                DateRangeQuery dateRangeQuery = new DateRangeQuery()
                {
                    Field = Constants.Search.SearchFields.PublicSighting.updateAt,
                    GreaterThanOrEqualTo = advanceQueryViewModel.SelectedStartYear,
                    LessThanOrEqualTo = advanceQueryViewModel.SelectedEndYear,
                    Format = "dd/MM/yyyy||yyyy"
                };

                dateRangeQueryContainer = dateRangeQuery;
            }

            /*QueryContainer distanceRangeQueryContainer = new QueryContainer();

            if (advanceQueryViewModel.isLocationPointSearch)
            {
                GeoDistanceQuery geoDistanceQuery = new GeoDistanceQuery
                {
                    Location = advanceQueryViewModel.Latitude.ToString() + "," + advanceQueryViewModel.Longitude.ToString(), //"1.655083,103.5861",
                    //Location = "1.400254,103.7728", //"1.655083,103.5861",
                    Field = Constants.Search.SearchFields.PublicSighting.sightingLocation,
                    Distance = Nest.Distance.Kilometers(Constants.Search.Elasticsearch.DefaultDistanceKM)
                    //Distance = Nest.Distance.Inches(10)
                };

                distanceRangeQueryContainer = geoDistanceQuery;
            }

            if (advanceQueryViewModel.isLocationPloygonSearch)
            {
              
                List<GeoLocation> geoLocationList = new List<GeoLocation>();

                foreach (var item in advanceQueryViewModel.PolygonPoints)
                {
                    GeoLocation geoLocation = new GeoLocation(item.Latitude, item.Longitude);
                    geoLocationList.Add(geoLocation);
                }

                GeoPolygonQuery geoPolygonQuery = new GeoPolygonQuery
                {
                    //Coerce = true,
                    Points = geoLocationList,
                    Field = Constants.Search.SearchFields.PublicSighting.sightingLocation
                };

                distanceRangeQueryContainer = geoPolygonQuery;
            }*/

            QueryContainer shapeQueryContainer = new QueryContainer();

            if (advanceQueryViewModel.isLocationPointSearch)
            {
                GeoShapeQuery geoShapePointQuery = new GeoShapeQuery
                {
                    //Location = advanceQueryViewModel.Latitude.ToString() + "," + advanceQueryViewModel.Longitude.ToString(), //"1.655083,103.5861",
                    Field = Constants.Search.SearchFields.PublicSighting.sightingLocation,
                    Shape = new PointGeoShape(new GeoCoordinate(advanceQueryViewModel.Latitude, advanceQueryViewModel.Longitude)),
                    Relation = GeoShapeRelation.Intersects
                };

                shapeQueryContainer = geoShapePointQuery;

            }

            if (advanceQueryViewModel.isLocationPloygonSearch)
            {

                List<List<GeoCoordinate>> geoCoordinates = new List<List<GeoCoordinate>>();


                List<GeoCoordinate> innerCoordinate = new List<GeoCoordinate>();
                foreach (var item in advanceQueryViewModel.PolygonPoints)
                {
                    GeoCoordinate geoLocation = new GeoCoordinate(item.Latitude, item.Longitude);
                    innerCoordinate.Add(geoLocation);
                }

                geoCoordinates.Add(innerCoordinate);

                GeoShapeQuery geoShapePolygonQuery = new GeoShapeQuery
                {
                    //Location = advanceQueryViewModel.Latitude.ToString() + "," + advanceQueryViewModel.Longitude.ToString(), //"1.655083,103.5861",
                    Field = Constants.Search.SearchFields.PublicSighting.sightingLocation,
                    Shape = new PolygonGeoShape(geoCoordinates),
                    Relation = GeoShapeRelation.Intersects
                };

                shapeQueryContainer = geoShapePolygonQuery;


            }

            //for public sighting filter, !projectSightingFilter
            QueryContainer projectSightingFilter = new ExistsQuery
            {
                Field = "projectSightings",
            };

            List<IHit<SightingsViewModel.SightingsES>> sightingResults = new List<IHit<SightingsViewModel.SightingsES>>();

            try
            {
                SearchRequest<SightingsViewModel.SightingsES> sightingSearchRequest = new SearchRequest<SightingsViewModel.SightingsES>(Constants.Search.Elasticsearch.SightingIndexName)
                {
                    Query = new BoolQuery
                    {
                        Filter = filterList.ToArray(),  //new QueryContainer[] { filterList.ToList() },
                        Must = new QueryContainer[] { searchQueryContainer, dateRangeQueryContainer, shapeQueryContainer, inappropriateFilter, !projectSightingFilter }
                        
                        //Must = new QueryContainer[] { searchQueryContainer, dateRangeQueryContainer, distanceRangeQueryContainer, inappropriateFilter, !projectSightingFilter }
                    },
                    Highlight = advanceQueryViewModel.isLocationPloygonSearch || advanceQueryViewModel.isLocationPointSearch ? null : sightingHighlight,
                    Size = Constants.Search.Elasticsearch.DefaultSearchCount
                };

                var result = Client.Search<SightingsViewModel.SightingsES>(sightingSearchRequest);

                sightingResults = result.HitsMetadata.Hits.ToList();
            }
            catch (Exception ex)
            {
                sightingResults = new List<IHit<SightingsViewModel.SightingsES>>();
            }

            #endregion "Main Query"

            #region "Merge Highlight Text On Result"

            foreach (var item in sightingResults)
            {
                foreach (var highLight in item.Highlight)
                {
                    string highlightString = "";

                    switch (highLight.Key)
                    {
                        case Constants.Search.SearchFields.PublicSighting.description:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.Description = highlightString;
                            break;

                        case Constants.Search.SearchFields.PublicSighting.categoryName:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.CategoryName = highlightString;
                            break;

                        case Constants.Search.SearchFields.PublicSighting.commonName:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.CommonName = highlightString;
                            break;

                        case Constants.Search.SearchFields.PublicSighting.scientificName:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.ScientificName = highlightString;
                            break;

                        case Constants.Search.SearchFields.PublicSighting.comment:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                foreach (var itemComment in item.Source.SightingComments)
                                {
                                    if (itemComment.CommentString.Equals(itemHighLightString.Replace(Constants.Search.Highlight.preTag, "").Replace(Constants.Search.Highlight.postTag, "")))
                                    {
                                        itemComment.CommentString = itemHighLightString;
                                        break;
                                    }
                                }
                            }
                            break;

                        case Constants.Search.SearchFields.PublicSighting.taxonomyScientificName:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                foreach (var itemTaxonomy in item.Source.SightingTaxonomy)
                                {
                                    foreach (var itemTaxonomyClassification in itemTaxonomy.SightingTaxonomyClassifications)
                                    {
                                        if (itemTaxonomyClassification.ScientificName.Equals(itemHighLightString.Replace(Constants.Search.Highlight.preTag, "").Replace(Constants.Search.Highlight.postTag, "")))
                                        {
                                            itemTaxonomyClassification.ScientificName = itemHighLightString;
                                        }
                                    }
                                }
                            }
                            break;

                        default:
                            break;
                    }
                }
                if (item.Source != null)
                {
                    /*  SightingDetail sighting = sightingsService.GetSightingDetails(item.Source.Id);
                      if (sighting != null && sighting.StatusRank != (int)BIOME.Enumerations.Sighting.StatusRank.Draft)
                      {
                          sightingList.Add(sighting);
                      }*/

                    sightingListID.Add(item.Source.Id);

                    
                }
            }

            return sightingsService.GetSightingDetailsList(sightingListID.ToArray());

            #endregion "Merge Highlight Text On Result"

            //return sightingList;

        }

        public IEnumerable<SightingDetail> SearchSightingsList(string queryString)
        {
            //return new List<SightingDetail>();
            //Commented because Elastic Search libs have been removed.
            InitialElasticsearch(Constants.Search.Elasticsearch.SightingIndexName);

            SearchRequest<SightingDetail> sightingSearchRequest = new SearchRequest<SightingDetail>(Constants.Search.Elasticsearch.SightingIndexName)
            {
                Query = new MultiMatchQuery
                {
                    Fields = DefaultSightingFieldList().ToArray(),
                    Query = queryString
                },
                Size = 20
            };

            var result = Client.Search<SightingDetail>(sightingSearchRequest);

            IEnumerable<SightingDetail> sightingList = result.Documents.ToList();

            return sightingList;

        }

        public List<SightingDetail> SearchProjectList(AdvanceSearchViewModel.AdvanceQueryViewModel advanceQueryViewModel)
        {
            //return new List<SightingDetail>();
            //Commented because Elastic Search libs have been removed.
            InitialElasticsearch(Constants.Search.Elasticsearch.SightingIndexName);

            List<string> fields = new List<string>();

            List<QueryContainer> filterList = new List<QueryContainer>();

            List<SightingDetail> projectList = new List<SightingDetail>();

            #region "Preparing Filter"

            int filterCount = advanceQueryViewModel.ProjectList.Where(s => s.IsFilterType.Equals(true)
                                        && s.Selected.Equals(true)).Count();

            //if 2 filter than both Verfied status and Non Verification status that mean no filter need for this logic
            if (filterCount < 2)
            {
                foreach (var filter in advanceQueryViewModel
                                    .ProjectList
                                    .Where(s => s.IsFilterType.Equals(true)
                                        && s.Selected.Equals(true)))
                {
                    QueryContainer filterQuery = new MatchQuery
                    {
                        Field = filter.ElasticSearchMappingField,
                        Query = filter.ElasticSearchValueField
                    };

                    filterList.Add(filterQuery);
                }
            }

            //QueryContainer hasProject = new HasChildQuery
            //{
            //    MinChildren = 1,
            //    Type = "projectSightings"

            //};

            //filterList.Add(hasProject);

            #endregion "Preparing Filter"

            #region "Preparing Search Fields"

            //Initialized Fields
            foreach (var item in advanceQueryViewModel
                                    .ProjectList
                                    .Where(s => s.Selected.Equals(true)))
            {
                if (item.ItemId != 0 && item.IsFilterType == false)
                {
                    fields.Add(item.ElasticSearchMappingField);
                }
            }

            if (advanceQueryViewModel.SelectedProjectTaxonomyList != null)
            {
                foreach (var itemTaxonomyField in advanceQueryViewModel.SelectedProjectTaxonomyList)
                {
                    if (itemTaxonomyField.Contains(","))
                    {
                        string[] allfields = itemTaxonomyField.Split(",".ToCharArray());

                        foreach (var item in allfields)
                        {
                            if (!fields.Contains(item))
                            {
                                fields.Add(item);
                            }
                        }
                    }
                    else
                    {
                        if (!fields.Contains(itemTaxonomyField))
                        {
                            fields.Add(itemTaxonomyField);
                        }
                    }
                }
            }

            //no field count then select default field count
            if (fields.Count == 0)
            {
                fields = DefaultProjectFieldList();
            }

            #endregion "Preparing Search Fields"

            #region "Preparing HighLight Fields"

            Nest.Highlight projectHighlight = PrepareHighlight(fields);

            #endregion "Preparing HighLight Fields"

            #region "Main Query"

            //Main Query
            QueryContainer searchQueryContainer = new MultiMatchQuery
            {
                Fields = fields.ToArray(),
                Query = advanceQueryViewModel.SearchQuery
            };

            QueryContainer inappropriateFilter = new TermQuery
            {
                Field = Constants.Search.SearchFields.PublicSighting.isInappropriate,
                Value = "false"
            };

            //Date Range Query
            QueryContainer dateRangeQueryContainer = new QueryContainer();

            if (advanceQueryViewModel.DateRange.Equals("optionYear"))
            {
                DateRangeQuery dateRangeQuery = new DateRangeQuery()
                {
                    Field = Constants.Search.SearchFields.Project.updatedAt,
                    GreaterThanOrEqualTo = advanceQueryViewModel.SelectedStartYear,
                    LessThanOrEqualTo = advanceQueryViewModel.SelectedEndYear,
                    Format = "dd/MM/yyyy||yyyy"
                };

                dateRangeQueryContainer = dateRangeQuery;
            }

            QueryContainer distanceRangeQueryContainer = new QueryContainer();

            if (advanceQueryViewModel.isLocationPointSearch)
            {
                GeoDistanceQuery geoDistanceQuery = new GeoDistanceQuery
                {
                    Location = advanceQueryViewModel.Latitude.ToString() + "," + advanceQueryViewModel.Longitude.ToString(), //"1.655083,103.5861",
                    Field = Constants.Search.SearchFields.PublicSighting.sightingLocation,
                    Distance = Nest.Distance.Kilometers(Constants.Search.Elasticsearch.DefaultDistanceKM)
                };

                distanceRangeQueryContainer = geoDistanceQuery;
            }

            if (advanceQueryViewModel.isLocationPloygonSearch)
            {
                #region "geoshapePolygonQuery"

                //IPolygonGeoShape geoShapePolygon = new PolygonGeoShape();

                //List<List<GeoCoordinate>> pointListList = new List<List<GeoCoordinate>>();
                //foreach (var item in advanceQueryViewModel.PolygonPoints)
                //{
                //    IEnumerable<GeoCoordinate> geoCoordinate;
                //    List<GeoCoordinate> pointList = new List<GeoCoordinate>();
                //    GeoCoordinate point = new GeoCoordinate(item.Latitude,item.Longitude);
                //    pointList.Add(point);
                //    pointListList.Add(pointList);
                //    geoCoordinate = pointList;
                //}

                //geoShapePolygon.Coordinates = pointListList;

                //GeoShapePolygonQuery geoShapePolygonQuery = new GeoShapePolygonQuery
                //{
                //    Field = Constants.Search.SearchFields.PublicSighting.sightingLocation,
                //    Shape = geoShapePolygon
                //};

                //distanceRangeQueryContainer = geoShapePolygonQuery;

                #endregion "geoshapePolygonQuery"

                List<GeoLocation> geoLocationList = new List<GeoLocation>();

                foreach (var item in advanceQueryViewModel.PolygonPoints)
                {
                    GeoLocation geoLocation = new GeoLocation(item.Latitude, item.Longitude);
                    geoLocationList.Add(geoLocation);
                }

                GeoPolygonQuery geoPolygonQuery = new GeoPolygonQuery
                {
                    //Coerce = true,
                    Points = geoLocationList,
                    Field = Constants.Search.SearchFields.PublicSighting.sightingLocation
                };

                distanceRangeQueryContainer = geoPolygonQuery;
            }

            QueryContainer projectSightingFilter = new ExistsQuery
            {
                Field = "projectSightings",
            };

            List<IHit<SightingDetail>> projectResults = new List<IHit<SightingDetail>>();

            try
            {
                SearchRequest<SightingDetail> projectSearchRequest = new SearchRequest<SightingDetail>(Constants.Search.Elasticsearch.SightingIndexName)
                {
                    Query = new BoolQuery
                    {
                        Filter = filterList.ToArray(),  //new QueryContainer[] { filterList.ToList() },
                        Must = new QueryContainer[] { searchQueryContainer, dateRangeQueryContainer, distanceRangeQueryContainer, inappropriateFilter, projectSightingFilter }
                    },
                    Highlight = advanceQueryViewModel.isLocationPloygonSearch || advanceQueryViewModel.isLocationPointSearch ? null : projectHighlight,
                    Size = Constants.Search.Elasticsearch.DefaultSearchCount,
                };

                var result = Client.Search<SightingDetail>(projectSearchRequest);

                projectResults = result.HitsMetadata.Hits.ToList();
            }
            catch (Exception ex)
            {
                projectResults = new List<IHit<SightingDetail>>();
            }

            #endregion "Main Query"

            #region "Merge Highlight Text On Result"

            foreach (var item in projectResults)
            {
                foreach (var highLight in item.Highlight)
                {
                    string highlightString = "";

                    switch (highLight.Key)
                    {
                        case Constants.Search.SearchFields.Project.description:

                            foreach (var itemHighLightString in highLight.Value)
                            {
                                foreach (var itemProjectSighting in item.Source.ProjectSightings)
                                {
                                    if (itemProjectSighting.Project.Description.Equals(itemHighLightString.Replace(Constants.Search.Highlight.preTag, "").Replace(Constants.Search.Highlight.postTag, "")))
                                    {
                                        itemProjectSighting.Project.Description = itemHighLightString;
                                        break;
                                    }
                                }
                            }

                            break;

                        case Constants.Search.SearchFields.Project.title:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                foreach (var itemProjectSighting in item.Source.ProjectSightings)
                                {
                                    if (itemProjectSighting.Project.Title.Equals(itemHighLightString.Replace(Constants.Search.Highlight.preTag, "").Replace(Constants.Search.Highlight.postTag, "")))
                                    {
                                        itemProjectSighting.Project.Title = itemHighLightString;
                                        break;
                                    }
                                }
                            }
                            break;

                        default:
                            break;
                    }
                }

                projectList.Add(item.Source);
            }

            #endregion "Merge Highlight Text On Result"

            return projectList;
            
            
        }

        public List<ResourceDocument> SearchResourceDocumentList(AdvanceSearchViewModel.AdvanceQueryViewModel advanceQueryViewModel, long groupId, bool skipGroupFilter)
        {
            //return new List<ResourceDocument>();
            //Commented because Elastic Search libs have been removed.
            InitialElasticsearch(Constants.Search.Elasticsearch.ResourceIndexName);

            List<string> fields = new List<string>();

            List<QueryContainer> filterList = new List<QueryContainer>();

            List<ResourceDocument> resourceDocumentList = new List<ResourceDocument>();
            List<long> resourceDocumentListIDs = new List<long>();

            #region "Preparing Search Fields"

            //Initialized Fields
            foreach (var item in advanceQueryViewModel
                                    .ResourceList
                                    .Where(s => s.Selected.Equals(true)))
            {
                if (item.ItemId != 0 && item.IsFilterType == false)
                {
                    fields.Add(item.ElasticSearchMappingField);
                }
            }

            //no field count then select default field count
            if (fields.Count == 0)
            {
                fields = DefaultResourceDocumentFieldList();
            }

            #endregion "Preparing Search Fields"

            #region "Preparing HighLight Fields"

            Nest.Highlight resourceHighlight = PrepareHighlight(fields);

            #endregion "Preparing HighLight Fields"

            #region "Main Query"

            //Main Query
            QueryContainer searchQueryContainer = new MultiMatchQuery
            {
                Fields = fields.ToArray(),
                Query = advanceQueryViewModel.SearchQuery
            };

            //QueryContainer filterInappropriate = new TermQuery
            //{
            //    Field = Constants.Search.SearchFields.PublicSighting.isInappropriate,
            //    Value = "false"
            //};

            QueryContainer dateRangeQueryContainer = new QueryContainer();

            if (advanceQueryViewModel.DateRange.Equals("optionYear"))
            {
                DateRangeQuery dateRangeQuery = new DateRangeQuery()
                {
                    Field = Constants.Search.SearchFields.Resource.updatedAt,
                    GreaterThanOrEqualTo = advanceQueryViewModel.SelectedStartYear,
                    LessThanOrEqualTo = advanceQueryViewModel.SelectedEndYear,
                    Format = "dd/MM/yyyy||yyyy"
                };

                dateRangeQueryContainer = dateRangeQuery;
            }

            /*QueryContainer distanceRangeQueryContainer = new QueryContainer();

            if (advanceQueryViewModel.isLocationPointSearch)
            {
                GeoDistanceQuery geoDistanceQuery = new GeoDistanceQuery
                {
                    Location = advanceQueryViewModel.Latitude.ToString() + "," + advanceQueryViewModel.Longitude.ToString(), //"1.655083,103.5861",
                    Field = Constants.Search.SearchFields.Resource.locationField,
                    Distance = Nest.Distance.Kilometers(Constants.Search.Elasticsearch.DefaultDistanceKM)
                };

                distanceRangeQueryContainer = geoDistanceQuery;
            }

            if (advanceQueryViewModel.isLocationPloygonSearch)
            {
                List<GeoLocation> geoLocationList = new List<GeoLocation>();

                foreach (var item in advanceQueryViewModel.PolygonPoints)
                {
                    GeoLocation geoLocation = new GeoLocation(item.Latitude, item.Longitude);
                    geoLocationList.Add(geoLocation);
                }

                GeoPolygonQuery geoPolygonQuery = new GeoPolygonQuery
                {
                    //Coerce = true,
                    Points = geoLocationList,
                    Field = Constants.Search.SearchFields.Resource.locationField
                };

                distanceRangeQueryContainer = geoPolygonQuery;
            }*/

            QueryContainer shapeQueryContainer = new QueryContainer();

            if (advanceQueryViewModel.isLocationPointSearch)
            {
                GeoShapeQuery geoShapePointQuery = new GeoShapeQuery
                {
                    //Location = advanceQueryViewModel.Latitude.ToString() + "," + advanceQueryViewModel.Longitude.ToString(), //"1.655083,103.5861",
                    Field = Constants.Search.SearchFields.Resource.locationField,
                    Shape = new PointGeoShape(new GeoCoordinate(advanceQueryViewModel.Latitude, advanceQueryViewModel.Longitude)),
                    Relation = GeoShapeRelation.Intersects
                };

                shapeQueryContainer = geoShapePointQuery;

            }

            if (advanceQueryViewModel.isLocationPloygonSearch)
            {
                

                List<List<GeoCoordinate>> geoCoordinates = new List<List<GeoCoordinate>>();


                List<GeoCoordinate> innerCoordinate = new List<GeoCoordinate>();
                foreach (var item in advanceQueryViewModel.PolygonPoints)
                {
                    GeoCoordinate geoLocation = new GeoCoordinate(item.Latitude, item.Longitude);
                    innerCoordinate.Add(geoLocation);
                }

                geoCoordinates.Add(innerCoordinate);

                GeoShapeQuery geoShapePolygonQuery = new GeoShapeQuery
                {
                    //Location = advanceQueryViewModel.Latitude.ToString() + "," + advanceQueryViewModel.Longitude.ToString(), //"1.655083,103.5861",
                    Field = Constants.Search.SearchFields.Resource.locationField,
                    Shape = new PolygonGeoShape(geoCoordinates),
                    Relation = GeoShapeRelation.Intersects
                };

                shapeQueryContainer = geoShapePolygonQuery;


            }


            List<IHit<ResourceDocument>> resourceDocumentResults = new List<IHit<ResourceDocument>>();
            List<string> excudeList = new List<string>();
            excudeList.Add(Constants.Search.SearchFields.Resource.file);
            try
            {
                SearchRequest<ResourceDocument> resourceDocumentSearchRequest = new SearchRequest<ResourceDocument>(Constants.Search.Elasticsearch.ResourceIndexName)
                {
                    Query = new BoolQuery
                    {
                        Filter = filterList.ToArray(),  //new QueryContainer[] { filterList.ToList() },
                        Must = new QueryContainer[] { searchQueryContainer, dateRangeQueryContainer, shapeQueryContainer }
                    },
                    Highlight = advanceQueryViewModel.isLocationPloygonSearch || advanceQueryViewModel.isLocationPointSearch ? null : resourceHighlight,
                    Size = Constants.Search.Elasticsearch.DefaultSearchCount,
                    //Source = new SourceFilter
                    //{
                    //    Exclude = excudeList.ToArray()
                    //}
                };

                var result = Client.Search<ResourceDocument>(resourceDocumentSearchRequest);

                resourceDocumentResults = result.HitsMetadata.Hits.ToList();
            }
            catch (Exception ex)
            {
                resourceDocumentResults = new List<IHit<ResourceDocument>>();
            }

            #region "Merge Highlight Text On Result"

            foreach (var item in resourceDocumentResults)
            {
               /* #region "Generate Token"

                FileDownloadViewModel fileDownloadVM = new FileDownloadViewModel();
                FileDownloadViewModel viewReportVM = new FileDownloadViewModel();
                string fileDownloadAccessRight = resourcesService.GetGroupForResourceDocumentDownload(item.Source.Id);
                string viewReportAccessRight = resourcesService.GetGroupForResourceDocumentViewReport(item.Source.Id);

                fileDownloadVM.AccessRight = fileDownloadAccessRight;
                fileDownloadVM.ContentType = item.Source.ContentType;
                fileDownloadVM.DownloadFileInfo = item.Source.ServerFileName;
                fileDownloadVM.ExpiredDate = DateTime.Now.AddDays(1);
                fileDownloadVM.FileName = item.Source.fileName;
                fileDownloadVM.FileType = BIOME.Enumerations.FileDownload.FileType.Resource;

                viewReportVM.AccessRight = viewReportAccessRight;
                viewReportVM.ContentType = item.Source.ContentType;
                viewReportVM.DownloadFileInfo = item.Source.ServerFileName;
                viewReportVM.ExpiredDate = DateTime.Now.AddDays(1);
                viewReportVM.FileName = item.Source.fileName;
                viewReportVM.FileType = BIOME.Enumerations.FileDownload.FileType.Resource;

                string token = fileDownloadService.GenerateFileDownloadToken(fileDownloadVM);
                string viewReportToken = fileDownloadService.GenerateFileDownloadToken(viewReportVM);

                item.Source.FileToken = token;
                item.Source.ViewReportToken = viewReportToken;
                item.Source.IsPreview = false;

                #endregion "Generate Token"*/

                foreach (var highLight in item.Highlight)
                {
                    string highlightString = "";

                    switch (highLight.Key)
                    {
                        case Constants.Search.SearchFields.Resource.fileName:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.fileName = highlightString;
                            break;

                        case Constants.Search.SearchFields.Resource.author:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;

                                foreach (var itemAuthor in item.Source.AtResourceMetaData.ResourceMetaDataAuthors)
                                {
                                    if (itemAuthor.Name.Equals(itemHighLightString.Replace(Constants.Search.Highlight.preTag, "").Replace(Constants.Search.Highlight.postTag, "")))
                                    {
                                        itemAuthor.Name = itemHighLightString;
                                        break;
                                    }
                                }
                            }
                            break;

                        case Constants.Search.SearchFields.Resource.title:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.AtResourceMetaData.Title = highlightString;
                            break;

                        case Constants.Search.SearchFields.Resource.description:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.AtResourceMetaData.Description = highlightString;
                            break;

                        case Constants.Search.SearchFields.Resource.organization:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.AtResourceMetaData.Organization = highlightString;
                            break;

                        default:
                            break;
                    }
                }

                resourceDocumentListIDs.Add(item.Source.Id);
                /*if (skipGroupFilter)
                {
                  *//*  ResourceDocument tempDoc = resourcesService.GetResourceDocument(item.Source.Id);
                    if (tempDoc != null && tempDoc.IsDeleted == false && tempDoc.AtResourceMetaData != null && tempDoc.AtResourceMetaData.IsDeleted == false)
                    {
                        tempDoc.FileToken = item.Source.FileToken;
                        resourceDocumentList.Add(tempDoc);
                    }*//*

                }
                else
                {
                    *//*if (resourcesService.IsSearchableResource(item.Source.Id, groupId))
                    {
                        //resourceDocumentList.Add(item.Source);
                        ResourceDocument tempDoc = resourcesService.GetResourceDocument(item.Source.Id);
                        if (tempDoc != null && tempDoc.IsDeleted == false && tempDoc.AtResourceMetaData != null && tempDoc.AtResourceMetaData.IsDeleted == false)
                        {
                            tempDoc.FileToken = item.Source.FileToken;
                            resourceDocumentList.Add(tempDoc);
                        }
                    }*//*
                }*/
            }

            if (resourceDocumentListIDs.Count > 0) {
                /*if (skipGroupFilter)
                {
                    resourceDocumentList = resourcesService.GetResourceDocumentForAdvanceSearch(groupId, resourceDocumentListIDs.Distinct().ToArray());
                }
                else
                {
                    resourceDocumentList = resourcesService.GetResourceDocumentForAdvanceSearch(groupId, resourceDocumentListIDs.Distinct().ToArray());
                }*/
                resourceDocumentList = resourcesService.GetResourceDocumentForAdvanceSearch(groupId, resourceDocumentListIDs.Distinct().ToArray());

                foreach (ResourceDocument doc in resourceDocumentList) {

                    #region "Generate Token"

                    FileDownloadViewModel fileDownloadVM = new FileDownloadViewModel();
                    FileDownloadViewModel viewReportVM = new FileDownloadViewModel();
                    //string fileDownloadAccessRight = resourcesService.GetGroupForResourceDocumentDownload(item.Source.Id);
                    //string viewReportAccessRight = resourcesService.GetGroupForResourceDocumentViewReport(item.Source.Id);

                    var result = doc.ResourceFilePermissions.Where(b => b.IsAllowDownload.Equals(true) && b.AtGroup != null).Select(b => b.AtGroup.Id.ToString()).ToList();
                    string fileDownloadAccessRight = string.Join(",", result);
                    fileDownloadVM.AccessRight = fileDownloadAccessRight;
                    fileDownloadVM.ContentType = doc.ContentType;
                    fileDownloadVM.DownloadFileInfo = doc.ServerFileName;
                    fileDownloadVM.ExpiredDate = DateTime.Now.AddDays(1);
                    fileDownloadVM.FileName = doc.fileName;
                    fileDownloadVM.FileType = BIOME.Enumerations.FileDownload.FileType.Resource;
                    string token = fileDownloadService.GenerateFileDownloadToken(fileDownloadVM);
                    doc.FileToken = token;


                    if (groupId == -1)  //System Admin
                    {
                        if (doc.IsMapIndex)
                        {
                            doc.IsViewReport = true;
                        }
                    }
                    else 
                    {
                        if (doc.IsMapIndex)
                        {
                            doc.IsViewReport = doc.ResourceFilePermissions.Where(b => b.AtGroup.Id == groupId && b.IsViewReport.Equals(true)).Count() > 0;
                        }
                    }
                        


                    /*  viewReportVM.AccessRight = viewReportAccessRight;
                      viewReportVM.ContentType = doc.ContentType;
                      viewReportVM.DownloadFileInfo = doc.ServerFileName;
                      viewReportVM.ExpiredDate = DateTime.Now.AddDays(1);
                      viewReportVM.FileName = doc.fileName;
                      viewReportVM.FileType = BIOME.Enumerations.FileDownload.FileType.Resource;*/
                    //string viewReportToken = fileDownloadService.GenerateFileDownloadToken(viewReportVM);
                    //item.Source.ViewReportToken = viewReportToken;
                    //item.Source.IsPreview = false;

                    #endregion "Generate Token"
                }


            }

            #endregion "Merge Highlight Text On Result"

            #endregion "Main Query"

            return resourceDocumentList;

        }

        public List<ApplicationStatusViewModel.ResearchApplicationES> SearchResearchApplicationList(AdvanceSearchViewModel.AdvanceQueryViewModel advanceQueryViewModel)
        {
            //return new List<ResearchApplication>();
            //Commented because Elastic Search libs have been removed.
            InitialElasticsearch(Constants.Search.Elasticsearch.ResearchApplicationIndexName);

            List<string> fields = new List<string>();

            List<QueryContainer> filterList = new List<QueryContainer>();

            /*List<ResearchApplication> researchApplicationList = new List<ResearchApplication>();*/

            List<ApplicationStatusViewModel.ResearchApplicationES> researchApplicationList = new List<ApplicationStatusViewModel.ResearchApplicationES>();

            #region "Prepareing Filter"

            //no filter

            #endregion "Prepareing Filter"

            #region "Preparing Search Fields"

            //Initialized Fields
            foreach (var item in advanceQueryViewModel
                                    .ResearchPermitList
                                    .Where(s => s.Selected.Equals(true)))
            {
                if (item.ItemId != 0 && item.IsFilterType == false)
                {
                    fields.Add(item.ElasticSearchMappingField);
                }
            }

            //no field count then select default field count
            if (fields.Count == 0)
            {
                fields = DefaultPermitApplicationFieldList();
            }

            #endregion "Preparing Search Fields"

            #region "Preparing HighLight Fields"

            Nest.Highlight researchApplicationHighlight = PrepareHighlight(fields);

            #endregion "Preparing HighLight Fields"

            #region "Main Query"

            //Main Query
            QueryContainer searchQueryContainer = new MultiMatchQuery
            {
                Fields = fields.ToArray(),
                Query = advanceQueryViewModel.SearchQuery
            };

            //QueryContainer filterInappropriate = new TermQuery
            //{
            //    Field = Constants.Search.SearchFields.PublicSighting.isInappropriate,
            //    Value = "false"
            //};

            QueryContainer dateRangeQueryContainer = new QueryContainer();

            if (advanceQueryViewModel.DateRange.Equals("optionYear"))
            {
                DateRangeQuery dateRangeQuery = new DateRangeQuery()
                {
                    Field = Constants.Search.SearchFields.PermitApplication.updatedAt,
                    GreaterThanOrEqualTo = advanceQueryViewModel.SelectedStartYear,
                    LessThanOrEqualTo = advanceQueryViewModel.SelectedEndYear,
                    Format = "dd/MM/yyyy||yyyy"
                };

                dateRangeQueryContainer = dateRangeQuery;
            }

            QueryContainer shapeQueryContainer = new QueryContainer();

            if (advanceQueryViewModel.isLocationPointSearch)
            {
                GeoShapeQuery geoShapePointQuery = new GeoShapeQuery
                {
                    //Location = advanceQueryViewModel.Latitude.ToString() + "," + advanceQueryViewModel.Longitude.ToString(), //"1.655083,103.5861",
                    Field = Constants.Search.SearchFields.PermitApplication.locationField,
                    Shape = new PointGeoShape(new GeoCoordinate(advanceQueryViewModel.Latitude, advanceQueryViewModel.Longitude)),
                    Relation = GeoShapeRelation.Intersects
                };

                shapeQueryContainer = geoShapePointQuery;
                
            }

            if (advanceQueryViewModel.isLocationPloygonSearch)
            {
 
                List<List<GeoCoordinate>> geoCoordinates = new List<List<GeoCoordinate>>();


                List<GeoCoordinate> innerCoordinate = new List<GeoCoordinate>();
                foreach (var item in advanceQueryViewModel.PolygonPoints)
                {
                    GeoCoordinate geoLocation = new GeoCoordinate(item.Latitude, item.Longitude);
                    innerCoordinate.Add(geoLocation);
                }

                geoCoordinates.Add(innerCoordinate);

                GeoShapeQuery geoShapePolygonQuery = new GeoShapeQuery
                {
                    //Location = advanceQueryViewModel.Latitude.ToString() + "," + advanceQueryViewModel.Longitude.ToString(), //"1.655083,103.5861",
                    Field = Constants.Search.SearchFields.PermitApplication.locationField,
                    Shape = new PolygonGeoShape(geoCoordinates),
                    Relation = GeoShapeRelation.Intersects
                };

                shapeQueryContainer = geoShapePolygonQuery;

                
            }

            List<IHit<ApplicationStatusViewModel.ResearchApplicationES>> researchApplicationResults = new List<IHit<ApplicationStatusViewModel.ResearchApplicationES>>();

            try
            {
                SearchRequest<ApplicationStatusViewModel.ResearchApplicationES> researchApplicationSearchRequest = new SearchRequest<ApplicationStatusViewModel.ResearchApplicationES>(Constants.Search.Elasticsearch.ResearchApplicationIndexName)
                {
                    Query = new BoolQuery
                    {
                        Filter = filterList.ToArray(),  //new QueryContainer[] { filterList.ToList() },
                        Must = new QueryContainer[] { searchQueryContainer, dateRangeQueryContainer, shapeQueryContainer },

                    },
                    Highlight = advanceQueryViewModel.isLocationPloygonSearch || advanceQueryViewModel.isLocationPointSearch ? null : researchApplicationHighlight,
                    Size = Constants.Search.Elasticsearch.DefaultSearchCount
                };

                var result = Client.Search<ApplicationStatusViewModel.ResearchApplicationES>(researchApplicationSearchRequest);

                researchApplicationResults = result.HitsMetadata.Hits.ToList();
            }
            catch (Exception ex)
            {
                researchApplicationResults = new List<IHit<ApplicationStatusViewModel.ResearchApplicationES>>();
            }

            #region "Merge Highlight Text On Result"

            foreach (var item in researchApplicationResults)
            {
                if (!string.IsNullOrEmpty(item.Source.IndemnityFormFileName))
                {
                    FileDownloadViewModel fileDownloadVM = new FileDownloadViewModel();
                    string accessRight = item.Source.ResearcherId.ToString();

                    fileDownloadVM.AccessRight = accessRight;
                    fileDownloadVM.ContentType = "";
                    fileDownloadVM.DownloadFileInfo = BIOME.Constants.Configuration.FilePath.Contents.Permit.IndemnityFile.ToLocalPath() + "\\" + item.Source.IndemnityFormFileName;
                    fileDownloadVM.ExpiredDate = DateTime.Now.AddDays(1);
                    fileDownloadVM.FileName = item.Source.IndemnityFormFileName;
                    fileDownloadVM.FileType = BIOME.Enumerations.FileDownload.FileType.Permit;

                    string token = fileDownloadService.GenerateFileDownloadToken(fileDownloadVM);

                    item.Source.IndemnityFormFileToken = token;
                }

                if (!string.IsNullOrEmpty(item.Source.MiscFileName))
                {
                    FileDownloadViewModel fileDownloadVM = new FileDownloadViewModel();
                    string accessRight = item.Source.ResearcherId.ToString();

                    fileDownloadVM.AccessRight = accessRight;
                    fileDownloadVM.ContentType = "";
                    fileDownloadVM.DownloadFileInfo = BIOME.Constants.Configuration.FilePath.Contents.Permit.MiscFile.ToLocalPath() + "\\" + item.Source.MiscFileName;
                    fileDownloadVM.ExpiredDate = DateTime.Now.AddDays(1);
                    fileDownloadVM.FileName = item.Source.MiscFileName;
                    fileDownloadVM.FileType = BIOME.Enumerations.FileDownload.FileType.Permit;

                    string token = fileDownloadService.GenerateFileDownloadToken(fileDownloadVM);

                    item.Source.MiscFileToken = token;
                }

                foreach (var highLight in item.Highlight)
                {
                    string highlightString = "";

                    switch (highLight.Key)
                    {
                        case Constants.Search.SearchFields.PermitApplication.permitNo:

                            
                          /*  foreach (var itemHighLightString in highLight.Value.Highlights)
                            {
                                highlightString += itemHighLightString;

                                foreach (var itemPermitApplication in item.Source.PermitApplications)
                                {
                                    if (itemPermitApplication.PermitNumber.Equals(itemHighLightString.Replace(Constants.Search.Highlight.preTag, "").Replace(Constants.Search.Highlight.postTag, "")))
                                    {
                                        itemPermitApplication.PermitNumber = itemHighLightString;
                                        break;
                                    }
                                }
                            }*/

                            break;

                        case Constants.Search.SearchFields.PermitApplication.institution:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.InstitutionName= highlightString;
                            break;

                        case Constants.Search.SearchFields.PermitApplication.teamMember:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;

                                foreach (var itemMember in item.Source.FieldSurveyTeamMembers)
                                {
                                    if (itemMember.Name.Equals(itemHighLightString.Replace(Constants.Search.Highlight.preTag, "").Replace(Constants.Search.Highlight.postTag, "")))
                                    {
                                        itemMember.Name = itemHighLightString;
                                        break;
                                    }
                                }
                            }

                            break;

                        case Constants.Search.SearchFields.PermitApplication.researchTitle:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.Title = highlightString;
                            break;

                        case Constants.Search.SearchFields.PermitApplication.purpose:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.Purpose = highlightString;
                            break;

                        case Constants.Search.SearchFields.PermitApplication.typeOfResearch:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.ResearchTypeName= highlightString;

                            break;

                        case Constants.Search.SearchFields.PermitApplication.habitat:
                            foreach (var itemHighLightString in highLight.Value)
                            {
                                highlightString += itemHighLightString;
                            }
                            item.Source.HabitatName = highlightString;
                            break;

                        default:
                            break;
                    }
                }

                researchApplicationList.Add(item.Source);
            }

            #endregion "Merge Highlight Text On Result"

            #endregion "Main Query"

            return researchApplicationList;

        }

        #region "Private Functions"

        //private void InitialElasticsearch(string index)
        //{
        //Commented because Elastic Search libs have been removed.
        /*try
        {
            var connectionPool = new SingleNodeConnectionPool(new Uri(!string.IsNullOrEmpty(elasticsearchServer) ? elasticsearchServer : Constants.Search.Elasticsearch.ServerURL));


            var settings = new ConnectionSettings(connectionPool, connectionSettings => new MyJsonNetSerializer(connectionSettings))
                .DefaultIndex(index)
                .DisableDirectStreaming()
                .PrettyJson();

            Client = new ElasticClient(settings);
        }
        catch (Exception ex)
        {
        }*/

        // }

        private void InitialElasticsearch(string index)
        {
            try
            {
                //var connectionPool = new SingleNodeConnectionPool(new Uri(!string.IsNullOrEmpty(elasticsearchServer) ? elasticsearchServer : Constants.Search.Elasticsearch.ServerURL));
                var connectionPool = new SingleNodeConnectionPool(new Uri(elasticsearchServer));
                /*var settings = new ConnectionSettings(connectionPool, sourceSerializer: (s,f) => new MyJsonNetSerializer() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore })
                    .DefaultIndex(defalultIndex)
                    .DisableDirectStreaming()
                    .PrettyJson();*/

                var settings = new ConnectionSettings(connectionPool, sourceSerializer: (builtin, setting) => new JsonNetSerializer(
                   builtin, setting,
                   () => new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore }))
                    .DefaultIndex(index)
                    .DisableDirectStreaming()
                    .PrettyJson();
                
                settings.BasicAuthentication(System.Configuration.ConfigurationManager.AppSettings[Constants.Configuration.Server.AppSettings.ElasticsearchUserName],
                    System.Configuration.ConfigurationManager.AppSettings[Constants.Configuration.Server.AppSettings.ElasticsearchPassword]);

                //settings.ServerCertificateValidationCallback(object(), new System.Security.Cryptography.X509Certificates.X509Certificate(), System.Security.Cryptography.X509Certificates.X509Chain.Create()
                  //  , System.Net.Security.SslPolicyErrors.None, true);

                /*settings.ServerCertificateValidationCallback(CertificateValidations.AuthorityIsRoot(new System.Security.Cryptography.X509Certificates.X509Certificate(this.ClusterConfiguration.FileSystem.CaCertificate))
        );*/
                
                settings.ServerCertificateValidationCallback(CertificateValidations.AllowAll);

                Client = new ElasticClient(settings);
                

            }
            catch { 
            }

        }

        private List<string> DefaultSightingFieldList()
        {
            List<string> sightingFields = new List<string>();

            sightingFields.Add(Constants.Search.SearchFields.PublicSighting.description);
            sightingFields.Add(Constants.Search.SearchFields.PublicSighting.categoryName);
            sightingFields.Add(Constants.Search.SearchFields.PublicSighting.commonName);
            sightingFields.Add(Constants.Search.SearchFields.PublicSighting.scientificName);
            sightingFields.Add(Constants.Search.SearchFields.PublicSighting.taxonomyScientificName);
            sightingFields.Add(Constants.Search.SearchFields.PublicSighting.comment);
            sightingFields.Add(Constants.Search.SearchFields.PublicSighting.owner);

            return sightingFields;
        }

        private List<string> DefaultProjectFieldList()
        {
            List<string> projectField = new List<string>();
            projectField.Add(Constants.Search.SearchFields.Project.description);
            projectField.Add(Constants.Search.SearchFields.Project.title);
            projectField.Add(Constants.Search.SearchFields.Project.member);
            return projectField;
        }

        private List<string> DefaultPermitApplicationFieldList()
        {
            List<string> permitApplicationField = new List<string>();
            permitApplicationField.Add(Constants.Search.SearchFields.PermitApplication.permitNo);
            permitApplicationField.Add(Constants.Search.SearchFields.PermitApplication.institution);
            permitApplicationField.Add(Constants.Search.SearchFields.PermitApplication.teamMember);
            permitApplicationField.Add(Constants.Search.SearchFields.PermitApplication.researchTitle);
            permitApplicationField.Add(Constants.Search.SearchFields.PermitApplication.purpose);
            permitApplicationField.Add(Constants.Search.SearchFields.PermitApplication.typeOfResearch);
            permitApplicationField.Add(Constants.Search.SearchFields.PermitApplication.habitat);
            return permitApplicationField;
        }

        private List<string> DefaultResourceDocumentFieldList()
        {
            List<string> resourceDocumentField = new List<string>();
            resourceDocumentField.Add(Constants.Search.SearchFields.Resource.fileName);
            resourceDocumentField.Add(Constants.Search.SearchFields.Resource.title);
            resourceDocumentField.Add(Constants.Search.SearchFields.Resource.author);
            resourceDocumentField.Add(Constants.Search.SearchFields.Resource.organization);
            resourceDocumentField.Add(Constants.Search.SearchFields.Resource.description);
            resourceDocumentField.Add(Constants.Search.SearchFields.Resource.file);
            return resourceDocumentField;
        }

        //Commented because Elastic Search libs have been removed.
        private Nest.Highlight PrepareHighlight(List<string> fields)
        {
            Dictionary<Field, IHighlightField> highlightField = new Dictionary<Field, IHighlightField>();
            foreach (var fieldItem in fields)
            {
                if (!highlightField.Keys.Contains(fieldItem))
                {
                    highlightField.Add(fieldItem, new HighlightField { Type = HighlighterType.Plain });
                }
            }

            Nest.Highlight highLight = new Nest.Highlight();
            highLight.PreTags = new[] { Constants.Search.Highlight.preTag };
            highLight.PostTags = new[] { Constants.Search.Highlight.postTag };
            highLight.Fields = highlightField;

            return highLight;
        }

        #endregion "Private Functions"
    }
}