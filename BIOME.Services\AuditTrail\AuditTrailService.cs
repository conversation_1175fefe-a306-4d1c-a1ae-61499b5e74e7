using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BIOME.Models;
using BIOME.ViewModels;
using System.Web;
using System.Data.Entity.SqlServer;
using MvcPaging;

namespace BIOME.Services
{
    public class AuditTrailService : ServiceBase, IAuditTrailService
    {
        #region Constructors
        private readonly ApplicationDbContext dbContext;
        private readonly log4net.ILog logger = log4net.LogManager.GetLogger(typeof(AuditTrailService));
        //private readonly UserService userService;

        public AuditTrailService(ApplicationDbContext dbContext)
        {
            this.dbContext = dbContext;
        }
        #endregion

        #region Public Methods

        public string GetActionName(int actionId)
        {
            string actionName = "";
            switch (actionId)
            {
                case (int)BIOME.Enumerations.Audit.Action.Login:
                    actionName = "Login";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.Logout:
                    actionName = "Logout";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.AccessResourcesUnsuccessful:
                    actionName = "Unsuccessful attempts to access resources";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.AccessViolationsRequests:
                    actionName = "Access violations from local and remote requests";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.SystemStartUpShutDown:
                    actionName = "System start-up and shut down";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.SystemBackupRecovery:
                    actionName = "System backup and recovery";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.SecurityProfileChanges:
                    actionName = "Security profile changes";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.SystemMaintenance:
                    actionName = "System maintenance activities";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.Transactions:
                    actionName = "CMS Transactions";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.EditDocumentType:
                    actionName = "Edit Document Types";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.AddNewStructuredDataTemplate:
                    actionName = "Add New Structured Data Template";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.EditStructuredDataTemplate:
                    actionName = "Edit Structured Data Templates";
                    break;
                case (int)BIOME.Enumerations.Audit.Action.AddResourceDocument:
                    actionName = "Add Resource Document";
                    break;
                default:
                    break;
            }
            return actionName;
        }

        public bool LogAuditTrail(long userId, string IP, int actionId, string module, string eventstring)
        {
            return LogAuditTrail(userId, IP, actionId, module, eventstring, false);
        }
        public bool LogAuditTrail(long userId, string IP, int actionId, string module, string eventstring, bool fromAPI)
        {
            try
            {
                AuditTrailLogging auditTrailLogging = new AuditTrailLogging();
                auditTrailLogging.ActionId = actionId;
                auditTrailLogging.ActionName = GetActionName(actionId);
                auditTrailLogging.UserId = userId;
                if (userId > 0)
                {
                    ApplicationUser applicationUser = dbContext.Users.Find(userId);
                    auditTrailLogging.UserEmail = applicationUser.Email;
                    //auditTrailLogging.FirstName = applicationUser.FirstName;
                    //auditTrailLogging.LastName = applicationUser.LastName;
                    auditTrailLogging.PersonName = applicationUser.PersonName;
                }
                else
                {
                    auditTrailLogging.UserEmail = "";
                    //auditTrailLogging.FirstName = "";
                    //auditTrailLogging.LastName = "";
                    auditTrailLogging.PersonName = "";
                }
                auditTrailLogging.Module = module;
                auditTrailLogging.Event = eventstring;
                auditTrailLogging.IP = IP;

                var siteType = System.Configuration.ConfigurationManager.AppSettings["SiteType"];
                if (fromAPI)
                    siteType = "API, " + siteType;
                auditTrailLogging.SiteType = siteType;
                dbContext.AuditTrailLoggings.Add(auditTrailLogging);
                bool ret = false;
                if (dbContext.SaveChanges() > 0) ret = true;
                return ret;
            }
            catch (Exception e)
            {
                return false;
            }
        }

        public bool LogAuditTrail(long userId, string Module, string Action, string AdditionalInfo)
        {
            
            return LogAuditTrail(userId, Module, Action, AdditionalInfo, false);
        }

        public bool LogAuditTrail(long userId, string Module, string Action, string AdditionalInfo, bool fromAPI)
        {
            try
            {
                AuditTrailLogging auditTrailLogging = new AuditTrailLogging();
                auditTrailLogging.ActionName = Action;
                auditTrailLogging.UserId = userId;
                if (userId > 0)
                {
                    ApplicationUser applicationUser = dbContext.Users.Find(userId);
                    auditTrailLogging.UserEmail = applicationUser.Email;
                    //auditTrailLogging.FirstName = applicationUser.FirstName;
                    //auditTrailLogging.LastName = applicationUser.LastName;
                    auditTrailLogging.PersonName = applicationUser.PersonName;
                }
                else
                {
                    auditTrailLogging.UserEmail = "";
                    //auditTrailLogging.FirstName = "";
                    //auditTrailLogging.LastName = "";
                    auditTrailLogging.PersonName = "";
                }



                auditTrailLogging.Module = Module;
                auditTrailLogging.Event = AdditionalInfo;
                auditTrailLogging.IP = GetIP(true);
                var siteType = System.Configuration.ConfigurationManager.AppSettings["SiteType"];
                if (fromAPI)
                    siteType = "API, " + siteType;
                auditTrailLogging.SiteType = siteType;
                dbContext.AuditTrailLoggings.Add(auditTrailLogging);
                bool ret = false;
                if (dbContext.SaveChanges() > 0) ret = true;
                return ret;
            }
            catch (Exception e)
            {
                return false;
            }
        }

        public bool LogAuditTrail(string Module, string Action, string AdditionalInfo)
        {
            try
            {
                AuditTrailLogging auditTrailLogging = new AuditTrailLogging();
                auditTrailLogging.ActionName = Action;
                auditTrailLogging.UserId = 0;
                auditTrailLogging.UserEmail = "";
                auditTrailLogging.FirstName = "";
                auditTrailLogging.LastName = "";
                auditTrailLogging.PersonName = "";
                auditTrailLogging.Module = Module;
                auditTrailLogging.Event = AdditionalInfo;
                auditTrailLogging.IP = "";

                dbContext.AuditTrailLoggings.Add(auditTrailLogging);
                bool ret = false;
                if (dbContext.SaveChanges() > 0) ret = true;
                return ret;
            }
            catch (Exception e)
            {
                return false;
            }
        }

        public IPagedList<AuditTrailLogging> GetListAuditTrail(int actionid, string Email, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo, string ipAddress
            , int currentIndexPage, int pageSize) //Fix for NPARKS-107
        {
            try
            {
                logger.Info($"GetListAuditTrail - START: actionid={actionid}, Email='{Email}', tPeriodFrom={tPeriodFrom}, tPeriodTo={tPeriodTo}, ipAddress='{ipAddress}', currentIndexPage={currentIndexPage}, pageSize={pageSize}");

                // Start building the query
                logger.Debug($"GetListAuditTrail - Building base query with actionid={actionid}");
                var query = dbContext.AuditTrailLoggings.AsNoTracking()
                            .Where(b => b.ActionId == actionid
                                        && b.CreatedAt >= tPeriodFrom
                                        && b.CreatedAt <= tPeriodTo);

                // Apply filters based on optional parameters
                if (!string.IsNullOrEmpty(Email))
                {
                    logger.Debug($"GetListAuditTrail - Adding email filter: '{Email}'");
                    query = query.Where(b => Email.Equals(b.UserEmail, StringComparison.InvariantCultureIgnoreCase));
                }

                if (!string.IsNullOrEmpty(ipAddress))
                {
                    logger.Debug($"GetListAuditTrail - Adding IP address filter: '{ipAddress}'");
                    query = query.Where(b => ipAddress.Equals(b.IP, StringComparison.InvariantCultureIgnoreCase));
                }

                // Order and paginate the results
                logger.Debug($"GetListAuditTrail - Ordering and paginating results");
                query = query.OrderByDescending(b => b.Id);

                logger.Debug($"GetListAuditTrail - Executing ToPagedList with currentIndexPage={currentIndexPage}, pageSize={pageSize}");
                var result = query.ToPagedList(currentIndexPage, pageSize);

                logger.Info($"GetListAuditTrail - SUCCESS: Retrieved {result.Count} items, TotalItemCount={result.TotalItemCount}, PageCount={result.PageCount}");
                return result;
            }
            catch (System.Data.Entity.Core.EntityException ex)
            {
                logger.Error($"GetListAuditTrail - Database Entity Error: {ex.Message}");
                logger.Error($"GetListAuditTrail - Entity Error Details: {ex.InnerException?.Message}");
                throw new Exception($"Database connection error in GetListAuditTrail: {ex.Message}", ex);
            }
            catch (System.Data.SqlClient.SqlException ex)
            {
                logger.Error($"GetListAuditTrail - SQL Error: {ex.Message}, Number: {ex.Number}, Severity: {ex.Class}, State: {ex.State}");
                throw new Exception($"SQL error in GetListAuditTrail: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                logger.Error($"GetListAuditTrail - Unexpected Error: {ex.Message}");
                logger.Error($"GetListAuditTrail - Stack Trace: {ex.StackTrace}");
                throw new Exception($"Unexpected error in GetListAuditTrail: {ex.Message}", ex);
            }
        }

        public IPagedList<AuditTrailLogging> GetAuditTrails(string module, string Email, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo, string ipAddress
            , int currentIndexPage,int pageSize) //Fix for NPARKS-107
        {
            try
            {
                logger.Info($"GetAuditTrails - START: module='{module}', Email='{Email}', tPeriodFrom={tPeriodFrom}, tPeriodTo={tPeriodTo}, ipAddress='{ipAddress}', currentIndexPage={currentIndexPage}, pageSize={pageSize}");

                logger.Debug($"GetAuditTrails - Building base query with date range");
                var query = dbContext.AuditTrailLoggings.Where(b => b.CreatedAt < tPeriodTo && b.CreatedAt > tPeriodFrom).AsQueryable();

                if (!module.Equals("--All--"))
                {
                    logger.Debug($"GetAuditTrails - Adding module filter: '{module}'");
                    query = query.Where(t => t.Module.Equals(module));
                }

                if (!string.IsNullOrEmpty(Email))
                {
                    logger.Debug($"GetAuditTrails - Adding email filter: '{Email}' (searching UserEmail, ActionName, PersonName)");
                    query = query.Where(t => t.UserEmail.Contains(Email) || t.ActionName.Contains(Email) || t.PersonName.Contains(Email));
                }

                if (!string.IsNullOrEmpty(ipAddress))
                {
                    logger.Debug($"GetAuditTrails - Adding IP address filter: '{ipAddress}'");
                    query = query.Where(t => t.IP.Equals(ipAddress));
                }

                logger.Debug($"GetAuditTrails - Executing query with ordering and pagination");
                var result = query.OrderByDescending(t => t.Id).ToPagedList(currentIndexPage, pageSize);

                logger.Info($"GetAuditTrails - SUCCESS: Retrieved {result.Count} items, TotalItemCount={result.TotalItemCount}, PageCount={result.PageCount}");
                return result;
            }
            catch (System.Data.Entity.Core.EntityException ex)
            {
                logger.Error($"GetAuditTrails - Database Entity Error: {ex.Message}");
                logger.Error($"GetAuditTrails - Entity Error Details: {ex.InnerException?.Message}");
                throw new Exception($"Database connection error in GetAuditTrails: {ex.Message}", ex);
            }
            catch (System.Data.SqlClient.SqlException ex)
            {
                logger.Error($"GetAuditTrails - SQL Error: {ex.Message}, Number: {ex.Number}, Severity: {ex.Class}, State: {ex.State}");
                throw new Exception($"SQL error in GetAuditTrails: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                logger.Error($"GetAuditTrails - Unexpected Error: {ex.Message}");
                logger.Error($"GetAuditTrails - Stack Trace: {ex.StackTrace}");
                throw new Exception($"Unexpected error in GetAuditTrails: {ex.Message}", ex);
            }
        }

        public List<AuditLog> GetListAuditLog( string Email, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo, string ModuelName)
        {
            try
            {
                logger.Info($"GetListAuditLog - START: Email='{Email}', tPeriodFrom={tPeriodFrom}, tPeriodTo={tPeriodTo}, ModuelName='{ModuelName}'");

                List<AuditLog> result;

                if (string.IsNullOrEmpty(Email) & string.IsNullOrEmpty(ModuelName))
                {
                    logger.Debug($"GetListAuditLog - No filters applied, getting all audit logs in date range");
                    var query = (from b in dbContext.AuditLogs
                                 where   b.CreatedAt < tPeriodTo && b.CreatedAt > tPeriodFrom
                                 orderby b.CreatedAt descending
                                 select b).ToList();

                    result = query;
                }
                else if (!string.IsNullOrEmpty(Email) & string.IsNullOrEmpty(ModuelName))
                {
                    logger.Debug($"GetListAuditLog - Filtering by Email only: '{Email}'");
                    var query = (from b in dbContext.AuditLogs
                                 join u in dbContext.Users on b.UserID equals u.Id
                                 where  b.CreatedAt < tPeriodTo && b.CreatedAt > tPeriodFrom
                                 && Email.Equals(u.Email, StringComparison.InvariantCultureIgnoreCase)
                                 orderby b.CreatedAt descending
                                 select b).ToList();

                    result = query;
                }
                else if (string.IsNullOrEmpty(Email) & !string.IsNullOrEmpty(ModuelName))
                {
                    logger.Debug($"GetListAuditLog - Filtering by ModuleName only: '{ModuelName}'");
                    var query = (from b in dbContext.AuditLogs
                                 join u in dbContext.Users on b.UserID equals u.Id
                                 where b.CreatedAt < tPeriodTo && b.CreatedAt > tPeriodFrom
                                 && b.TableName.Contains(ModuelName)// ModuelName.Contains(b.TableName, StringComparison.InvariantCultureIgnoreCase)
                                 orderby b.CreatedAt descending
                                 select b).ToList();

                    result = query;
                }
                else
                {
                    logger.Debug($"GetListAuditLog - Filtering by both Email '{Email}' and ModuleName '{ModuelName}'");
                    var query = (from b in dbContext.AuditLogs
                                 join u in dbContext.Users on b.UserID equals u.Id
                                 where  b.CreatedAt < tPeriodTo && b.CreatedAt > tPeriodFrom
                                 &&   b.TableName.Contains(ModuelName)
                                 && Email.Equals(u.Email, StringComparison.InvariantCultureIgnoreCase)
                                 orderby b.CreatedAt descending
                                 select b).ToList();

                    result = query;
                }

                logger.Info($"GetListAuditLog - SUCCESS: Retrieved {result?.Count ?? 0} audit log records");
                return result ?? new List<AuditLog>();
            }
            catch (System.Data.Entity.Core.EntityException ex)
            {
                logger.Error($"GetListAuditLog - Database Entity Error: {ex.Message}");
                logger.Error($"GetListAuditLog - Entity Error Details: {ex.InnerException?.Message}");
                throw new Exception($"Database connection error in GetListAuditLog: {ex.Message}", ex);
            }
            catch (System.Data.SqlClient.SqlException ex)
            {
                logger.Error($"GetListAuditLog - SQL Error: {ex.Message}, Number: {ex.Number}, Severity: {ex.Class}, State: {ex.State}");
                throw new Exception($"SQL error in GetListAuditLog: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                logger.Error($"GetListAuditLog - Unexpected Error: {ex.Message}");
                logger.Error($"GetListAuditLog - Stack Trace: {ex.StackTrace}");
                throw new Exception($"Unexpected error in GetListAuditLog: {ex.Message}", ex);
            }
        }


        public string GetIP(bool CheckForward = false)
        {
            string ip = null;
            if (CheckForward)
            {
                ip = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            }

            if (string.IsNullOrEmpty(ip))
            {
                ip = HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];
            }
            else
            { // Using X-Forwarded-For last address
                /*ip = ip.Split(',')
                       .Last()
                       .Trim();*/
                //Use client IP. Issue ID: NPARKS-41
                ip = ip.Split(',')
                       .First()
                       .Trim();
            }

            return ip;
        }

        #endregion

        

        #region For Admin Report

        //RegisterFrom: -1: from mobile or web, 0: from mobile, 1: from web
        public int GetNewUsersNumber(int RegisterFrom, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo)
        {
            int query;
            if (RegisterFrom < 0)
            {
                query = (from b in dbContext.Users
                         where b.CreatedAt < tPeriodTo && b.CreatedAt > tPeriodFrom
                         select b).Count();
            }
            else if (RegisterFrom == 0)
            {
                query = (from b in dbContext.Users
                         where b.CreatedAt < tPeriodTo && b.CreatedAt > tPeriodFrom && b.IsMobileRegistered
                         select b).Count();
            }
            else
            {
                query = (from b in dbContext.Users
                         where b.CreatedAt < tPeriodTo && b.CreatedAt > tPeriodFrom && !b.IsMobileRegistered
                         select b).Count();
            }

            return query;
            
        }

        public int GetAllUsersNumber()
        {
            var query = (from b in dbContext.Users
                         select b).Count();

            return query;

        }

        public int GetLoginNumber(string keyword, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo)
        {
            if (string.IsNullOrEmpty(keyword))
            {
                //var query = (from b in dbContext.AuditTrailLoggings
                //             where b.ActionId == (int)BIOME.Enumerations.Audit.Action.Login && b.CreatedAt < tPeriodTo && b.CreatedAt > tPeriodFrom
                //             select b.ActionId).Count();
                var query = dbContext.AuditTrailLoggings.Count(b => b.ActionId == (int)BIOME.Enumerations.Audit.Action.Login
                            && b.CreatedAt < tPeriodTo
                            && b.CreatedAt > tPeriodFrom);
                return query;
            }
            else
            {
                //var query = (from b in dbContext.AuditTrailLoggings
                //             where b.ActionId == (int)BIOME.Enumerations.Audit.Action.Login && b.CreatedAt < tPeriodTo && b.CreatedAt > tPeriodFrom && b.Event.Contains(keyword)
                //             select b.ActionId).Count();

                var query = dbContext.AuditTrailLoggings.Count(b => b.ActionId == (int)BIOME.Enumerations.Audit.Action.Login
                            && b.CreatedAt < tPeriodTo
                            && b.CreatedAt > tPeriodFrom
                            && b.Event.Contains(keyword));

                return query;
            }

        }

        public Dictionary<long, int> GetUsersNumberPerGroup()
        {
            var groupUserNumber = new Dictionary<long, int>();
            try
            {
                var query = from b in dbContext.Users
                            group b by b.Group into g
                            select new { Id = g.Key.Id, Count = g.Count() };


                foreach (var item in query)
                {
                    groupUserNumber.Add(item.Id, item.Count);
                }
                return groupUserNumber;
            }
            catch (Exception e)
            {
                return groupUserNumber;
            }
        }

        public int GetGroupUserNumber(long GroupId)
        {
            var query = (from b in dbContext.Users
                         where b.Group.Id == GroupId
                         select b).Count();
            
            return query;
        }

        public List<AuditTrailLogging> GetLoginAfterOfficeHourLogs(DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo)
        {
            var query = dbContext.AuditTrailLoggings.Where(b => b.CreatedAt < tPeriodTo && b.CreatedAt >= tPeriodFrom).AsQueryable();
            int sqlDW_Sat = 7;
            int sqlDW_Sun = 1;
            query = query.Where(t => SqlFunctions.DatePart("dw", t.CreatedAt) == sqlDW_Sat || SqlFunctions.DatePart("dw", t.CreatedAt) == sqlDW_Sun || (t.CreatedAt.Hour >= 19 && t.CreatedAt.Hour < 7));
            //query = query.Where(t => t.ActionName.Equals("Login") && t.Event.Equals("Website Login Successfully"));
            query = query.Where(t => t.ActionName.Equals("Login") && t.Event.Contains("Website Login Successfully"));
            return query.OrderBy(t=>t.CreatedAt).ToList();
        }

        public List<AuditTrailLogging> GetUnsuccessfulLoginLogs(DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo)
        {
            var query = dbContext.AuditTrailLoggings.Where(b => b.CreatedAt < tPeriodTo && b.CreatedAt >= tPeriodFrom).AsQueryable();
            query = query.Where(t => t.ActionName.Equals("Login") && t.Event.Contains("Login Unsuccessfully"));
            return query.OrderBy(t => t.CreatedAt).ToList();
        }

        public List<AuditTrailLogging> GetIncorrectOTPLogs(DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo)
        {
            var query = dbContext.AuditTrailLoggings.Where(b => b.CreatedAt < tPeriodTo && b.CreatedAt >= tPeriodFrom).AsQueryable();
            query = query.Where(t => t.ActionName.Equals("Login") && t.Event.Contains("OTP Login Unsuccessfully"));
            return query.OrderBy(t => t.CreatedAt).ToList();
        }

        public List<AuditTrailLogging> GetAdminActionLogs(List<string> userEmails, DateTimeOffset tPeriodFrom, DateTimeOffset tPeriodTo)
        {
            var query = dbContext.AuditTrailLoggings.Where(b => userEmails.Contains(b.UserEmail) &&  b.CreatedAt < tPeriodTo && b.CreatedAt >= tPeriodFrom).AsQueryable();
            return query.OrderBy(t => t.CreatedAt).ToList();
        }

        public List<AuditLogAction> GetAuditLogActions()
        {
            var query = dbContext.AuditLogActions.Where(b => b.IsForAdminReport == true).AsQueryable();
            return query.ToList();
        }

        public string ConstructConCurrentAlertMessageFor1stLogin_BIOME(string source, System.Uri contactUsLink)
        {
            var contactUsUrl = "";
            if (contactUsLink != null)
            {
                contactUsUrl = $"<a href=\"{contactUsLink.PathAndQuery}\">\"Contact Us\"</a>";
            }
            return TokenService.messageForFirstLogin_BIOME_template.Replace("<source_pf>", source).Replace("<contact us>", contactUsUrl);
        }

        #endregion

        #region Private Methods

        #endregion
    }
}
