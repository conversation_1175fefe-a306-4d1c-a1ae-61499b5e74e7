﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ErrorCode_1000" xml:space="preserve">
    <value>Error occured.</value>
    <comment>Error occured</comment>
  </data>
  <data name="ErrorCode_1001" xml:space="preserve">
    <value>Invalid login. Please try again. If you are a new user, please register for an account.</value>
    <comment>Login failure</comment>
  </data>
  <data name="ErrorCode_1002" xml:space="preserve">
    <value>Password expired.</value>
    <comment>Password expired</comment>
  </data>
  <data name="ErrorCode_1003" xml:space="preserve">
    <value>Account has expired. Please check your email to reactivate your account.</value>
    <comment>Account expired</comment>
  </data>
  <data name="ErrorCode_1004" xml:space="preserve">
    <value>Email '{0}' is invalid.</value>
    <comment>Email invalid</comment>
  </data>
  <data name="ErrorCode_1005" xml:space="preserve">
    <value>Email '{0}' is already taken.</value>
    <comment>Email taken</comment>
  </data>
  <data name="ErrorCode_1006" xml:space="preserve">
    <value>User name {0} is already taken.</value>
    <comment>Username taken</comment>
  </data>
  <data name="ErrorCode_1007" xml:space="preserve">
    <value>User name {0} is invalid, can only contain letters or digits.</value>
    <comment>Username invalid</comment>
  </data>
  <data name="ErrorCode_1008" xml:space="preserve">
    <value>Email is required.</value>
    <comment>Email entry required</comment>
  </data>
  <data name="ErrorCode_1009" xml:space="preserve">
    <value>Password is required.</value>
    <comment>Password entry required</comment>
  </data>
  <data name="ErrorCode_1010" xml:space="preserve">
    <value>The Password must have at least 1 uppercase letter, 1 lowercase letter, 1 number and 1 special character as shown here "@$!%*?&amp;" and must have at least 12 characters</value>
    <comment>Password fail security enforcement strength</comment>
  </data>
  <data name="ErrorCode_1011" xml:space="preserve">
    <value>Incorrect password.</value>
    <comment>Incorrect password</comment>
  </data>
  <data name="ErrorCode_1012" xml:space="preserve">
    <value>Passwords must have at least one digit ('0'-'9').</value>
    <comment>Password missing digit</comment>
  </data>
  <data name="ErrorCode_1013" xml:space="preserve">
    <value>Passwords must have at least one lowercase ('a'-'z').</value>
    <comment>Password missing lowercase</comment>
  </data>
  <data name="ErrorCode_1014" xml:space="preserve">
    <value>Passwords must have at least one non letter or digit character.</value>
    <comment>Password missing non letter or digit</comment>
  </data>
  <data name="ErrorCode_1015" xml:space="preserve">
    <value>Passwords must have at least one uppercase ('A'-'Z').</value>
    <comment>Password missing uppercase</comment>
  </data>
  <data name="ErrorCode_1016" xml:space="preserve">
    <value>Passwords must be at least 12 characters.</value>
    <comment>Password too short</comment>
  </data>
  <data name="ErrorCode_1017" xml:space="preserve">
    <value>User already in role.</value>
    <comment>User already in role</comment>
  </data>
  <data name="ErrorCode_1018" xml:space="preserve">
    <value>Role {0} is already taken.</value>
    <comment>Role taken</comment>
  </data>
  <data name="ErrorCode_1019" xml:space="preserve">
    <value>{0} cannot be null or empty.</value>
    <comment>Role name missing</comment>
  </data>
  <data name="ErrorCode_1020" xml:space="preserve">
    <value>First Name is required.</value>
    <comment>First name required</comment>
  </data>
  <data name="ErrorCode_1021" xml:space="preserve">
    <value>Last Name is required.</value>
    <comment>Last Name required</comment>
  </data>
  <data name="ErrorCode_1022" xml:space="preserve">
    <value>{0} must be agreed before proceeding.</value>
    <comment>Must agree T&amp;C</comment>
  </data>
  <data name="ErrorCode_1023" xml:space="preserve">
    <value>If your account registration is successful, you will receive an activation email with further instructions. If your account registration is not successful, your email address may have already been registered. Kindly proceed to login with your email and password.</value>
    <comment>Register success</comment>
  </data>
  <data name="ErrorCode_1024" xml:space="preserve">
    <value>Account has been reactivated.</value>
    <comment>Account reactivated</comment>
  </data>
  <data name="ErrorCode_1025" xml:space="preserve">
    <value>Password has been used within the previous {0} times. Please choose another password that has not been used previously.</value>
    <comment>Password reused.</comment>
  </data>
  <data name="ErrorCode_1026" xml:space="preserve">
    <value>If your email address is registered in our system, you will receive an email with instructions on how to reset your password. Do check your spam/junk folder as well.</value>
    <comment>Reset password sent</comment>
  </data>
  <data name="ErrorCode_1027" xml:space="preserve">
    <value>Email Address or SOE ID is required.</value>
    <comment>Email or SOE ID required</comment>
  </data>
  <data name="ErrorCode_1028" xml:space="preserve">
    <value>{0}' and '{1}' cannot be the same.</value>
    <comment>Old and New password same.</comment>
  </data>
  <data name="ErrorCode_1029" xml:space="preserve">
    <value>Change password successfully. For security reasons, you are required to login again.</value>
    <comment>Change password success</comment>
  </data>
  <data name="ErrorCode_1030" xml:space="preserve">
    <value>Account already activated.</value>
    <comment>Account activated</comment>
  </data>
  <data name="ErrorCode_1031" xml:space="preserve">
    <value>If your email address is registered in our system, you will receive an email with instructions on how to activate your account. Do check your spam/junk folder as well.</value>
    <comment>Activation sent</comment>
  </data>
  <data name="ErrorCode_1032" xml:space="preserve">
    <value>Activation successful.</value>
    <comment>Activation success</comment>
  </data>
  <data name="ErrorCode_1033" xml:space="preserve">
    <value>This link had expired.</value>
    <comment>Reset password session expired.</comment>
  </data>
  <data name="ErrorCode_1034" xml:space="preserve">
    <value>Password enter limit reached. Please reset your password.</value>
    <comment>Password enter cannot try more than 5 times</comment>
  </data>
  <data name="ErrorCode_1035" xml:space="preserve">
    <value>Invalid Login OTP. Please try again.</value>
    <comment>OTP Login failure</comment>
  </data>
  <data name="ErrorCode_1036" xml:space="preserve">
    <value>Email address and/or password is not correct.</value>
    <comment>Email address and/or password is not correct.</comment>
  </data>
  <data name="ErrorCode_1101" xml:space="preserve">
    <value>Cannot find user '{0}'.</value>
    <comment>User not found</comment>
  </data>
  <data name="ErrorCode_1102" xml:space="preserve">
    <value>Invalid token.</value>
    <comment>Invalid token</comment>
  </data>
  <data name="ErrorCode_1103" xml:space="preserve">
    <value>Unable to activate user '{0}'</value>
    <comment>Activate user failed</comment>
  </data>
  <data name="ErrorCode_1104" xml:space="preserve">
    <value>Activate user failed, token expired</value>
    <comment>Activate user failed, token expired</comment>
  </data>
  <data name="ErrorCode_1106" xml:space="preserve">
    <value>External logged in. Needs Create account.</value>
    <comment>External logged in. No existing account. [Not for displaying message]</comment>
  </data>
  <data name="ErrorCode_1107" xml:space="preserve">
    <value>Failed to link facebook to existing account '{0}'.</value>
    <comment>Fail to link facebook to account</comment>
  </data>
  <data name="ErrorCode_1108" xml:space="preserve">
    <value>Account is not activated. Please request for a new email to activate your account here - https://biome.nparks.gov.sg/Account/ResendActivation</value>
    <comment>Account not activated</comment>
  </data>
  <data name="ErrorCode_1109" xml:space="preserve">
    <value>Sorry, currently you do not have any roles assigned in BIOME. <NAME_EMAIL> for assistance.</value>
    <comment>Account without role</comment>
  </data>
  <data name="ErrorCode_1201" xml:space="preserve">
    <value>Cannot follow self.</value>
    <comment>Cannot follow self</comment>
  </data>
  <data name="ErrorCode_3101" xml:space="preserve">
    <value>Failed to update accepted terms and condition. Please try again.</value>
    <comment>Permit tnc failed</comment>
  </data>
  <data name="ErrorCode_9000" xml:space="preserve">
    <value>Updated successfully.</value>
    <comment>Any kinds of update success</comment>
  </data>
  <data name="ErrorCode_9001" xml:space="preserve">
    <value>No change made.</value>
    <comment>No changes</comment>
  </data>
  <data name="ErrorCode_9002" xml:space="preserve">
    <value>NParks Logo failed to update.</value>
    <comment>NParks logo update fail</comment>
  </data>
  <data name="ErrorCode_9003" xml:space="preserve">
    <value>Goverment Logo failed to update.</value>
    <comment>Gov logo updated fail</comment>
  </data>
  <data name="ErrorCode_9004" xml:space="preserve">
    <value>Title Logo failed to update.</value>
    <comment>Title logo updated fail</comment>
  </data>
  <data name="ErrorCode_9005" xml:space="preserve">
    <value>SGBioAtlas Row information failed to update.</value>
    <comment>SGBioAtlas Row info updated fail</comment>
  </data>
  <data name="ErrorCode_9006" xml:space="preserve">
    <value>Footer failed to update.</value>
    <comment>Footer updated fail</comment>
  </data>
  <data name="ErrorCode_9007" xml:space="preserve">
    <value>Background Image failed to update.</value>
    <comment>Background image updated fail</comment>
  </data>
  <data name="ErrorCode_9008" xml:space="preserve">
    <value>Fail to add new highlight.</value>
    <comment>Highlight add fail</comment>
  </data>
  <data name="ErrorCode_9009" xml:space="preserve">
    <value>Highlight failed to update.</value>
    <comment>Highlight update fail</comment>
  </data>
  <data name="ErrorCode_9010" xml:space="preserve">
    <value>Highlight not found.</value>
    <comment>Highlight not found</comment>
  </data>
  <data name="ErrorCode_9011" xml:space="preserve">
    <value>Failed to update system parameters.</value>
    <comment>System parameters update fail</comment>
  </data>
  <data name="ErrorCode_9100" xml:space="preserve">
    <value>Url format invalid. (URL should starts with http:// or https://)</value>
    <comment>Url format invalid</comment>
  </data>
  <data name="ErrorCode_9101" xml:space="preserve">
    <value>Phone format invalid.</value>
    <comment>Phone format invalid</comment>
  </data>
  <data name="ErrorCode_9102" xml:space="preserve">
    <value>{0} and {1} does not match.</value>
    <comment>Password and Confirm Password not match</comment>
  </data>
  <data name="ErrorCode_9103" xml:space="preserve">
    <value>The file size for {0} should not exceed {1}.</value>
    <comment>File size exceed</comment>
  </data>
  <data name="ErrorCode_9104" xml:space="preserve">
    <value>Maximum {2} characters.</value>
    <comment>Character exceed</comment>
  </data>
  <data name="ErrorCode_9105" xml:space="preserve">
    <value>Please choose any of the status.</value>
    <comment>Status is required</comment>
  </data>
  <data name="ErrorCode_9106" xml:space="preserve">
    <value>Recipient is required.</value>
    <comment>Recipient is required</comment>
  </data>
  <data name="ErrorCode_9107" xml:space="preserve">
    <value>Name can only contain (lowercase character, uppercase character, comma, hyphen, dot and single quote)</value>
    <comment>Name is invalid</comment>
  </data>
  <data name="ErrorCode_9108" xml:space="preserve">
    <value>Name can not exceed {1} characters</value>
    <comment>Name character exceed</comment>
  </data>
  <data name="ErrorCode_9109" xml:space="preserve">
    <value>Please tick the reCAPTCHA (I'm not a robot) before proceeding</value>
    <comment>reCAPTCHA required</comment>
  </data>
  <data name="ErrorCode_9200" xml:space="preserve">
    <value>Unable to connect to reCAPTCHA verification server at the moment. Please try again in 5 minutes. If the problem persists, you may wish to contact &lt;a href='https://biome.nparks.gov.sg/Contact/'&gt;technical support&lt;/a&gt; for further assistance</value>
    <comment>reCAPTCHA error</comment>
  </data>
  <data name="ErrorCode_9201" xml:space="preserve">
    <value>Unable to communicate to server at the moment. Please try again.</value>
    <comment>reCAPTCHA error SGBioAtlas</comment>
  </data>
  <data name="ErrorCode_9202" xml:space="preserve">
    <value>Email address and/or Date of Birth is incorrect.</value>
    <comment>Member eSign Ack error</comment>
  </data>
  <data name="ErrorCode_9203" xml:space="preserve">
    <value>Your access has been locked after 6 tries. Please contact Permit Applicant to resend e-Acknowlegement form.</value>
    <comment>Member eSign Ack error</comment>
  </data>
  <data name="ErrorCode_9204" xml:space="preserve">
    <value>You have successfully signed e-Acknowledgement form.</value>
    <comment>Member eSign Ack success</comment>
  </data>
  <data name="ErrorCode_9205" xml:space="preserve">
    <value>The link is not available. You may contact research permit applicant or &lt;contact_us&gt; for further assistance.</value>
    <comment>Member eSign link not available</comment>
  </data>
  <data name="ErrorCode_9206" xml:space="preserve">
    <value>Email can not exceed {1} characters</value>
    <comment>Email character exceed</comment>
  </data>
  <data name="ErrorCode_9207" xml:space="preserve">
    <value>Name is required.</value>
    <comment>Name is required.</comment>
  </data>
</root>