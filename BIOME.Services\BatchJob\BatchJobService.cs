﻿using BIOME.Models;
using Elasticsearch.Net;
using Hangfire;
using Nest;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Utilities.Helpers;
using CsvHelper;
using System.Web.Http;
using AutoMapper;
using BIOME.ViewModels;
using BIOME.Constants;

using WinSCP;
using dbAutoTrack.PDFWriter;
using dbAutoTrack.PDFWriter.Graphics;
using Microsoft.Owin.Security.DataProtection;
using Microsoft.Owin.Security.OAuth;
using System.Drawing;
using System.Security.Cryptography.X509Certificates;
using dbAutoTrack.PDFWriter.Form;
using static BIOME.ViewModels.UserViewModel;
using System.Threading;
using Nest.JsonNetSerializer;
using System.Web.UI.WebControls.WebParts;
//using DocumentFormat.OpenXml.Presentation;
using System.IO.Compression;
using CsQuery.ExtensionMethods.Internal;
using Flurl;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.TaskbarClock;
using System.Security.RightsManagement;
using static BIOME.Constants.Account;
//using DocumentFormat.OpenXml.Office2010.Excel;
using System.Data.Entity;
//using DocumentFormat.OpenXml.Spreadsheet;

namespace BIOME.Services
{
    public class BatchJobService : ServiceBase, IBatchJobService
    {
        #region Fields

        //   private readonly IUserServiceNoOWIN userService;
        private readonly ISystemParametersService systemParameterService;
        private readonly IDiscussionService discussionService;
        private readonly ISightingsService sightingService;
        private readonly IResearchService researchService;
        private readonly IResourcesService resourcesService;
        private readonly IMigrationService migrationService;
        protected readonly IUserServiceNoOWIN UserService;
        protected readonly IUserService UserServices;
        protected readonly IEmailService emailService;
        protected readonly ISiteService siteService;
        private readonly IGroupService groupService;
        private readonly IAuditTrailService auditTrailService;
        private log4net.ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private readonly string elasticsearchServer;
        public string ElasticsearchServer
        {
            get
            {
                return elasticsearchServer;
            }
        }

        //For Elastic Indexer
        //private static ElasticClient Client { get; set; }
        //private static string CurrentIndexName { get; set; }

        #endregion

        #region Constructors

        public BatchJobService(ISystemParametersService systemParameterService, IDiscussionService discussionService, ISightingsService sightingService, IResearchService researchService, IResourcesService resourcesService, IMigrationService migrationService, IEmailService emailService, IUserServiceNoOWIN UserService, ISiteService siteService, IGroupService groupService, IAuditTrailService auditTrailService, /*IUserService UserServices,*/  string elasticsearchServer)
        {
            //  this.userService = userService;IUserServiceNoOWIN userService, 
            this.systemParameterService = systemParameterService;
            this.discussionService = discussionService;
            this.sightingService = sightingService;
            this.researchService = researchService;
            this.resourcesService = resourcesService;
            this.elasticsearchServer = elasticsearchServer;
            this.migrationService = migrationService;
            this.UserService = UserService;
            this.groupService = groupService;
            // this.UserServices = UserServices;
            this.emailService = emailService;
            this.siteService = siteService;
            this.auditTrailService = auditTrailService;
            //IUserService UserService, IEmailService emailService,
        }

        #endregion

        #region Public Methods

        /*[DisableConcurrentExecution(5 * 60)]
        public ServiceResult MigrateFirstLastName(int? take)
        {
            int user_count_to_be_migrated = 0;
            int user_count_migrated = 0;
            try
            {
                //Migrate First and Last Name into PersonName field.
                using(ApplicationDbContext context =new ApplicationDbContext())
                {
                    List<ApplicationUser> userToMigrate;

                    var QuserToMigrate = context.Users.Where(t => t.PersonName == null);
                    if(take.HasValue)
                        userToMigrate = QuserToMigrate.Take(take.Value).ToList();
                    else
                        userToMigrate = QuserToMigrate.ToList();

                    user_count_to_be_migrated = userToMigrate.Count;
                    if (userToMigrate.Any())
                    {
                        foreach (var user in userToMigrate)
                        {
                            var namesPart = new List<string>();
                            if (!string.IsNullOrEmpty(user.FirstName))
                            {
                                namesPart.Add(user.FirstName);
                            }
                            if (!string.IsNullOrEmpty(user.LastName))
                            {
                                namesPart.Add(user.LastName);
                            }

                            user.PersonName = string.Join(" ", namesPart);

                        }
                        user_count_migrated = context.SaveChanges();
                    }

                }

                return ServiceResult.SuccessWithOutput("user count to be migrated: " + user_count_to_be_migrated.ToString() 
                    + ",user count migrated: " + user_count_migrated.ToString());

            }
            catch(Exception ex) {

                return ServiceResult.FailWithOutput("error: " + ex.ToString());
            }
            
        }*/

        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult SyncDataToMaven(int type, int fromday,int today, int month,int year,int skip,int? take)
        {
            //ReSync missing date between 2023-12-19 and 2024-09-13

            int count_to_be_migrated = 0;
            try
            {
                DateTime startDate = new DateTime(2023, 12, 19);
                DateTime endDate = new DateTime(2024, 09, 13);

                DateTime pstartDate = new DateTime(year, month, fromday);
                DateTime pendDate = new DateTime(year, month, today);

                if (pstartDate < startDate) {
                    return ServiceResult.FailWithOutput("pstartDate < startDate " + pstartDate.ToString() + " < " + startDate.ToString());
                }

                if (pendDate > endDate)
                {
                    return ServiceResult.FailWithOutput("pendDate < endDate " + pstartDate.ToString() + " < " + startDate.ToString());
                }


                using (ApplicationDbContext context = new ApplicationDbContext())
                {

                    if (type == 1)  //Sightings
                    {
                        List<SightingDetail> dataToSync;
                        var QdataToSync = context.SightingDetails.Where(t => t.CreatedAt >= pstartDate && t.CreatedAt <= pendDate).OrderBy(t => t.CreatedAt);

                        if (take.HasValue)
                            dataToSync = QdataToSync.Skip(skip).Take(take.Value).ToList();
                        else
                            dataToSync = QdataToSync.ToList();

                        count_to_be_migrated = dataToSync.Count;

                        if (dataToSync.Any())
                        {
                            foreach (var d in dataToSync)
                            {

                                Hangfire.BackgroundJob.Enqueue(() => ArcGISHelper.SyncMissingSighting(d.Id, d.Latitude, d.Longitude));
                            }
                        }

                        return ServiceResult.SuccessWithOutput("Sightings sync. count to be migrated: " + count_to_be_migrated.ToString());

                    }
                    else if (type == 2)  //Permit
                    {
                        List<ResearchPermitApplication> dataToSync;
                        var QdataToSync = context.ResearchPermitApplications.Where(t => t.CreatedAt >= pstartDate && t.CreatedAt <= pendDate).OrderBy(t => t.CreatedAt);

                        if (take.HasValue)
                            dataToSync = QdataToSync.Skip(skip).Take(take.Value).ToList();
                        else
                            dataToSync = QdataToSync.ToList();

                        count_to_be_migrated = dataToSync.Count;

                        if (dataToSync.Any())
                        {
                            foreach (var d in dataToSync)
                            {

                                Hangfire.BackgroundJob.Enqueue(() => ArcGISHelper.UpdateResearchLocation(d.Id, d.StudyLocationsDrawn));
                            }
                        }

                        return ServiceResult.SuccessWithOutput("Permit sync. count to be migrated: " + count_to_be_migrated.ToString());
                    }
                    else if (type == 3)  //Resources
                    {
                        List<ResourceMetaData> dataToSync;
                        var QdataToSync = context.ResourceMetaDatas.Where(t => t.CreatedAt >= pstartDate && t.CreatedAt <= pendDate).OrderBy(t => t.CreatedAt);

                        if (take.HasValue)
                            dataToSync = QdataToSync.Skip(skip).Take(take.Value).ToList();
                        else
                            dataToSync = QdataToSync.ToList();

                        count_to_be_migrated = dataToSync.Count;

                        if (dataToSync.Any())
                        {
                            foreach (var metadata in dataToSync)
                            {
                                Dictionary<string, object> attr = new Dictionary<string, object>();
                                var user = context.Users.Find(metadata.UploaderId);
                                attr.Add("ResourceMetaDataId", metadata.Id);
                                attr.Add("ResourceDescription", metadata.Description);
                                attr.Add("Title", metadata.Title);
                                attr.Add("PeriodofStudyFrom", metadata.PreiodOfStudyFrom.ToString());
                                attr.Add("PeriodofStudyTo", metadata.PeriodOfStudyTo.ToString());
                                attr.Add("CreatedAt", metadata.CreatedAt.ToString());
                                attr.Add("UpdatedAt", metadata.UpdatedAt.ToString());
                                attr.Add("Organization", metadata.Organization);
                                attr.Add("UploaderName", user.PersonName);
                                attr.Add("AuthorName", String.Join(", ", metadata.ResourceMetaDataAuthors.Select(au => au.Name)));
                                attr.Add("DataCategories", String.Join(", ",
                                    metadata.Categories.Select(cat => cat.CategoryName).Concat(
                                        metadata.EnvironmentCategories.Select(cat => cat.Name)).Concat(
                                        metadata.HabitatCategories.Select(cat => cat.Name)).Concat(
                                        metadata.OtherCategories.Select(cat => cat.Name))));
                                attr.Add("ResourceID", 0);


                                if (metadata.LocationType.Equals(Enumerations.ResourceLocationTypes.Multiple, StringComparison.OrdinalIgnoreCase))
                                {
                                    Hangfire.BackgroundJob.Enqueue(() => ArcGISHelper.UpdateResourceMetaData_FeatureJson_multi(metadata.Id, attr, metadata.MultipileLocationDrawn));
                                }
                                else
                                {
                                    Hangfire.BackgroundJob.Enqueue(() => ArcGISHelper.UpdateResourceMetaData(metadata.Id, attr,
                                    metadata.ResourceMetaLocations.Select(l => Tuple.Create(l.Latitude, l.Longitude)).ToList()));
                                }

                            }
                        }

                        return ServiceResult.SuccessWithOutput("Resources sync. count to be migrated: " + count_to_be_migrated.ToString());
                    }
                }

                return ServiceResult.SuccessWithOutput("Invalid sync type parameter");

            }
            catch(Exception ex) {

                return ServiceResult.FailWithOutput("error: " + ex.ToString());
            }
            
        }

        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult CleanUpAccountExpiry()
        {
            logger.Info("start CleanUpAccountExpiry");
            //var users = UserService.GetUsers();
            var users = UserService.GetUsers().Where(t => t.DateLastLogin.Year > 2000);

            var currentTime = DateTimeOffset.Now;

            //var resetAccountUrl = new Uri(Url.RouteUrl(BIOMEWebApplication.Constants.AccountControllerRoute.GetReActivateLoggedIn, null, protocol: Request.Url.Scheme));

            //var siteType = ConfigurationManager.AppSettings["SiteType"];
            string resetAccountUrl = "/Account/ReactivateLoggedIn";
            string contactUsUrl = "/Home/Contact";
            //if (!string.IsNullOrEmpty(siteType))
            //{
            //    if (siteType == "Intranet")
            //    {
            //        resetAccountUrl = ConfigurationManager.AppSettings["IntranetEmailBaseUrl"] + resetAccountUrl;

            //        contactUsUrl = ConfigurationManager.AppSettings["IntranetEmailBaseUrl"] + contactUsUrl;
            //    }
            //    else if (siteType == "Internet")
            //    {
            //        resetAccountUrl = ConfigurationManager.AppSettings["InternetEmailBaseUrl"] + resetAccountUrl;
            //        contactUsUrl = ConfigurationManager.AppSettings["InternetEmailBaseUrl"] + contactUsUrl;
            //    }
            //}

            int accountExpiryPeriod = systemParameterService.GetAccountExpiryPeriod();
            //int passwordExpiryPeriod = systemParameterService.GetPasswordExpiryPeriod();

            var dateToCheck = currentTime.AddDays(-accountExpiryPeriod);
            //List<Tuple<long, DateTimeOffset, DateTimeOffset>> usersWithDates = new List<Tuple<long, DateTimeOffset, DateTimeOffset>>();

            //if lastlogin is 0001/01/01, means no login, do no expire account
            //foreach (var userWithAccountExpired in users.Where(u => u.IsActive && u.DateLastLogin.Year>2000 &&
            foreach (var userWithAccountExpired in users.Where(u => u.IsActive &&
                (dateToCheck > u.DateLastLogin) && u.Group.Name != BIOME.Constants.Account.UserGroup.Public).ToList())
            {

                logger.Info("SetAccountExpired: " + userWithAccountExpired.Id);
                UserService.SetAccountExpired(userWithAccountExpired.Id);

                logger.Info("SendAccountExpiryFromScheduler: " + userWithAccountExpired.Id);
                //var accountExpireDate = userWithAccountExpired.DateLastLoginOrActivate.AddDays(accountExpiryPeriod);
                //var resetAccountUrl_User = resetAccountUrl + "?email=" + userWithAccountExpired.Email;
                emailService.SendAccountExpiryFromScheduler(userWithAccountExpired.PersonName, userWithAccountExpired.Email, DateTime.Now.ToString("dd/MM/yyyy HH:mm"), resetAccountUrl, contactUsUrl);
            }

            //14days advance notice

            users = UserService.GetUsers().Where(t => t.DateLastLogin.Year > 2000);
            //var date_notice_14days = currentTime.Date.AddDays(-accountExpiryPeriod).AddDays(14);
            //var date_notice_14days2 = currentTime.AddDays(-accountExpiryPeriod).AddDays(14);
            int day_notice = accountExpiryPeriod - 14;
            //var user_list_to_notify = users.Where(u => u.IsActive && u.Group.Name != BIOME.Constants.Account.UserGroup.Public && u.DateLastLogin.Year > 2000).ToList();
            var user_list_to_notify = users.Where(u => u.IsActive && u.Group.Name != BIOME.Constants.Account.UserGroup.Public).ToList();
            foreach (var user in user_list_to_notify)
            {
                //DateTime dateToCompare = user.DateLastLogin.Date.AddDays(day_notice);
                //bool test = dateToCompare == currentTime.Date;
                if (user.DateLastLogin.Date.AddDays(day_notice) == currentTime.Date)
                {
                    logger.Info("SendAccountExpiryNotification: " + user.Id);

                    var accountExpireDate = user.DateLastLoginOrActivate.AddDays(accountExpiryPeriod);
                    //var resetAccountUrl_User = resetAccountUrl + "?email=" + user.Email;
                    emailService.SendAccountExpiryNotification(user.PersonName, user.Email, accountExpireDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), resetAccountUrl, contactUsUrl);
                }
            }



            //foreach (var userWithPasswordExpired in users.Where(u => u.DateLastChangePassword > dateToCheck))
            //{

            //}

            //throw new NotImplementedException();
            return ServiceResult.Success;
        }


        [DisableConcurrentExecution(5 * 60)]

        public ServiceResult ExportUserToACE()
        {
            string Output = "";
            try
            {
                //SFTP Config
                string host = ConfigurationManager.AppSettings["SFTPHost"];
                int port = 22;
                int.TryParse(ConfigurationManager.AppSettings["SFTPPort"], out port);
                string username = ConfigurationManager.AppSettings["SFTPUsername"];
                string pwd = ConfigurationManager.AppSettings["SFTPPassword"];
                string sshHostKey = ConfigurationManager.AppSettings["SFTPsshHostKey"];
                //string sshPrivateKey = ConfigurationManager.AppSettings["SFTPsshPrivateKey"];
                //string sshPrivateKeyPwd = ConfigurationManager.AppSettings["SFTPsshPrivateKeyPass"];
                string sftpFilePath = ConfigurationManager.AppSettings["SFTPFilePathOutput"];


                if (!string.IsNullOrEmpty(host) && !string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(pwd))
                {

                    sftpFilePath += '/';
                    logger.Info(sftpFilePath);
                    try
                    {

                        SessionOptions sessionOptions = new SessionOptions
                        {
                            Protocol = Protocol.Sftp,
                            HostName = host,
                            PortNumber = port,
                            UserName = username,
                            Password = pwd,
                            
                        };

                        
                        if (!string.IsNullOrEmpty(sshHostKey))
                        {
                            sessionOptions.SshHostKeyFingerprint = sshHostKey;
                        }
                        else 
                        {

                            try
                            {

                                Output = Output + "RetrieveSFTPsshHostKey ";
                                using (Session session = new Session())
                                {

                                    // Retrieve and display the host key fingerprint
                                    SessionOptions sessionOptionsRetrieve = new SessionOptions
                                    {
                                        Protocol = Protocol.Sftp,
                                        HostName = host,
                                        PortNumber = port,

                                    };
                                    string fingerprint = session.ScanFingerprint(sessionOptionsRetrieve, "SHA-256");
                                    Output = Output + " Fingerprint: " + fingerprint;

                                }
                            }
                            catch (Exception ex) {

                                Output = Output + " Error:" + ex.Message;
                                return ServiceResult.SuccessWithOutput(Output);
                            }

                            return ServiceResult.SuccessWithOutput("Please set the sshHostKey in web config. " + Output);


                        }

                        

                        /*if (!string.IsNullOrEmpty(sshPrivateKey))
                        {
                            sessionOptions.SshPrivateKeyPath = sshPrivateKey;

                            if (!string.IsNullOrEmpty(sshPrivateKeyPwd))
                            {
                                sessionOptions.PrivateKeyPassphrase = sshPrivateKeyPwd;
                            }
                        }*/
                        
                        

                        using (Session session = new Session())
                        {
                            var str = UserService.ExportNparksUsersInfoForAudit();
                            string tempPath = Path.GetTempPath();
                            string tempPathWFile = tempPath + "BIOME_" + DateTime.Now.ToString("ddMMyyyy") + ".txt";

                            /* using (Stream file = File.OpenWrite(tempPathWFile))
                             {
                                 file.Write(str, 0, str.Length);

                             }*/

                            Encoding utf8WithoutBOM = new UTF8Encoding(false);
                            using (StreamWriter streamWriter = new StreamWriter(tempPathWFile, false, utf8WithoutBOM))
                            {
                                streamWriter.Write(str);
                            }

                            session.Open(sessionOptions);
                            TransferOptions transferOptions = new TransferOptions();
                            transferOptions.TransferMode = TransferMode.Binary;
                            transferOptions.PreserveTimestamp = false;


                            session.PutFiles(tempPathWFile, sftpFilePath + '*', false, transferOptions).Check();

                            File.Delete(tempPathWFile);
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.Info("ExportUserToACE: There is error on accessing ftp using user name:" + username + ", filepath:" + sftpFilePath + ",domain:" + host + ",port:" + port + ",Error: " + ex.Message);
                        Output = Output + "There is error on accessing ftp:" + username + ":filepath" + sftpFilePath + ":domain" + host + ",port:" + port + ",Error: " + ex.Message;
                    }
                }
                else {
                    logger.Info("ExportUserToACE: SFTP credentials is not provided in web config.");
                    Output = "ExportUserToACE: SFTP credentials is not provided in web config.";
                }
            }
            catch (Exception ex)
            {
                Output = Output + ex.Message;
            }
            return ServiceResult.SuccessWithOutput(Output);
        }
        //[DisableConcurrentExecution(5 * 60)]
        //public ServiceResult ExportUserToACE_Test()
        //{
        //    string Output = "";
        //    try
        //    {


        //        var str = UserService.ExportNparksUsersInfoForAudit();
        //        string tempPath = Path.GetTempPath();
        //        string tempPathWFile = tempPath + "User_Export_" + DateTime.Now.ToString("yyyyMMdd") + ".csv";
        //        using (Stream file = File.OpenWrite(tempPathWFile))
        //        {
        //            file.Write(str, 0, str.Length);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Output = Output + ex.Message;
        //    }
        //    return ServiceResult.SuccessWithOutput(Output);
        //}
        public ServiceResult testUpdate()
        {
            string Output = "";
            /*  
              ApplicationUser usr = UserService.GetUserbyEmail("d");
              usr.AccountStatus = BIOME.Constants.Account.AccountStatus.ACTIVE;
              var userVM = Mapper.Map<UserViewModel.DetailViewModel>(usr);
              var result = UserService.UpdateUser(userVM);*/

            return ServiceResult.SuccessWithOutput(Output);
        }

        /*[DisableConcurrentExecution(5 * 60)]
        public ServiceResult UpdateUserStatusFromACE()
        {
            string Output = "";
            StringBuilder sbErrorSummary = new StringBuilder();
            int ace_file_user_count = 0;
            NparksUserAccountStatusChangedViewModel updated_user = null;
            NparksUserACEFileViewModel not_exist_user = null;
            NparksUserACEFileViewModel failed_toupdate_user = null;

            List<NparksUserAccountStatusChangedViewModel> list_updated_user = new List<NparksUserAccountStatusChangedViewModel>();
            List<NparksUserACEFileViewModel> list_not_exist_user = new List<NparksUserACEFileViewModel>();
            List<NparksUserACEFileViewModel> list_failed_to_update_user = new List<NparksUserACEFileViewModel>();
            try
            {
                string aceConfig = ConfigurationManager.AppSettings["EdyFileServer"];
                //SFTP Config
                string host = ConfigurationManager.AppSettings["SFTPHost"];
                int port = 0;
                int.TryParse(ConfigurationManager.AppSettings["SFTPPort"],out port);
                string username = ConfigurationManager.AppSettings["SFTPUsername"];
                string pwd = ConfigurationManager.AppSettings["SFTPPassword"];
                string sshHostKey = ConfigurationManager.AppSettings["SFPsshHostKey"];
                string sshPrivateKey = ConfigurationManager.AppSettings["SFTPsshPrivateKey"];
                string sshPrivateKeyPwd = ConfigurationManager.AppSettings["SFTPsshPrivateKeyPass"];
                string sftpFilePath = ConfigurationManager.AppSettings["SFTPFilePath"];

                //UNC Config
                string user = ConfigurationManager.AppSettings["UNCUserName"];
                string domain = ConfigurationManager.AppSettings["UNCDomainName"];
                string password = ConfigurationManager.AppSettings["UNCPassword"];
                string filePath = ConfigurationManager.AppSettings["UNCFilePath"];

                string[] filePaths = null;
                string str1 = "";
                string hearder = "";
                List<string> lstStr = new List<string>();

                if (aceConfig == "unc")
                {
                    try
                    {
                        using (UNCAccessWithCredentials unc = new UNCAccessWithCredentials())
                        {
                            unc.NetUseDelete();
                            int retValue = unc.NetUseWithCredentials(filePath, user, domain, password);
                            Output = Output + "Retvalue:" + retValue.ToString();
                            if (retValue == 0)
                            {
                                logger.Info("unc login successful.");
                                filePaths = Directory.GetFiles(filePath, "*.txt");
                            }
                            else
                            {
                                logger.Info("unc login failed.");
                                filePaths = Directory.GetFiles(filePath, "*.txt");
                                logger.Info("normal login successful.");
                            }

                            filePaths = Directory.GetFiles(filePath, "*.txt");

                            foreach (string str in filePaths)
                            {
                                try
                                {
                                    using (var sr = new StreamReader(Path.Combine(filePath, str)))
                                    {
                                        var csv = new CsvReader(sr);
                                        csv.Read();
                                        hearder = csv.FieldHeaders[0];

                                        if (!lstStr.Contains(hearder))
                                            lstStr.Add(hearder);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Output = Output + ex.Message;
                                }
                            }

                            foreach (string str in filePaths)
                            {
                                try
                                {
                                    using (var sr = new StreamReader(Path.Combine(filePath, str)))
                                    {
                                        var csv = new CsvReader(sr);

                                        while (csv.Read())
                                        {

                                            try
                                            {
                                                str1 = csv.GetField<string>(0);

                                                lstStr.Add(str1);

                                            }
                                            catch (Exception ex)
                                            {
                                                Output = Output + ex.Message;
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Output = Output + ex.Message;
                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        logger.Info("There is error on accessing ftp using user name:" + user + "." + ex.Message);
                        Output = Output + "There is error on accessing ftp:" + user + ":filepath" + filePath + ":domain" + domain + ex.Message;
                    }
                } else if (aceConfig == "sftp") {
                    logger.Info(sftpFilePath);

                    string fileNameToRead = "ACE_USER_DEACT_" + DateTime.Now.ToString("yyyyMMdd") + ".txt";


                    bool fileFound = false;

#if (UAT || DEBUG)

                    //to test locally.

                    string testAcePath = "/ACE_Test";
                    string fileName = testAcePath.ToLocalPath() + "/" + fileNameToRead;
                    //string protectedFilePath = ConfigurationManager.AppSettings[BIOME.Constants.Configuration.Server.AppSettings.ProtectedFilePath];
                    string tempPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, fileName);
                    if (File.Exists(tempPath))
                    {
                        fileFound = true;
                        lstStr.AddRange(File.ReadAllLines(tempPath).ToList());
                    }

                    if (!fileFound)
                    {
                        sbErrorSummary.AppendLine("ACE file (" + fileNameToRead + ") not found");
                    }
#else
    try
                    {

                        SessionOptions sessionOptions = new SessionOptions
                        {
                            Protocol = Protocol.Sftp,
                            HostName = host,
                            PortNumber = port,
                            UserName = username,
                            Password = pwd,
                            SshHostKeyFingerprint = sshHostKey,
                            //SshPrivateKeyPath = sshPrivateKey,
                            //PrivateKeyPassphrase = sshPrivateKeyPwd
                        };

                        
                        logger.Info("ACE file to read: " + fileNameToRead);

                        using (Session session = new Session())
                        {
                            session.Open(sessionOptions);
                            TransferOptions transferOptions = new TransferOptions();
                            transferOptions.TransferMode = TransferMode.Binary;

                            RemoteDirectoryInfo directory = session.ListDirectory(sftpFilePath);

                            
                            foreach (RemoteFileInfo fileInfo in directory.Files)
                            {
                                //if (!fileInfo.IsDirectory && fileInfo.Name.EndsWith(".txt", StringComparison.OrdinalIgnoreCase))
                                if (!fileInfo.IsDirectory && fileInfo.Name.Equals(fileNameToRead, StringComparison.OrdinalIgnoreCase))
                                {
                                    logger.Info("ACE file found:" + fileNameToRead);
                                    fileFound = true;

                                    //string tempPath = Path.GetTempFileName();
                                    var tempPath = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName());      //Fixes for sonarqube finding Nov2021
                                    // Download the file to a temporary folder
                                    var sourcePath = RemotePath.EscapeFileMask(sftpFilePath + "/" + fileInfo.Name);
                                    session.GetFiles(sourcePath, tempPath).Check();

                                    lstStr.AddRange(File.ReadAllLines(tempPath).ToList());
                                    File.Delete(tempPath);
                                }
                            }
                        }

                        if (!fileFound)
                        {
                            sbErrorSummary.AppendLine("ACE file (" + fileNameToRead + ") not found");
                        }
                    }
                    catch (Exception ex)
                    {
                        //logger.Info("There is error on accessing ftp using user name:" + username + "." + ex.Message);
                        logger.Info("There is error on accessing ftp using user name:" + username + ", filepath:" + sftpFilePath + ",filename:" + fileNameToRead + ",domain:" + host + ",port:" + port + ",Error: " + ex.Message);
                        Output = Output + "There is error on accessing ftp:" + username + ":filepath" + sftpFilePath + ":domain" + host + ",port:" + port + ",Error: " + ex.Message;
                        sbErrorSummary.AppendLine("There is error on accessing ftp using user name:" + username + ", filepath:" + sftpFilePath + ",filename:" + fileNameToRead + ",domain:" + host + ",port:" + port);
                        
                        
                        
                    }
#endif



                }

                if (lstStr.Count > 0)
                    lstStr = lstStr.Where(t => !t.Trim().StartsWith("|") && !String.IsNullOrEmpty(t)).ToList();

                string[] users = null;

                if (lstStr.Count > 0)
                {

                    foreach (string st in lstStr)
                    {
                        try
                        {
                            users = st.Split('|');


                            //StringBuilder updated_list = new StringBuilder();
                            //StringBuilder not_exist_list = new StringBuilder();

                            if (users.Count() > 0)
                            {
                                ace_file_user_count += 1;
                                ApplicationUser usr = null;
                                string ace_user_Email = users[0].ToString();
                                string ace_user_AD = users[1].ToString();
                                if (!string.IsNullOrEmpty(ace_user_Email))
                                {
#if (STAGING || DEBUG)
                                    usr = UserService.GetUserbyEmail(ace_user_Email);
#else
                                    bool isEmail = false;
                                    try
                                    {
                                        var mailAddress = new System.Net.Mail.MailAddress(ace_user_Email);
                                        isEmail = true;
                                    }
                                    catch (FormatException)
                                    {
                                        isEmail = false;
                                    }

                                    if (isEmail)
                                    {
                                        usr = UserService.GetUserbyEmail(ace_user_Email);
                                    }
                                    else
                                    {
                                        usr = UserService.GetUserByADId(ace_user_Email);
                                    }
                                    if (usr == null)
                                    {
                                        usr = UserService.GetUserByADId(ace_user_AD);
                                    }
#endif
                                }

                                //usr = UserService.GetUserbyEmail(users[0].ToString());
                                if (usr == null)
                                {
                                    logger.Info("Cannot find the user with such Email:" + users[0].ToString());
                                    Output = Output + "Cannot find the user with such Email:" + users[0].ToString();

                                    not_exist_user = new NparksUserACEFileViewModel();
                                    not_exist_user.USERID = ace_user_Email;
                                    not_exist_user.SOEID = ace_user_AD;

                                    list_not_exist_user.Add(not_exist_user);
                                }
                                else
                                {
                                    bool sendFlag = false;
                                    int ACE_Status = -1; //-1: None, 0: transferred: 1: Leave

                                    DateTime? EffectiveDateOfTransfer = null;
                                    DateTime? LeavingServiceDate = null;
                                    try
                                    {
                                        if (!string.IsNullOrEmpty(users[3].ToString()) && users[3].ToString() != "00000000")
                                            LeavingServiceDate = new DateTime(Convert.ToInt32(users[3].ToString().Substring(0, 4)), Convert.ToInt32(users[3].ToString().Substring(4, 2)), Convert.ToInt32(users[3].ToString().Substring(6, 2)));
                                    }
                                    catch (Exception ex)
                                    {

                                        logger.Info("There is error on Leaving Service Date." + users[0].ToString() + ":" + ex.Message.ToString());
                                        Output = Output + "There is error on Leaving Service Date." + users[0].ToString() + ":" + ex.Message.ToString();

                                    }
                                    try
                                    {
                                        if (!string.IsNullOrEmpty(users[4].ToString()) && users[4].ToString() != "00000000")
                                            EffectiveDateOfTransfer = new DateTime(Convert.ToInt32(users[4].ToString().Substring(0, 4)), Convert.ToInt32(users[4].ToString().Substring(4, 2)), Convert.ToInt32(users[4].ToString().Substring(6, 2)));
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.Info("There is error on Effective Date Of Transfer." + users[0].ToString() + ":" + ex.Message.ToString());
                                        Output = Output + "There is error on Effective Date Of Transfer." + users[0].ToString() + ":" + ex.Message.ToString();
                                    }
                                    if (LeavingServiceDate != null)
                                    {
                                        ACE_Status = 1;
                                        
                                        usr.AccountStatus = BIOME.Constants.Account.AccountStatus.DEACTIVATED;
                                        if (usr.IsActive) {
                                            usr.IsActive = false;
                                            usr.LastSuspendDate = DateTime.Now;
                                        }
                                        
                                        logger.Error("Set User Inactive in ApiUser UserStatusUpdate1 " + usr.Id + " " + usr.PersonName);
                                        Output = Output + "Set User Inactive in ApiUser UserStatusUpdate1 " + usr.Id + " " + usr.PersonName;
                                    }
                                    *//*if (EffectiveDateOfTransfer != null)
                                    {
                                        ACE_Status = 0;
                                        usr.AccountStatus = (usr.IsActive ? BIOME.Constants.Account.AccountStatus.ACTIVE : BIOME.Constants.Account.AccountStatus.SUSPENDED);
                                        logger.Error("Set User  active in ApiUser UserStatusUpdate2 " + usr.Id + " " + usr.PersonName);
                                        Output = Output + "Set User  active in ApiUser UserStatusUpdate2 " + usr.Id + " " + usr.PersonName;
                                    }*/

                                    /*if (ACE_Status != -1)
                                    {
                                        sendFlag = true;
                                    }*//*

                                    var userVM = Mapper.Map<UserViewModel.DetailViewModel>(usr);

                                    var existingDbRoles = UserService.GetRolesForUser(usr.Id);

                                    if (EffectiveDateOfTransfer != null)
                                    {
                                        ACE_Status = 0;
                                       

                                        //usr.AccountStatus = (usr.IsActive ? BIOME.Constants.Account.AccountStatus.ACTIVE : BIOME.Constants.Account.AccountStatus.SUSPENDED);
                                        if (existingDbRoles.Any()) {
                                            bool roleRemoved = false;
                                            if (existingDbRoles.Contains(UserRoles.Public))
                                            {
                                                userVM.RolesSelected.Add(UserRoles.Public);
                                            }
                                            //roleRemoved = (existingDbRoles.Any() && userVM.RolesSelected.Count == 0);
                                            roleRemoved = userVM.RolesSelected.Count == 0;
                                            if (usr.IsActive)
                                            {

                                                if (roleRemoved)
                                                {
                                                    usr.AccountStatus = Account.AccountStatus.DEACTIVATED;
                                                    usr.LastDeActivateDate = DateTime.Now;

                                                }
                                                else
                                                {
                                                    usr.AccountStatus = BIOME.Constants.Account.AccountStatus.ACTIVE;
                                                }

                                            }
                                            else
                                            {
                                                if (roleRemoved)
                                                {
                                                    usr.AccountStatus = Account.AccountStatus.DEACTIVATED;
                                                    usr.LastDeActivateDate = DateTime.Now;

                                                }
                                                else
                                                {
                                                    usr.AccountStatus = Account.AccountStatus.SUSPENDED;
                                                }
                                            }
                                            logger.Error("EffectiveDateOfTransfer " + usr.Id + " " + usr.PersonName);
                                            Output = Output + "EffectiveDateOfTransfer " + usr.Id + " " + usr.PersonName;
                                        }
                                        

                                        // if (usr.AccountStatus == BIOME.Constants.Account.AccountStatus.ACTIVE)
                                        
                                        var UserGroup = new List<long>();
                                        // var group = groupService.GetGroupById(usr.Group.Id);
                                        if (usr.Group != null)
                                        {

                                            if (groupService.IsSubGroup(usr.Group))
                                            {

                                                var group = (SubGroup)usr.Group;
                                                UserGroup.Add(group.MainGroupId);
                                            }
                                            else if (groupService.IsSubSubGroup(usr.Group))
                                            {
                                                var group = (SubSubGroup)usr.Group;
                                                UserGroup.Add(group.ParentGroup.MainGroupId);
                                            }
                                            else
                                            {

                                                UserGroup.Add(usr.Group.Id);
                                            }
                                            userVM.UserGroupChanged = UserGroup;
                                        }
                                    }
                                    if (LeavingServiceDate != null)
                                    {
                                        usr.LastDeActivateDate = DateTime.Now;
                                        if (usr.Group != null)
                                        {
                                            var UserGroup = new List<long>();
                                            UserGroup.Add(usr.Group.Id);
                                            userVM.UserGroupChanged = UserGroup;
                                        }


                                    }
                                    //string existingRoles = string.Join(", ", UserService.GetRolesForUser(usr.Id).Select(t => t.ToProperRoleName()).ToList());

                                    if (ACE_Status != -1)
                                    {
                                        sendFlag = true;
                                    }
                                    var result = UserService.UpdateUser(userVM);

                                    if (!result.Succeeded)
                                    {
                                        logger.Info("Updating Account's Status unsuccessful");
                                        Output = Output + "Updating Account's Status unsuccessful";


                                        failed_toupdate_user = new NparksUserACEFileViewModel();
                                        failed_toupdate_user.USERID = ace_user_Email;
                                        failed_toupdate_user.SOEID = ace_user_AD;

                                        list_failed_to_update_user.Add(failed_toupdate_user);
                                    }
                                    else
                                    {
                                        logger.Info("Updating Account's Status successful. SendFlag: " + sendFlag);
                                        Output = Output + "Updating Account's Status successful";

                                        updated_user = new NparksUserAccountStatusChangedViewModel();
                                        updated_user.USERID = usr.UserName;
                                        updated_user.SOEID = ace_user_AD;
                                        updated_user.STAFFNAME = usr.PersonName;
                                        updated_user.EMAIL = usr.Email;
                                        updated_user.ACCSTATUS = usr.AccountStatus;
                                        string roleRemoved = "";
                                        if (userVM.RolesSelected != null && userVM.RolesSelected.Count() > 0)
                                        {
                                            //updated_user.CURRENT_ROLE = string.Join(",", userVM.RolesSelected.ToArray());
                                            updated_user.CURRENT_ROLE = string.Join(",", userVM.RolesSelected.Select(t=>t.ToProperRoleName()).ToArray()); //Fix for NPARK/BIOME/NCODE/2020_0194

                                            roleRemoved = string.Join(", ", existingDbRoles.Except(userVM.RolesSelected).Select(t => t.ToProperRoleName()).ToList());
                                        }
                                        else
                                        {
                                            updated_user.CURRENT_ROLE = "";
                                            roleRemoved = string.Join(", ", existingDbRoles.Select(t => t.ToProperRoleName()).ToList());
                                        }
                                        
                                        
                                        updated_user.ROLE_REMOVED = roleRemoved;
                                        updated_user.LASTSUSPENDDATE = usr.LastSuspendDate.Year > 2000 ? usr.LastSuspendDate.ToddMMyyyyHHmmss() : "N/A";
                                        updated_user.LASTDEACTIVATEDATE = usr.LastDeActivateDate.Year > 2000 ? usr.LastDeActivateDate.ToddMMyyyyHHmmss() : "N/A";
                                        list_updated_user.Add(updated_user);

                                        if (sendFlag)
                                        {
                                            bool isTransferred = (ACE_Status == 0);
                                            //removed for Account Log Audit CR
                                            *//*List<ApplicationUser> systemAdmins = UserService.GetUsers(new string[] { BIOME.Constants.UserRoles.SystemAdmin }).ToList();
                                            foreach (var item in systemAdmins)
                                            {
                                                logger.Info("SystemAdmin SendAccountSuppendOrDeAactivate: isTransferred:" + isTransferred);
                                                emailService.SendAccountSuppendOrDeAactivate(item.Email, usr.UserName, usr.PersonName, existingRoles, usr.LastSuspendDate.ToString(), usr.LastDeActivateDate.ToString(),usr.AccountStatus, isTransferred);
                                            }*//*

                                            //string existingRoles = string.Join(", ", existingDbRoles.Select(t => t.ToProperRoleName()).ToList());

                                            List<ApplicationUser> permitmanager = UserService.GetUsers(new string[] { BIOME.Constants.UserRoles.PermitManager }).ToList();
                                            foreach (var item in permitmanager)
                                            {
                                                logger.Info("PermitManager SendAccountSuppendOrDeAactivate: isTransferred:" + isTransferred);
                                                emailService.SendAccountSuppendOrDeAactivate(item.Email, usr.UserName, usr.PersonName, roleRemoved, usr.LastSuspendDate.ToString(), usr.LastDeActivateDate.ToString(), usr.AccountStatus, isTransferred);

                                                *//*if (!systemAdmins.Contains(item))
                                                {
                                                    logger.Info("PermitManager SendAccountSuppendOrDeAactivate: isTransferred:" + isTransferred);
                                                    emailService.SendAccountSuppendOrDeAactivate(item.Email, usr.UserName, usr.PersonName, existingRoles, usr.LastSuspendDate.ToString(), usr.LastDeActivateDate.ToString(), usr.AccountStatus, isTransferred);
                                                }*//*
                                            }
                                        }
                                    }
                                }
                            }


                        }
                        catch (Exception ex)
                        {
                            Output = Output + "There is error." + ":" + users[0].ToString() + ":" + ex.Message.ToString();
                            logger.Error(users[0].ToString() + ":" + ex.Message.ToString());

                            sbErrorSummary.AppendLine("There is error while processing user: " + users[0].ToString());
                        }


                    }

                }


            }
            catch (Exception ex)
            {
                Output = Output + "There is error." + ":" + ex.Message.ToString();
                logger.Error(ex.Message.ToString());
                sbErrorSummary.AppendLine("There is error while processing schedule job. Details logs have been captured in the server.");
            }

            //Account Log Audit CR

            *//*string list_updated = "0 record updated";
            string list_updated_failed = "0 record failed to update.";
            string list_not_exist = "0 record not exist in BIOME";*//*

            string list_updated = "0 record";
            string list_updated_failed = "0 record";
            string list_not_exist = "0 record";

            //string str_ace_count = "ACE file have " + ace_file_user_count + " record(s)";
            string str_ace_count = ace_file_user_count + " record(s)";
            if (ace_file_user_count > 0)
            {
                if (list_updated_user.Count > 0)
                    list_updated = getListUpdatedUsers(list_updated_user);

                if (list_not_exist_user.Count > 0)
                    list_not_exist = getListACEUsers(list_not_exist_user);

                if (list_failed_to_update_user.Count > 0)
                    list_updated_failed = getListACEUsers(list_failed_to_update_user);
            }

            if (sbErrorSummary.Length == 0)
                sbErrorSummary.Append("No error.");

            List<ApplicationUser> systemAdmins = UserService.GetUsers(new string[] { BIOME.Constants.UserRoles.SystemAdmin }).Where(t=>t.IsActive).ToList();
            foreach (var item in systemAdmins)
            {
                //logger.Info("SystemAdmin SendAccountSuppendOrDeAactivate: isTransferred:" + isTransferred);

                emailService.SendAccountChangeSummary(item.Email, list_updated, list_updated_failed, list_not_exist, str_ace_count, sbErrorSummary.ToString());
            }

            return ServiceResult.SuccessWithOutput(Output);
        }*/

        private string getListUpdatedUsers(List<NparksUserAccountStatusChangedViewModel> list) {
            string sHeader = getTHForUpdatedUsers();
            StringBuilder sTable = new StringBuilder();
            //sTable.Append("<table><header>" + sHeader + "</header><body>");
            sTable.Append("<table>" + sHeader);

            foreach (NparksUserAccountStatusChangedViewModel row in list) {
                sTable.Append(getTRForUpdatedUsers(row));
            }

            sTable.Append("</table>");
            //sTable.Append("</body></table>");
            return sTable.ToString();
        }


        private string getTHForUpdatedUsers()
        {
            string sTR = "<tr>";

            sTR += getTD("USERID");
            sTR += getTD("SOEID");
            sTR += getTD("STAFFNAME");
            sTR += getTD("EMAIL");
            sTR += getTD("ACCSTATUS");
            sTR += getTD("ROLE_REMOVED");
            sTR += getTD("CURRENT_ROLE");
            sTR += getTD("LASTSUSPENDDATE");
            sTR += getTD("LASTDEACTIVATEDATE");

            sTR += "</tr>";
            return sTR;

        }
        private string getTRForUpdatedUsers(NparksUserAccountStatusChangedViewModel model) {
            string sTR = "<tr>";

            sTR += getTD(model.USERID);
            sTR += getTD(model.SOEID);
            sTR += getTD(model.STAFFNAME);
            sTR += getTD(model.EMAIL);
            sTR += getTD(model.ACCSTATUS);
            sTR += getTD(model.ROLE_REMOVED);
            sTR += getTD(model.CURRENT_ROLE);
            sTR += getTD(model.LASTSUSPENDDATE);
            sTR += getTD(model.LASTDEACTIVATEDATE);

            sTR += "</tr>";
            return sTR;

        }


        private string getListACEUsers(List<NparksUserACEFileViewModel> list)
        {
            string sHeader = getTHForACEUsers();
            StringBuilder sTable = new StringBuilder();
            sTable.Append("<table><header>" + sHeader + "</header><body>");

            foreach (NparksUserACEFileViewModel row in list)
            {
                sTable.Append(getTRForACEUsers(row));
            }

            sTable.Append("</body></table>");
            return sTable.ToString();
        }

        private string getTHForACEUsers()
        {
            string sTR = "<tr>";

            sTR += getTD("USERID");
            sTR += getTD("SOEID");

            sTR += "</tr>";
            return sTR;

        }
        private string getTRForACEUsers(NparksUserACEFileViewModel model)
        {
            string sTR = "<tr>";

            sTR += getTD(model.USERID);
            sTR += getTD(model.SOEID);



            sTR += "</tr>";
            return sTR;

        }
        private string getTD(string val) {

            string sTD = "<td style='border:1px solid black;'>";
            sTD += val;
            sTD += "</td>";
            return sTD;
        }

        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult SendRemiderEmailForSiteManagerPermitApplication(string profileUrl, string emailTemplateUrl, string applicationurl,string contactUsUrl)
        {
            var lstLocation = siteService.GetLatestResearchPermitStudyLocation();
            string strOutput = "";
            int Count = 0;
            foreach (var loc in lstLocation)
            {
                if (loc.Access.Equals(2)) //no status set yet
                {
                    if (loc.ResearchPermitApplication.Status.Any(s => s.StatusName == Research.Permit.Status.InProgress))
                    {
                        //TODO: remove UAT checking before deploying to UAT
                        if ((DateTime.Today.Date - loc.CreatedAt.Date).Days == 7 || (DateTime.Today.Date - loc.CreatedAt.Date).Days >= 10)
                        {
                            var lstSiteManger = siteService.GetSiteManagerBySiteId(loc.Location.Id);

                            foreach (var sitemanager in lstSiteManger)
                            {
                                profileUrl = string.Format(profileUrl.Replace(@"/0/","/{0}/"), loc.ResearchPermitApplication.AtResearchApplication.Researcher.Id.ToString());
                                string url = string.Format(applicationurl.Replace(@"/0/", "/{0}/"), loc.ResearchPermitApplication.AtResearchApplication.Id.ToString());
                                url = url + "?permitid=" + loc.ResearchPermitApplication.Id.ToString();
                                string fullName = loc.ResearchPermitApplication.AtResearchApplication.Researcher.PersonName;
                                emailService.SendEmailReminderToSiteManagerForPermitApplication(sitemanager.Email, sitemanager.PersonName, fullName, profileUrl, url, loc.ResearchPermitApplication.CreatedAt.ToString("dd-MMMM-yyyy"), loc.ResearchPermitApplication.AtResearchApplication.Title,contactUsUrl);
                                Count++;
                                strOutput = strOutput + Count.ToString() + ":" + sitemanager.Email + "," + sitemanager.PersonName + "," + fullName + "," + profileUrl + "," + loc.ResearchPermitApplication.PermitNumber + "," + loc.Location.Name;
                            }

                            if ((DateTime.Today.Date - loc.CreatedAt.Date).Days >= 42)
                            {
                                if (lstSiteManger != null && lstSiteManger.Count > 0) 
                                {
                                    List<ApplicationUser> permitmanager = UserService.GetUsers(new string[] { BIOME.Constants.UserRoles.PermitManager }).ToList();
                                    foreach (var item in permitmanager)
                                    {
                                        string url;
                                        var emailTemplate = emailService.GetEmailTemplate("RESEARCH_PERMIT_EMAIL_REMINDER_TO_SITE_MANAGER_SUPERVISOR");
                                        //logger.Debug(emailTemplate.Id.ToString());
                                        if (emailTemplateUrl != null)
                                            emailTemplateUrl = emailTemplateUrl.Replace("0/", "");
                                        /*if (emailTemplate == null)
                                               url = emailTemplateUrl.Replace("0", "0");
                                          else
                                               url = emailTemplateUrl.Replace("0", emailTemplate.Id.ToString());*/
                                        string tempId = emailTemplate != null ? emailTemplate.Id.ToString() : "0";
                                        string recId = loc.ResearchPermitApplication != null ? loc.ResearchPermitApplication.Id.ToString() : "0";
                                        url = emailTemplateUrl + tempId + "/" + recId + "/";

                                        //Fix for NPARK/BIOME/NCODE/2020_0088
                                        emailService.SendEmailReminderToPermitManagerForPermitApplication(item.Email, item.PersonName, loc.ResearchPermitApplication.AtResearchApplication.Title, loc.ResearchPermitApplication.CreatedAt.ToString("dd-MMMM-yyyy"), url, item.Id, long.Parse(recId), contactUsUrl);
                                        //emailService.SendEmailReminderToPermitManagerForPermitApplication(item.Email, item.PersonName, loc.ResearchPermitApplication.AtResearchApplication.Title, loc.ResearchPermitApplication.AtResearchApplication.CreatedAt.ToString("dd-MMMM-yyyy"), url);

                                    }
                                }
                                

                            }
                        }
                    }
                }
            }

            return ServiceResult.SuccessWithOutput(strOutput + ":Total Count" + Count.ToString());
        }
        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult SendRemiderEmailForSiteManagerPermitApplicationPending(string profileUrl, string emailTemplateUrl, string applicationurl, string contactUsUrl)
        {
            var lstLocation = siteService.GetLatestResearchPermitStudyLocation();
            string strOutput = "";
            int Count = 0;
            foreach (var loc in lstLocation) //Phase4
            {
                if (loc.Access.Equals(-1)) //pending
                {

                    //fix for NPARK/BIOME/NCODE/2020_0185
                    if (loc.ResearchPermitApplication.Status.Any(s => s.StatusName == Research.Permit.Status.InProgress))
                    {
                        if ((DateTime.Today.Date - loc.CreatedAt.Date).Days == 30 || (DateTime.Today.Date - loc.CreatedAt.Date).Days == 44)
                        {
                            var lstSiteManger = siteService.GetSiteManagerBySiteId(loc.Location.Id);

                            foreach (var sitemanager in lstSiteManger)
                            {
                                profileUrl = string.Format(profileUrl.Replace(@"/0/", "/{0}/"), loc.ResearchPermitApplication.AtResearchApplication.Researcher.Id.ToString());
                                string fullName = loc.ResearchPermitApplication.AtResearchApplication.Researcher.PersonName;
                                string appUrl = string.Format(applicationurl.Replace(@"/0/", "/{0}/"), loc.ResearchPermitApplication.AtResearchApplication.Id.ToString());
                                appUrl = appUrl + "?permitid=" + loc.ResearchPermitApplication.Id.ToString();
                                emailService.SendEmailReminderToSiteManagerForPermitApplicationPending(sitemanager.Email, sitemanager.PersonName, fullName, profileUrl, appUrl, loc.ResearchPermitApplication.CreatedAt.ToString("dd-MMMM-yyyy"), loc.ResearchPermitApplication.AtResearchApplication.Title, contactUsUrl);
                                Count++;
                                strOutput = strOutput + "::" + Count.ToString() + ":" + sitemanager.Email + "," + sitemanager.PersonName + "," + fullName + "," + profileUrl + "," + loc.ResearchPermitApplication.PermitNumber + "," + loc.Location.Name + "," + loc.Id;

                            }
                        }
                        else
                        {
                            if ((DateTime.Today.Date - loc.CreatedAt.Date).Days > 44)
                            {
                                if ((((DateTime.Today.Date - loc.CreatedAt.Date).Days - 44) % 7) == 0)
                                {
                                    var lstSiteManger = siteService.GetSiteManagerBySiteId(loc.Location.Id);

                                    foreach (var sitemanager in lstSiteManger)
                                    {
                                        profileUrl = string.Format(profileUrl.Replace(@"/0/", "/{0}/"),loc.ResearchPermitApplication.AtResearchApplication.Researcher.Id.ToString());
                                        string fullName = loc.ResearchPermitApplication.AtResearchApplication.Researcher.PersonName;
                                        string appUrl = string.Format(applicationurl.Replace(@"/0/", "/{0}/"), loc.ResearchPermitApplication.AtResearchApplication.Id.ToString());
                                        appUrl = appUrl + "?permitid=" + loc.ResearchPermitApplication.Id.ToString();
                                        emailService.SendEmailReminderToSiteManagerForPermitApplicationPending(sitemanager.Email, sitemanager.PersonName, fullName, profileUrl, appUrl, loc.ResearchPermitApplication.CreatedAt.ToString("dd-MMMM-yyyy"), loc.ResearchPermitApplication.AtResearchApplication.Title, contactUsUrl);
                                        Count++;
                                        strOutput = strOutput + "::" + Count.ToString() + ":" + sitemanager.Email + "," + sitemanager.PersonName + "," + fullName + "," + profileUrl + "," + loc.ResearchPermitApplication.PermitNumber + "," + loc.Location.Name + "," + loc.Id;
                                    }
                                }
                            }
                        }
                    }
                      

                }

            }

            return ServiceResult.SuccessWithOutput(strOutput + ":Total Count" + Count.ToString());
        }
        [DisableConcurrentExecution(1 * 60)]
        public ServiceResult SendReminderEmailForUnanswerDiscussion()
        {
            /*var unanswerDiscussionList = discussionService.GetUnanswerResearchDiscussion(3); //hardcode for applicationId

            foreach (var item in unanswerDiscussionList)
            {
                //Get SM and P
                string urlString = String.Format("http://{2}/Research/DiscussionForum/{0}/{1}/", 3, 1, "localhost:33266");
                var questionUrl = new Uri(urlString);
                discussionService.SendDiscussionReminderEmailAsync("SM", item.ForumQuestion, questionUrl, "<EMAIL>");
            }*/

            return ServiceResult.Success;
        }

        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult ClearAllIndexFromElasticsearch()
        {
            ElasticClient Client = InitialElasticSearch(Constants.Search.Elasticsearch.SightingIndexName);

            if (Client.Indices.Exists(Constants.Search.Elasticsearch.SightingIndexName).IsValid)
            {
                Client.Indices.Delete(Constants.Search.Elasticsearch.SightingIndexName);
            }
            if (Client.Indices.Exists(Constants.Search.Elasticsearch.ResearchApplicationIndexName).IsValid)
            {
                Client.Indices.Delete(Constants.Search.Elasticsearch.ResearchApplicationIndexName);
            }
            if (Client.Indices.Exists(Constants.Search.Elasticsearch.ResourceIndexName).IsValid)
            {
                Client.Indices.Delete(Constants.Search.Elasticsearch.ResourceIndexName);
            }
            
            var mapIndices = Client.GetIndicesPointingToAlias(Constants.Search.Elasticsearch.MapResourceIndexName);
            //logger.Info(mapIndices);
            //logger.Info("All indixes");
            //logger.Info(String.Join("||", mapIndices.ToList()));
            foreach (String map_index in mapIndices.ToList()) {
                if (Client.Indices.Exists(map_index).IsValid)
                {
                    Client.Indices.Delete(map_index);
                }

            }

            return ServiceResult.Success;
        }

        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult ESjob(int type, int? skip, int? size)
        {
            if (type == 1)
                return RestoreLastUpdatedFromES_Sightings_all();
            else  if (type == 11)
                return RestoreLastUpdatedFromES_Sightings(skip, size.Value);
            else if (type == 2)
                return RestoreLastUpdatedDatePermit();
            else if (type == 3)
                return RestoreLastUpdatedDateResourceDoc();

            return ServiceResult.Success;
        }

        [DisableConcurrentExecution(30 * 60)]
        private ServiceResult RestoreLastUpdatedFromES_Sightings(int? skip, int? size)
        {
            ElasticClient Client = InitialElasticSearch(Constants.Search.Elasticsearch.SightingIndexName);

            //var es_record = Client.Get<SightingsViewModel.SightingsES>(30);
            //var source = es_record.Source;

            var searchResponse = Client.Search<SightingsViewModel.SightingsES>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Id,
                            f => f.UpdatedAt
                        )
                    )
                /* .Excludes(e => e
                     .Fields("num*")
                 )*/
                )
                .From(skip.Value)
                .Size(size.Value)
                .Query(q => q
                    .MatchAll()
                )//.Scroll("30s")
            );
            List<SightingsViewModel.SightingsES> es_docs = new List<SightingsViewModel.SightingsES>();

            if (searchResponse.IsValid && searchResponse.Documents.Any())
            {
                es_docs.AddRange(searchResponse.Documents);
            }

            int totalUpdated = 0;
            int totalCount = 0;
            int pageSize = 100;

            using (ApplicationDbContext boime_db = new ApplicationDbContext())
            {

                totalCount = es_docs.Count;

                var pages = (int)Math.Round(Math.Ceiling(totalCount / (double)pageSize), MidpointRounding.AwayFromZero);
                for (int i = 0; i < pages; i++)
                {
                    var skipCount = i * pageSize;
                    var page_part = es_docs.Skip(skipCount).Take(pageSize).ToList();
                    long[] es_ids = page_part.Select(t => t.Id).ToArray();

                    List<SightingDetail> db_records = boime_db.SightingDetails.Where(t => es_ids.Contains(t.Id) && t.IsIndex).ToList();
                    foreach (SightingDetail db_obj in db_records)
                    {
                        if (!db_obj.IndexedDateTime.HasValue)
                            db_obj.IndexedDateTime = db_obj.UpdatedAt;

                        SightingsViewModel.SightingsES es_obj = page_part.Where(t => t.Id == db_obj.Id).FirstOrDefault();
                        if (es_obj != null)
                        {
                            db_obj.UpdatedAt = es_obj.UpdatedAt;
                        }
                    }
                    totalUpdated += db_records.Count;

                    boime_db.SaveChangesOnly();
                }

            }

            return ServiceResult.SuccessWithOutput(totalUpdated);
        }

        [DisableConcurrentExecution(30 * 60)]
        private ServiceResult RestoreLastUpdatedFromES_Sightings_all()
        {
            ElasticClient Client = InitialElasticSearch(Constants.Search.Elasticsearch.SightingIndexName);

            //var es_record = Client.Get<SightingsViewModel.SightingsES>(30);
            //var source = es_record.Source;

            var searchResponse = Client.Search<SightingsViewModel.SightingsES>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Id,
                            f => f.UpdatedAt
                        )
                    )
                /* .Excludes(e => e
                     .Fields("num*")
                 )*/
                )
                .Size(200)
                .Query(q => q
                    .MatchAll()
                ).Scroll("30s")
            );
            List<SightingsViewModel.SightingsES> es_docs = new List<SightingsViewModel.SightingsES>();

            while (searchResponse.Documents.Any())
            {

                if (searchResponse.IsValid)
                {
                    es_docs.AddRange(searchResponse.Documents);
                }

                searchResponse = Client.Scroll<SightingsViewModel.SightingsES>("30s", searchResponse.ScrollId);

            }

            int totalUpdated = 0;
            int totalCount = 0;
            int pageSize = 100;

            using (ApplicationDbContext boime_db = new ApplicationDbContext())
            {

                totalCount = es_docs.Count;

                var pages = (int)Math.Round(Math.Ceiling(totalCount / (double)pageSize), MidpointRounding.AwayFromZero);
                for (int i = 0; i < pages; i++)
                {
                    var skipCount = i * pageSize;
                    var page_part = es_docs.Skip(skipCount).Take(pageSize).ToList();
                    long[] es_ids = page_part.Select(t => t.Id).ToArray();

                    List<SightingDetail> db_records = boime_db.SightingDetails.Where(t => es_ids.Contains(t.Id) && t.IsIndex).ToList();
                    foreach (SightingDetail db_obj in db_records)
                    {
                        if (!db_obj.IndexedDateTime.HasValue)
                            db_obj.IndexedDateTime = db_obj.UpdatedAt;

                        SightingsViewModel.SightingsES es_obj = page_part.Where(t => t.Id == db_obj.Id).FirstOrDefault();
                        if (es_obj != null)
                        {
                            db_obj.UpdatedAt = es_obj.UpdatedAt;
                        }
                    }
                    totalUpdated += db_records.Count;

                    boime_db.SaveChangesOnly();
                }

            }

            return ServiceResult.SuccessWithOutput(totalUpdated);
        }
        [DisableConcurrentExecution(30 * 60)]
        private ServiceResult RestoreLastUpdatedDatePermit()
        {

            int total = 0;
            using (ApplicationDbContext boime_db = new ApplicationDbContext())
            {

                //DateTimeOffset dtStart = new DateTimeOffset(2022, 02, 23, 0, 0, 0, TimeSpan.Zero);
                //DateTimeOffset dtStart = new DateTimeOffset(2022, 02, 20, 0, 0, 0, TimeSpan.Zero);
                //DateTimeOffset dtend = new DateTimeOffset(2022, 03, 15, 0, 0, 0, TimeSpan.Zero);

                int[] years = boime_db.ResearchPermitApplications.Where(t=>t.CreatedAt!=null).Select(t => t.CreatedAt.Year).Distinct().ToArray();

                string issuedStaus = Research.Permit.Status.Issued;
                //                var query = dbContext.ResearchPermitApplications.Where(t => !t.IsIndex && t.Status.Count(s => s.StatusName == issuedStaus) > 0).Select(t => t.AtResearchApplication).AsQueryable();

                

                foreach (int year in years) {

                    List<ResearchPermitApplication> db_records = boime_db.ResearchPermitApplications.Where(t => t.IsIndex && t.CreatedAt != null && t.CreatedAt.Year == year
                    && t.Status.Count(s => s.StatusName == issuedStaus) > 0 && t.DateApproved!=null).ToList();

                    foreach (ResearchPermitApplication dbobj in db_records)
                    {
                        if (!dbobj.IndexedDateTime.HasValue) 
                            dbobj.IndexedDateTime = dbobj.UpdatedAt;
                        

                        if (dbobj.DateApproved != null)
                            dbobj.UpdatedAt = dbobj.DateApproved.UpdatedAt;
                    }

                    total += db_records.Count;

                    //db_records.ForEach(t => t.UpdatedAt = t.DateApproved.UpdatedAt);

                    boime_db.SaveChangesOnly();
                }


            }

            return ServiceResult.SuccessWithOutput(total);
        }
        [DisableConcurrentExecution(30 * 60)]
        private ServiceResult RestoreLastUpdatedDateResourceDoc()
        {

            int total = 0;
            using (ApplicationDbContext boime_db = new ApplicationDbContext())
            {

                int[] years = boime_db.ResourceDocuments.Where(t => t.CreatedAt != null).Select(t => t.CreatedAt.Year).Distinct().ToArray();
                
                foreach (int year in years)
                {

                    List<ResourceDocument> db_records = boime_db.ResourceDocuments.Where(t => (t.IsIndex || t.IsMapIndex) && t.CreatedAt != null && t.CreatedAt.Year == year && t.AtResourceMetaData != null).ToList();

                    foreach (ResourceDocument dbobj in db_records) {

                        if (!dbobj.IndexedDateTime.HasValue && dbobj.IsIndex)
                            dbobj.IndexedDateTime = dbobj.UpdatedAt;

                        if (!dbobj.IndexedMapDateTime.HasValue && dbobj.IsMapIndex)
                            dbobj.IndexedMapDateTime = dbobj.UpdatedAt;

                        if (dbobj.AtResourceMetaData != null)
                            dbobj.UpdatedAt = dbobj.AtResourceMetaData.UpdatedAt;
                    }
                    total += db_records.Count;
                    boime_db.SaveChangesOnly();
                }

            }

            return ServiceResult.SuccessWithOutput(total);
        }
        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult IndexSightingRecords()
        {
            return IndexSightingRecords(0);
        }
        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult IndexSightingRecords(long ID)
        {

            //return ServiceResult.SuccessWithOutput("Elastic Search Unavailable");

            //Commented because Elastic Search libs have been removed.
            ElasticClient Client = InitialElasticSearch(Constants.Search.Elasticsearch.SightingIndexName);

            //string CurrentIndexName = string.Format("{0}-{1:dd-MM-yyyy-HH-mm-ss}", Constants.Search.Elasticsearch.SightingIndexNameAlias, DateTime.Now);
            string CurrentIndexName = Constants.Search.Elasticsearch.SightingIndexName;

            /*var indexResult = Client.Indices.Create(CurrentIndexName, i => i
                           .Mappings(ms => ms
                               .Map<SightingDetail>(m => m
                                       .AutoMap()
                                       .Properties(pro => pro
                                           .GeoPoint(geo => geo
                                               .Name(n => n.SightingLocation)
                                               .LatLon(true)
                                               )
                                           )
                                       )
                                   )
                                 );*/


            if (!Client.Indices.Exists(CurrentIndexName).IsValid)
            {

                var indexResult = Client.Indices.Create(CurrentIndexName, i => i
                               .Map<SightingsViewModel.SightingsES>(m => m

                                       .AutoMap()
                                       .Properties(pro => pro
                                           .GeoShape(geo => geo
                                               .Name(n => n.SightingLocation)
                                               )
                                           )
                                       )
                                 );

            }


            



            //Indexing Data
            int totalCount = 0;
            int pageSize = 20;
            var sightingDetailsDB = sightingService.GetSightingsListIndex(ID);

            var sightingDetails = Mapper.Map<List<SightingsViewModel.SightingsES>>(sightingDetailsDB);
            totalCount = sightingDetails.Count();
            StringBuilder stringBuilder = new StringBuilder();
            var pages = (int)Math.Round(Math.Ceiling(totalCount / (double)pageSize), MidpointRounding.AwayFromZero);
            for (int i = 0; i < pages; i++)
            {
                var skipCount = i * pageSize;
                var sightingsPart = sightingDetails.Skip(skipCount).Take(pageSize).ToList();

                List<long> signtingIDs = new List<long>();
                foreach (var sightings in sightingsPart)
                {
                    signtingIDs.Add(sightings.Id);

                    if (sightings.Latitude != 0 && sightings.Longitude != 0)
                    {
                        GeoJSON.Net.Geometry.GeometryCollection geometryCollection = new GeoJSON.Net.Geometry.GeometryCollection();
                        geometryCollection.Geometries.Add(new GeoJSON.Net.Geometry.Point(new GeoJSON.Net.Geometry.GeographicPosition(sightings.Latitude, sightings.Longitude)));
                        sightings.SightingLocation = geometryCollection;
                    }

                }
                
                var result = Client.IndexMany(sightingsPart, CurrentIndexName);
                //stringBuilder.AppendLine(result.DebugInformation);
                //stringBuilder.AppendLine("-isvalid:" + result.IsValid + "-Errors:" + result.Errors + "-ItemsWithErrors:" + result.ItemsWithErrors.Any()) ;
                //stringBuilder.AppendLine(string.Join("Item: ",result.Items.Select(t=> "ID: " + t.Id + "_IsValid" + t.IsValid + "_status:" + t.Status).ToArray()));
                List<long> failedIds = result.Items.Where(t => t.Status != 201).Select(t => long.Parse(t.Id)).ToList();
                signtingIDs = signtingIDs.Where(t => !failedIds.Contains(t)).ToList();
                if (signtingIDs.Any())
                {
                    sightingService.UpdateSightingsIndex(signtingIDs.ToArray());
                }
            }

            //SwapAlias(Constants.Search.Elasticsearch.SightingIndexNameAlias, Constants.Search.Elasticsearch.SightingIndexOldNameAlias, Client, CurrentIndexName);

            return ServiceResult.Success;

        }

        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult RemoveSightingsfromES(long id)
        {
            ElasticClient Client = InitialElasticSearch(Constants.Search.Elasticsearch.SightingIndexName);

            var response = Client.Delete<SightingsViewModel.SightingsES>(id);
            if (response.IsValid)
                return ServiceResult.SuccessWithOutput(id);
            else
            {
                //throw error for hangfire to retry
                throw new InvalidOperationException("delete failed");
            }
        }

        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult UpdateSightingsToES(long id)
        {
            ElasticClient Client = InitialElasticSearch(Constants.Search.Elasticsearch.SightingIndexName);

            var sightingDetailsDB = sightingService.GetSightingsByIdForES(id);

            var sightings = Mapper.Map<SightingsViewModel.SightingsES>(sightingDetailsDB);


            if (sightings.Latitude != 0 && sightings.Longitude != 0)
            {
                GeoJSON.Net.Geometry.GeometryCollection geometryCollection = new GeoJSON.Net.Geometry.GeometryCollection();
                geometryCollection.Geometries.Add(new GeoJSON.Net.Geometry.Point(new GeoJSON.Net.Geometry.GeographicPosition(sightings.Latitude, sightings.Longitude)));
                sightings.SightingLocation = geometryCollection;
            }

            var result = Client.Update<SightingsViewModel.SightingsES>(id,t=>t.Index(Constants.Search.Elasticsearch.SightingIndexName).Doc(sightings));
            if (result.IsValid)
            {
                sightingService.UpdateSightingsIndex(id);
                return ServiceResult.SuccessWithOutput(id);
            }
            else
            {
                //throw error for hangfire to retry
                throw new InvalidOperationException("update failed");
            }
            //return ServiceResult.FailWithOutput("update failed");
        }

        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult IndexApplicationRecords()
        {
            return IndexApplicationRecords(0);
        }

        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult IndexApplicationRecords(long ID)
        {
            //return ServiceResult.SuccessWithOutput("Elastic Search Unavailable");

            //Commented because Elastic Search libs have been removed.
            ElasticClient Client = InitialElasticSearch(Constants.Search.Elasticsearch.ResearchApplicationIndexName);

            //string CurrentIndexName = string.Format("{0}-{1:dd-MM-yyyy-HH-mm-ss}", Constants.Search.Elasticsearch.ApplicationIndexNameAlias, DateTime.Now);

            string CurrentIndexName = Constants.Search.Elasticsearch.ResearchApplicationIndexName;



            //var indexResult = Client.Indices.Create(CurrentIndexName, i => i.Map<ResearchApplication>(m => m.AutoMap()));

            //var indexResult = Client.Indices.Create(CurrentIndexName, i => i.Map<ResearchApplication>(m => m.a));

            //Client.Indices.DeleteAsync("applicationbiomesearch"); //Delete old index that no longer used if any.

            if (!Client.Indices.Exists(CurrentIndexName).IsValid) {

                var indexResult = Client.Indices.Create(CurrentIndexName, i => i.Map<ApplicationStatusViewModel.ResearchApplicationES>(m => m
                                           .AutoMap().Properties(pro => pro.GeoShape(t => t.Name(n => n.StudyLocationsDrawnGeo))
                                           )));

            }


            /* if (!Client.Ingest.GetPipeline(t => t.Id("researchapp_removefield")).IsValid)
             {
                 //create if not exist.
                 var piplineResponse = Client.Ingest.PutPipeline("researchapp_removefield", p => p
                      .Description("remove fields")
                      .Processors(pr => pr
                        .Remove<ApplicationStatusViewModel.PermitApplicationES>(r => r
                          .Field(ff => ff.Field(ss => ss.StudyLocationsDrawn)))
                      )
                    );

                 bool isValid = piplineResponse.IsValid;
             }
 */


            /*var indexResult = Client.Indices.Create(CurrentIndexName, i => i.Map<ApplicationStatusViewModel.ResearchApplicationES>(m => m
                                       .AutoMap()));*/

            //Indexing Data
            int totalCount = 0;
            int pageSize = 20;
            var applicationDetailsDB = researchService.GetResearchApplicationsToIndex(ID);
            //GeometryCollection collection = new GeometryCollection();

            var applicationDetails = Mapper.Map<List<ApplicationStatusViewModel.ResearchApplicationES>>(applicationDetailsDB);

            //var applicationDetails = researchService.GetResearchApplications();
            totalCount = applicationDetails.Count();



            var pages = (int)Math.Round(Math.Ceiling(totalCount / (double)pageSize), MidpointRounding.AwayFromZero);
            //bool isIndexed = true;
            for (int i = 0; i < pages; i++)
            {
                var skipCount = i * pageSize;
                var application = applicationDetails.Skip(skipCount).Take(pageSize).ToList();

                //List<long> ResearchAppID = new List<long>();
                List<long> ResearchPermitID = new List<long>();

                foreach (var app in application) {

                    GeoJSON.Net.Geometry.GeometryCollection geometryCollection = new GeoJSON.Net.Geometry.GeometryCollection();
                    app.PermitApplications = app.PermitApplications.Where(t => t.HasIssuedStatus).ToList();

                    foreach (var permitApp in app.PermitApplications) {
                        ResearchPermitID.Add(permitApp.Id);

                        try
                        {
                            GeoJSON.Net.Feature.FeatureCollection featureCollection = JsonConvert.DeserializeObject<GeoJSON.Net.Feature.FeatureCollection>(permitApp.StudyLocationsDrawn);

                            foreach (var feat in featureCollection.Features)
                            {

                                //geometryCollection.Geometries.Add(feat.Geometry);
                                switch (feat.Geometry.Type)
                                {
                                    case GeoJSON.Net.GeoJSONObjectType.Point:
                                        //geometryCollection.Geometries.Add(new GeoJSON.Net.Geometry.Point(((GeoJSON.Net.Geometry.Point)feat.Geometry).Coordinates));
                                        geometryCollection.Geometries.Add((GeoJSON.Net.Geometry.Point)feat.Geometry);
                                        break;
                                    case GeoJSON.Net.GeoJSONObjectType.Polygon:
                                        geometryCollection.Geometries.Add((GeoJSON.Net.Geometry.Polygon)feat.Geometry);
                                        break;
                                    default:
                                        break;
                                }

                            }
                        }
                        catch
                        { 
                        }
                    }

                    app.StudyLocationsDrawnGeo = geometryCollection;

                    /*
                    if (!string.IsNullOrEmpty(app.StudyLocationsDrawn))
                    {
                        //Nest.GeometryCollection geometryCollection1 = ;
                        //Nest.PointGeoShape pointGeoShape = new PointGeoShape(new GeoCoordinate());

                    }
                    */
                    Client.Delete<ApplicationStatusViewModel.ResearchApplicationES>(app.Id); //Delete existing research app
                    //ResearchAppID.Add(app.Id);
                }



                //var result  = Client.Bulk(t => t.Index(CurrentIndexName).IndexMany(application).Pipeline("researchapp_removefield"));
                var result = Client.Bulk(t => t.Index(CurrentIndexName).IndexMany(application));
                //var result = Client.IndexMany(application, CurrentIndexName);
                List<long> failedIds = result.Items.Where(t => t.Status != 201).Select(t => long.Parse(t.Id)).ToList();
                ResearchPermitID = ResearchPermitID.Where(t => !failedIds.Contains(t)).ToList();
                if (ResearchPermitID.Any())
                {
                    researchService.UpdateResearchPermitIndex(ResearchPermitID.ToArray());
                }
                /*if (result.IsValid)
                {
                    researchService.UpdateResearchPermitIndex(ResearchPermitID.ToArray());
                }*/
                
            }

            return ServiceResult.Success;
            

        }

        /*[DisableConcurrentExecution(30 * 60)]
        public ServiceResult DeleteAndUpdateApplicationRecordToES(long ID)
        {
            ElasticClient Client = InitialElasticSearch(Constants.Search.Elasticsearch.ResearchApplicationIndexName);

            var applicationDetailsDB = researchService.GetResearchApplicationsByIdForES(ID);
            
            if(applicationDetailsDB!=null)
            {
                var app  = Mapper.Map<ApplicationStatusViewModel.ResearchApplicationES>(applicationDetailsDB);

                if(app != null)
                {

                    GeoJSON.Net.Geometry.GeometryCollection geometryCollection = new GeoJSON.Net.Geometry.GeometryCollection();
                    app.PermitApplications = app.PermitApplications.Where(t => t.HasIssuedStatus).ToList();

                    foreach (var permitApp in app.PermitApplications)
                    {
                        
                        try
                        {
                            GeoJSON.Net.Feature.FeatureCollection featureCollection = JsonConvert.DeserializeObject<GeoJSON.Net.Feature.FeatureCollection>(permitApp.StudyLocationsDrawn);

                            foreach (var feat in featureCollection.Features)
                            {

                                //geometryCollection.Geometries.Add(feat.Geometry);
                                switch (feat.Geometry.Type)
                                {
                                    case GeoJSON.Net.GeoJSONObjectType.Point:
                                        //geometryCollection.Geometries.Add(new GeoJSON.Net.Geometry.Point(((GeoJSON.Net.Geometry.Point)feat.Geometry).Coordinates));
                                        geometryCollection.Geometries.Add((GeoJSON.Net.Geometry.Point)feat.Geometry);
                                        break;
                                    case GeoJSON.Net.GeoJSONObjectType.Polygon:
                                        geometryCollection.Geometries.Add((GeoJSON.Net.Geometry.Polygon)feat.Geometry);
                                        break;
                                    default:
                                        break;
                                }

                            }
                        }
                        catch
                        {
                        }
                    }

                    app.StudyLocationsDrawnGeo = geometryCollection;
          
                    Client.Delete<ApplicationStatusViewModel.ResearchApplicationES>(app.Id); //Delete existing research app

                    var result = Client.IndexDocument(app);
                    if (result.IsValid)
                        return ServiceResult.SuccessWithOutput(app.Id);
                }


            }

            return ServiceResult.Success;


        }*/
        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult SendAuditLogMonthlyReport(bool isIncludeCurrentMonth = false)
        {
            try
            {
                string DBAutoTrackLicenseKey = ConfigurationManager.AppSettings[BIOME.Constants.Configuration.Server.AppSettings.DBAutoTrackLicenseKey];
                //var reportFullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "UploadFiles\\AuditLogReport\\");
                var reportFullPath = Path.GetTempPath();
                //var reportFullPath1 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "UploadFiles\\AuditLogReport");

                //Initialize a new PDF Document
                Document doc = new Document();

                /* IMPORTANT - Please Enter LicenseKey for Licensed Version */
                doc.LicenseKey = DBAutoTrackLicenseKey;

                doc.Title = "Audit Log Monthly Report";
                doc.Author = "ncode";
                doc.Creator = "ncode";

                dbAutoTrack.PDFWriter.Page curPage = null;
                PDFGraphics graphics = null;

                PDFFont font1 = new PDFFont(new Font("Arial", 8, FontStyle.Bold), false);
                PDFFont font2 = new PDFFont(new Font("Arial", 8, FontStyle.Regular), false);

                Border border = new Border(0.5f, RGBColor.Black, LineStyle.Solid);

                TableStyle tblStyle = new TableStyle(font1, 8, RGBColor.Black);
                TableStyle tblHeaderStyle = new TableStyle(font1, 8, RGBColor.Black, TextAlignment.Left, ContentAlignment.MiddleLeft, true, PDFColor.Transparent, border, false);
                TableStyle tblRowStyle = new TableStyle(font2, 8, RGBColor.Black, TextAlignment.Left, ContentAlignment.MiddleLeft, true, RGBColor.Transparent, border, false);
                TableStyle tblAltRowStyle = new TableStyle(font2, 8, RGBColor.Black, TextAlignment.Left, ContentAlignment.MiddleLeft, true, RGBColor.Lavender, border, false);

                //Initialize new page with default PageSize A4
                curPage = new dbAutoTrack.PDFWriter.Page(PageSize.A4, PageOrientation.Landscape);
                doc.Pages.Add(curPage);//Add page to document

                //Get the PDFGraphics object for drawing to the page.
                graphics = curPage.Graphics;
                var month = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var sDate = month.AddMonths(-1);
                var eDate = month;
                //bool IsIncludeCurrentMonth = systemParameterService.IsIncludeCurrentMonthForALMonthlyReport();
                if (isIncludeCurrentMonth)
                    eDate = DateTime.Now;

                //--Header
                graphics.DrawString(50, 50, "BIOME Audit Log Monthly Report " + string.Format("({0} to {1})", sDate.ToString("d MMM yyyy"), eDate.ToString("d MMM yyyy")), RGBColor.Black, font1, 12);
                graphics.DrawString(50, 60, "Generated on " + string.Format("{0}", DateTime.Now.ToString("d MMM yyyy HH:mmtt")), RGBColor.Black, font2, 8);
                graphics.DrawLine(50, 66, 785, 66, 1, LineStyle.Solid, PDFColor.Black);
                graphics.DrawString(50, 80, "Please refer to Appendix for the description of Additional Info.", PDFColor.Black, font2, 8);
                //-Header

                //Section 1
                List<KeyValuePair<string, Table>> listTables = new List<KeyValuePair<string, Table>>();

                Table table = new Table(tblStyle);
                table.CellPadding = 4f;
                table.Columns.Add(35);
                table.Columns.Add(53);
                table.Columns.Add(70);
                table.Columns.Add(130);
                table.Columns.Add(345);
                table.Columns.Add(70);

                Row row = new Row(table, tblHeaderStyle);
                row.Height = 25;
                row.Cells.Add("#");
                row.Cells.Add("Date and Time");
                row.Cells.Add("Name");
                row.Cells.Add("Email");
                row.Cells.Add("Additional Info");
                row.Cells.Add("IP Address");
                table.Rows.Add(row);

                List<AuditLogAction> logActions = auditTrailService.GetAuditLogActions().Where(t => !string.IsNullOrEmpty(t.AdditionalInfoDesc)).ToList();
                List<AuditLogAction> loginLogActions = logActions.Where(t => t.Module.Trim().Equals(BIOME.Enumerations.Audit.Module.Users.Trim(), StringComparison.OrdinalIgnoreCase)).ToList();

                List<AuditTrailLogging> login = auditTrailService.GetLoginAfterOfficeHourLogs(sDate, eDate);
                int i = 0;
                foreach (AuditTrailLogging lRec in login)
                {
                    row = new Row(table, tblRowStyle);
                    row.Height = 24;
                    row.KeepTogether = true;
                    row.Cells.Add((++i).ToString());
                    row.Cells.Add(lRec.CreatedAt.ToString("yyyy-MM-dd HH:mm"));
                    row.Cells.Add(lRec.PersonName);
                    row.Cells.Add(lRec.UserEmail);
                    AuditLogAction alog = loginLogActions.Where(t => t.Action.Trim().Equals(lRec.ActionName.Trim(), StringComparison.OrdinalIgnoreCase)).Take(1).SingleOrDefault();

                    var sourceSite = "Source: " + lRec.SiteType + "\n";
                    if (alog != null)
                    {
                        if (alog.AdditionalInfoDesc.Contains("\\n"))
                        {
                            var addInfo = alog.AdditionalInfoDesc.Replace("\\n", "\n");
                            //row.Cells.Add(alog.Source + ": " + addInfo + " \n" + lRec.Event);
                            row.Cells.Add(sourceSite + addInfo + "\n" + lRec.Event);
                        }
                        else
                        {
                            //row.Cells.Add(alog.Source + ": {" + alog.AdditionalInfoDesc + "} \n" + lRec.Event);
                            row.Cells.Add(sourceSite + alog.AdditionalInfoDesc +"\n" + lRec.Event);
                        }
                    }
                    else
                        row.Cells.Add(sourceSite + lRec.Event);

                    row.Cells.Add(lRec.IP);
                    table.Rows.Add(row);
                }
                listTables.Add(new KeyValuePair<string, Table>("Table 1: Login after office-hours (Sat, Sun and 7pm-7am during weekend)", table));

                table = new Table(tblStyle);
                table.CellPadding = 4f;
                table.Columns.Add(35);
                table.Columns.Add(53);
                table.Columns.Add(70);
                table.Columns.Add(130);
                table.Columns.Add(345);
                table.Columns.Add(70);

                row = new Row(table, tblHeaderStyle);
                row.Height = 25;
                row.Cells.Add("#");
                row.Cells.Add("Date and Time");
                row.Cells.Add("Name");
                row.Cells.Add("Email");
                row.Cells.Add("Additional Info");
                row.Cells.Add("IP Address");
                table.Rows.Add(row);

                List<AuditTrailLogging> unsuccesslogin = auditTrailService.GetUnsuccessfulLoginLogs(sDate, eDate);
                i = 0;
                foreach (AuditTrailLogging lRec in unsuccesslogin)
                {
                    row = new Row(table, tblRowStyle);
                    row.KeepTogether = true;
                    row.Cells.Add((++i).ToString());
                    row.Cells.Add(lRec.CreatedAt.ToString("yyyy-MM-dd HH:mm"));
                    row.Cells.Add(lRec.PersonName);
                    row.Cells.Add(lRec.UserEmail);
                    var eventInfo = lRec.Event;
                    if (eventInfo.Contains("API Login Unsuccessfully, InvalidLogin:"))
                    {
                        eventInfo = lRec.Event.Substring(0, lRec.Event.IndexOf(", InvalidLogin:"));
                    }

                    var sourceSite = "Source: " + lRec.SiteType + "\n";
                    //eventInfo = "Source: " + lRec.SiteType + "\n" + eventInfo;
                    AuditLogAction alog = loginLogActions.Where(t => t.Action.Trim().Equals(lRec.ActionName.Trim(), StringComparison.OrdinalIgnoreCase)).Take(1).SingleOrDefault();
                    if (alog != null)
                    {
                        if (alog.AdditionalInfoDesc.Contains("\\n"))
                        {
                            var addInfo = alog.AdditionalInfoDesc.Replace("\\n", "\n");
                            //row.Cells.Add(alog.Source + ": " + addInfo + " \n" + eventInfo);
                            row.Cells.Add(sourceSite + addInfo + " \n" + eventInfo);
                        }
                        else
                        {
                            //row.Cells.Add(alog.Source + ": {" + alog.AdditionalInfoDesc + "} \n" + eventInfo);
                            row.Cells.Add(sourceSite + alog.AdditionalInfoDesc + "\n" + eventInfo);
                        }
                    }
                    else
                        row.Cells.Add(sourceSite + eventInfo);

                    row.Cells.Add(lRec.IP);
                    table.Rows.Add(row);
                }
                listTables.Add(new KeyValuePair<string, Table>("Table 2: Unsuccessful Login", table));

                table = new Table(tblStyle);
                table.CellPadding = 4f;
                table.Columns.Add(35);
                table.Columns.Add(53);
                table.Columns.Add(70);
                table.Columns.Add(130);
                table.Columns.Add(345);
                table.Columns.Add(70);

                row = new Row(table, tblHeaderStyle);
                row.Height = 25;
                row.Cells.Add("#");
                row.Cells.Add("Date and Time");
                row.Cells.Add("Name");
                row.Cells.Add("Email");
                row.Cells.Add("Additional Info");
                row.Cells.Add("IP Address");
                table.Rows.Add(row);
                List<AuditTrailLogging> incorrectOTP = auditTrailService.GetIncorrectOTPLogs(sDate, eDate);
                i = 0;
                foreach (AuditTrailLogging lRec in incorrectOTP)
                {
                    row = new Row(table, tblRowStyle);
                    row.Height = 24;
                    row.KeepTogether = true;
                    row.Cells.Add((++i).ToString());
                    row.Cells.Add(lRec.CreatedAt.ToString("yyyy-MM-dd HH:mm"));
                    row.Cells.Add(lRec.PersonName);
                    row.Cells.Add(lRec.UserEmail);
                    AuditLogAction alog = loginLogActions.Where(t => t.Action.Trim().Equals(lRec.ActionName.Trim(), StringComparison.OrdinalIgnoreCase)).Take(1).SingleOrDefault();

                    var sourceSite = "Source: " + lRec.SiteType + "\n";
                    //var eventInfo = sourceSite + lRec.Event;
                    if (alog != null)
                    {
                        if (alog.AdditionalInfoDesc.Contains("\\n"))
                        {
                            var addInfo = alog.AdditionalInfoDesc.Replace("\\n", "\n");
                            //row.Cells.Add(alog.Source + ": " + addInfo + " \n" + eventInfo);
                            row.Cells.Add(sourceSite + addInfo + "\n" + lRec.Event);
                        }
                        else
                        {
                            //string additionalInfo = ConstructJson(alog.AdditionalInfoDesc, lRec.Event);
                            //row.Cells.Add(alog.Source + "\n" + additionalInfo);
                            //row.Cells.Add(alog.Source + ": {" + alog.AdditionalInfoDesc + "} \n" + eventInfo);
                            row.Cells.Add(sourceSite + alog.AdditionalInfoDesc + "\n" + lRec.Event);
                        }
                    }
                    else
                        row.Cells.Add(sourceSite + lRec.Event);

                    row.Cells.Add(lRec.IP);
                    table.Rows.Add(row);
                }
                listTables.Add(new KeyValuePair<string, Table>("Table 3. Incorrect OTP", table));

                float curY = 110;
                Tuple<float, dbAutoTrack.PDFWriter.Page> res1 = RenderSection(doc, curPage, curY, "Section 1: Unexceptional Activities", listTables, font2);

                //start section 2 in new page
                curPage = new dbAutoTrack.PDFWriter.Page(PageSize.A4, PageOrientation.Landscape);
                doc.Pages.Add(curPage); //Add page to document
                curY = 50;

                listTables = new List<KeyValuePair<string, Table>>();
                table = new Table(tblStyle);
                table.CellPadding = 4f;
                table.Columns.Add(35);
                table.Columns.Add(53);
                table.Columns.Add(70);
                table.Columns.Add(130);
                table.Columns.Add(50);
                table.Columns.Add(60);
                table.Columns.Add(270);
                table.Columns.Add(70);

                row = new Row(table, tblHeaderStyle);
                row.Cells.Add("#");
                row.Cells.Add("Date and Time");
                row.Cells.Add("Name");
                row.Cells.Add("Email");
                row.Cells.Add("Module");
                row.Cells.Add("Action");
                row.Cells.Add("Additional Info");
                row.Cells.Add("IP Address");
                table.Rows.Add(row);

                //List<string> adminRoles = new List<string>() { BIOME.Constants.UserRoles.SystemAdmin, BIOME.Constants.UserRoles.Expert, BIOME.Constants.UserRoles.PermitManager, BIOME.Constants.UserRoles.ProjectAdmin, BIOME.Constants.UserRoles.ProjectMember, BIOME.Constants.UserRoles.ResourceUploader, BIOME.Constants.UserRoles.SiteManager };
                List<string> adminRoles = new List<string>() { BIOME.Constants.UserRoles.SystemAdmin };
                List<ApplicationUser> adminUsers = UserService.GetUsers(adminRoles.ToArray()).ToList();
                List<string> adminUserEmails = adminUsers.Select(t => t.Email).ToList();
                List<AuditTrailLogging> adminLogs = auditTrailService.GetAdminActionLogs(adminUserEmails, sDate, eDate);

                i = 0;
                foreach (AuditTrailLogging lRec in adminLogs)
                {
                    row = new Row(table, tblRowStyle);
                    row.KeepTogether = true;
                    row.Height = 24;
                    row.Cells.Add((++i).ToString());
                    row.Cells.Add(lRec.CreatedAt.ToString("yyyy-MM-dd HH:mm"));
                    row.Cells.Add(lRec.PersonName);
                    row.Cells.Add(lRec.UserEmail);
                    row.Cells.Add(lRec.Module);
                    row.Cells.Add(lRec.ActionName);

                    var eventInfo = lRec.Event;
                    if (eventInfo.Contains("API Login Unsuccessfully, InvalidLogin:"))
                    {
                        eventInfo = lRec.Event.Substring(0, lRec.Event.IndexOf(", InvalidLogin:"));
                    }

                    var sourceSite = "Source: " + lRec.SiteType + "\n";
                    //eventInfo = sourceSite + eventInfo;
                    AuditLogAction alog = logActions.Where(t => t.Action.Trim().Equals(lRec.ActionName.Trim(), StringComparison.OrdinalIgnoreCase) && t.Module.Trim().Equals(lRec.Module.Trim(), StringComparison.OrdinalIgnoreCase)).Take(1).SingleOrDefault();
                    if (alog != null)
                    {
                        if (alog.AdditionalInfoDesc.Contains("\\n"))
                        {
                            var addInfo = alog.AdditionalInfoDesc.Replace("\\n", "\n");
                            //row.Cells.Add(alog.Source + ": " + addInfo + " \n" + eventInfo);
                            //row.Cells.Add(addInfo + " \n" + eventInfo);
                            row.Cells.Add(sourceSite + addInfo + " \n" + eventInfo);
                        }
                        else
                        {
                            //string additionalInfo = ConstructJson(alog.AdditionalInfoDesc, eventInfo);
                            //row.Cells.Add(alog.Source + "\n" + additionalInfo);
                            //row.Cells.Add(alog.Source + ": {" + alog.AdditionalInfoDesc + "} \n" + eventInfo);
                            row.Cells.Add(sourceSite + alog.AdditionalInfoDesc + "\n" + eventInfo);
                        }
                    }
                    else
                        row.Cells.Add(sourceSite + eventInfo);

                    row.Cells.Add(lRec.IP);
                    table.Rows.Add(row);
                }
                listTables.Add(new KeyValuePair<string, Table>("", table));

                curY = 90;
                Tuple<float, dbAutoTrack.PDFWriter.Page> res2 = RenderSection(doc, curPage, curY, "Section 2: Activities by Administrators", listTables, font2);

                if (res2 != null && res2.Item1 <= 460) //check if there is space for signature or else start on new page
                {   //res2.Item1 <= 455 res2.Item1 <= 675
                    curY = res2.Item1 + 30;
                    curPage = res2.Item2;
                }
                else
                {
                    curPage = new dbAutoTrack.PDFWriter.Page(PageSize.A4, PageOrientation.Landscape);
                    doc.Pages.Add(curPage);//Add page to document
                    curY = 50;
                }

#region DigitalSignature

                //string dataPath = reportFullPath1;
                
                string certFilePath = "CertFiles\\2D086FEF-4D34-47BE-9C5E-0754CBB7D421.pfx";
                string dataPath = "";

                string protectedFilePath = ConfigurationManager.AppSettings[BIOME.Constants.Configuration.Server.AppSettings.ProtectedFilePath];
                dataPath = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, protectedFilePath, certFilePath));

                /*#if UAT
                                string protectedFilePath = ConfigurationManager.AppSettings[BIOME.Constants.Configuration.Server.AppSettings.ProtectedFilePath];
                                dataPath = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, protectedFilePath, certFilePath));
                #else
                                dataPath = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, certFilePath));
                #endif*/

                //dataPath += @"\data\";

                PDFDigitalSignature digitalSignature = new PDFDigitalSignature();
                //digitalSignature.SetX509Certificate(new X509Certificate2(dataPath, ConfigurationManager.AppSettings[BIOME.Constants.Configuration.Server.AppSettings.DigitalSignatureKey]));
                X509Certificate2 cert = new X509Certificate2(dataPath, ConfigurationManager.AppSettings[BIOME.Constants.Configuration.Server.AppSettings.DigitalSignatureKey], X509KeyStorageFlags.EphemeralKeySet);
                digitalSignature.SetX509Certificate(cert);

                digitalSignature.Date = DateTime.Now;
                digitalSignature.Location = "";
                digitalSignature.Reason = "";
                digitalSignature.ContactInfo = "ContactInfo";
                digitalSignature.DetachSignature = false;
                digitalSignature.RootTrusted = false;
                doc.DigitalSignature = digitalSignature;

                PDFFont _font1 = new PDFFont(StandardFonts.TimesRoman, FontStyle.Regular);
                graphics = curPage.Graphics;
                PdfSignatureField signatureField = graphics.AddDigitalSignature("name", new RectangleF(50, curY, 150, 30), _font1, 8f);
                signatureField.DigitalSignature = digitalSignature;

#endregion

                //Add PageFooter
                TextStyle txtStyle = new TextStyle(font2, 9);
                for (int pageno = 1, pageCount = doc.Pages.Count; pageno <= pageCount; pageno++)
                {
                    doc.Pages[pageno - 1].Graphics.DrawLine(50, 542, 785, 542, 1, LineStyle.Solid, PDFColor.LightGray);
                    doc.Pages[pageno - 1].Graphics.DrawTextBox(685, 544, 94, 36, string.Format("{0} | Page", pageno), txtStyle, TextAlignment.Right, null);
                }


                string fileName = @"Audit Log Monthly Report (" + sDate.ToString("MMM yyyy") + ").pdf";
                string savePath = reportFullPath + fileName;
                //byte[] docu = doc.Generate();
                //System.IO.File.WriteAllBytes(savePath, docu);

                if (File.Exists(savePath))
                {
                    System.GC.Collect();
                    System.GC.WaitForPendingFinalizers();
                    File.Delete(savePath);
                }
                using (FileStream _fs = new FileStream(savePath, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.ReadWrite))
                {
                    doc.Generate(_fs);
                }


                List<ApplicationUser> systemAdmins = UserService.GetUsers(new string[] { BIOME.Constants.UserRoles.SystemAdmin }).Where(t=>t.IsActive).OrderBy(t=>t.PersonName).ToList();
                List<string> sysAdminEmails = new List<string>();
                StringBuilder sbSysAdmin = new StringBuilder();
                int serialNo = 1;
                /*sbSysAdmin.Append("<table style='color:inhert;'>");
                foreach (ApplicationUser sysAdmin in systemAdmins) {
                    //emailService.SendAuditLogMonthlyReport(sysAdmin.PersonName, sDate.ToString("dd-MMM-yyyy"), eDate.ToString("dd-MMM-yyyy"), sysAdmin.Email, reportFullPath, fileName);
                    sysAdminEmails.Add(sysAdmin.Email);
                    sbSysAdmin.Append("</tr>");
                    sbSysAdmin.Append("<td>");
                    sbSysAdmin.Append(serialNo + ") ");
                    sbSysAdmin.Append("</td>");
                    sbSysAdmin.Append("<td>");
                    sbSysAdmin.Append(sysAdmin.PersonName);
                    sbSysAdmin.Append("</td>");
                    //sbSysAdmin.Append(serialNo + ") " + sysAdmin.PersonName + "<br/>");
                    sbSysAdmin.Append("</tr>");
                    serialNo++;
                }
                sbSysAdmin.Append("</table>");*/

                sbSysAdmin.Append("<div>");
                foreach (ApplicationUser sysAdmin in systemAdmins)
                {
                    sysAdminEmails.Add(sysAdmin.Email);
                    sbSysAdmin.Append(serialNo + ") " + sysAdmin.PersonName + "<br/>");
                    serialNo++;
                }
                sbSysAdmin.Append("</div>");

                string ccMail = "";
                ccMail = string.Join(",", sysAdminEmails);

                var recipients = systemParameterService.GetAuditLogMonthlyReportRecipients();
                if (!string.IsNullOrEmpty(recipients))
                {
                    List<string> recipList = recipients.Split(';').ToList();
                    foreach (string recipient in recipList)
                    {
                        ApplicationUser user = UserService.GetUserbyEmail(recipient.Trim());
                        if (user != null)
                            emailService.SendAuditLogMonthlyReport(user.PersonName, sDate.ToString("dd-MMM-yyyy"), eDate.AddDays(-1).ToString("dd-MMM-yyyy"), recipient.Trim(), reportFullPath, fileName, ccMail, sDate.ToString("MMM yyyy")
                                , sbSysAdmin.ToString());

                        /*if (!sysAdminEmails.Contains(recipient.Trim())) {
                            
                        }*/

                    }
                }

                if (cert != null)
                    cert.Dispose();

            
                if (File.Exists(savePath))
                    File.Delete(savePath);

                

                auditTrailService.LogAuditTrail(Enumerations.Audit.Module.AuditTrail, Enumerations.Audit.AuditLogAction.MonthlyReport_Job, "File Name:" + fileName);

            }
            catch (Exception ex)
            {
                var error = "Audit Log Monthly Report Failed: " + ex.Message;
                if (ex.InnerException != null)
                {
                    error = error + "\n InnerException Message: " + ex.InnerException.Message;
                    error = error + "\n InnerException StackTrace: " + ex.InnerException.StackTrace;

                    if (ex.InnerException.InnerException != null)
                    {
                        error = error + "\n InnerException InnerException Message: " + ex.InnerException.InnerException.Message;
                        error = error + "\n InnerException InnerException StackTrace: " + ex.InnerException.InnerException.StackTrace;
                    }
                }
                logger.Error(error);
                auditTrailService.LogAuditTrail(Enumerations.Audit.Module.AuditTrail, Enumerations.Audit.AuditLogAction.MonthlyReport_Error, error);

                throw ex; //throw error so that this job will be retried.
                //return ServiceResult.FailWithOutput(error);

            }
            return ServiceResult.Success;
        }

#region RenderSection
        private Tuple<float, dbAutoTrack.PDFWriter.Page> RenderSection(Document doc, dbAutoTrack.PDFWriter.Page curPage, float curY, string sectionTitle, List<KeyValuePair<string, Table>> listTables, PDFFont titleFont)
        {
            if (curPage == null)
            {
                //Initialize new page with PageSize A4
                curPage = new dbAutoTrack.PDFWriter.Page(PageSize.A4, PageOrientation.Landscape);

                //Add page to document
                doc.Pages.Add(curPage);

                curY = 50;
            }

            PDFGraphics graphics = curPage.Graphics;

            //section title
            graphics.DrawString(50, curY, sectionTitle, RGBColor.Black, titleFont, 10);
            curY += 30;

            Tuple<float, dbAutoTrack.PDFWriter.Page> res = null;
            foreach (KeyValuePair<string, Table> kv in listTables)
            {
                res = RenderTable(doc, curPage, kv.Value, curY, kv.Key, titleFont);
                if (res != null && res.Item1 <= 450) //check if there is space to start new table or else start on new page
                {   //res.Item1 <= 420 res.Item1 <= 620
                    curY = res.Item1 + 30;
                    curPage = res.Item2;
                }
                else
                {
                    curY = 50;
                    curPage = null;
                }
            }
            return res;
        }

        private Tuple<float, dbAutoTrack.PDFWriter.Page> RenderTable(Document doc, dbAutoTrack.PDFWriter.Page curPage, Table tbl, float curY, string tblTitle, PDFFont titleFont)
        {
            bool renderTitle = true;

            //title
            while (tbl != null)
            {
                if (curPage == null || curY > 435)
                {
                    curY = 50;
                    //Initialize new page with PageSize A4
                    curPage = new dbAutoTrack.PDFWriter.Page(PageSize.A4, PageOrientation.Landscape);

                    //Add page to document
                    doc.Pages.Add(curPage);
                }

                //Get the PDFGraphics object for drawing to the page.
                PDFGraphics graphics = curPage.Graphics;

                //render title
                if (renderTitle)
                {
                    renderTitle = false;
                    graphics.DrawString(50, curY, tblTitle, titleFont, 9);
                    curY += 10;
                }

                float tblHeight = tbl.Height;

                float clipHeight = (525 - curY);    //(775 - curY)

                //Overflow is return as Table
                //Loop until there is no overflow.
                tbl = graphics.DrawTable(50, curY, clipHeight, tbl);

                if (tbl == null)
                {
                    curY += tblHeight;
                }
                else
                {
                    curY = 75;
                    curPage = null;
                }
            }
            return new Tuple<float, dbAutoTrack.PDFWriter.Page>(curY, curPage);
        }

#endregion

       
        

#region Private Methods

        // Commented because Elastic Search libs have been removed.
        private ElasticClient InitialElasticSearch(string defalultIndex)
        {
            //var connectionPool = new SingleNodeConnectionPool(new Uri(!string.IsNullOrEmpty(elasticsearchServer) ? elasticsearchServer : Constants.Search.Elasticsearch.ServerURL));
            var connectionPool = new SingleNodeConnectionPool(new Uri(elasticsearchServer));
            /*var settings = new ConnectionSettings(connectionPool, sourceSerializer: (s,f) => new MyJsonNetSerializer() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore })
                .DefaultIndex(defalultIndex)
                .DisableDirectStreaming()
                .PrettyJson();*/

            var settings = new ConnectionSettings(connectionPool, sourceSerializer: (builtin, setting) => new JsonNetSerializer(
               builtin, setting,
               () => new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore }))
                .DefaultIndex(defalultIndex)
                .DisableDirectStreaming()
                .PrettyJson();
            settings.BasicAuthentication(System.Configuration.ConfigurationManager.AppSettings[Constants.Configuration.Server.AppSettings.ElasticsearchUserName],
                System.Configuration.ConfigurationManager.AppSettings[Constants.Configuration.Server.AppSettings.ElasticsearchPassword]);

            settings.ServerCertificateValidationCallback(CertificateValidations.AllowAll);
            ElasticClient Client = new ElasticClient(settings);

            return Client;

        }
        private void SwapAlias(string indexNameAlias, string indexOldNameAlias, ElasticClient Client, string CurrentIndexName)
        {
            //var indexExists = Client.IndexExists(indexNameAlias).Exists;
            var indexExists = Client.Indices.Exists(indexNameAlias).Exists;

            /*   Client.Alias(aliases =>
               {
                   if (indexExists)
                       aliases.Add(a => a.Alias(indexOldNameAlias).Index(indexNameAlias));

                   return aliases
                   .Remove(a => a.Alias(indexNameAlias).Index("*"))
                   .Add(a => a.Alias(indexNameAlias).Index(CurrentIndexName));
               });
   */
            Client.Indices.BulkAlias(aliases =>
            {
                if (indexExists)
                    aliases.Add(a => a.Alias(indexOldNameAlias).Index(indexNameAlias));

                return aliases
                .Remove(a => a.Alias(indexNameAlias).Index("*"))
                .Add(a => a.Alias(indexNameAlias).Index(CurrentIndexName));

            });

            /*   client.Indices.BulkAlias(aliases =>
               {
                   if (indexExists)
                   {
                       var oldIndices = client.GetIndicesPointingToAlias(CurrentAliasName);
                       var indexName = oldIndices.First().ToString();

                       //remove alias from live index
                       aliases.Remove(a => a.Alias(CurrentAliasName).Index("*"));
                   }
                   return aliases.Add(a => a.Alias(CurrentAliasName).Index(CurrentIndexName));
               });*/

            var oldIndices = Client.GetIndicesPointingToAlias(indexOldNameAlias)
            .OrderByDescending(name => name)
            .Skip(2);

            /*foreach (var oldIndex in oldIndices)
                Client.DeleteIndex(oldIndex);*/

            foreach (var oldIndex in oldIndices)
                Client.Indices.Delete(oldIndex);

        }
        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult IndexResourceDocumentRecords()
        {
            return IndexResourceDocumentRecords(0);
        }

        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult IndexResourceDocumentRecords(long ID)
        {

            int pageSize = 20;

            List<long> resourceDocuments = new List<long>();
            if (ID > 0)
            {
                resourceDocuments.Add(ID);
            }
            else
            {
                resourceDocuments = resourcesService.GetResourceDocumentForIndex();
            }
            int totalCount = resourceDocuments.Count();

            var pages = (int)Math.Round(Math.Ceiling(totalCount / (double)pageSize), MidpointRounding.AwayFromZero);

            for (int i = 0; i < pages; i++)
            {
                var skipCount = i * pageSize;
                var resourceDocumentBatchList = resourceDocuments.Skip(skipCount).Take(pageSize).ToList();

                foreach (var item in resourceDocumentBatchList)
                {
                    resourcesService.AddIndexForResourceDocument(item);
                }

            }

            return ServiceResult.Success;
            
        }
        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult Remove_And_ReAdd_ResourceDocToES(long id)
        {
            ElasticClient Client = InitialElasticSearch(Constants.Search.Elasticsearch.ResourceIndexName);

            var response = Client.Delete<ResourceViewModel.ResourceDocumentES>(id);
            if (response.IsValid)
            {
                IndexResourceDocumentRecords(id);
                return ServiceResult.SuccessWithOutput(id);
            }
            else
            {
                //throw error for hangfire to retry
                throw new InvalidOperationException("delete failed");
            }
        }
        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [DisableConcurrentExecution(30 * 60)]
        public ServiceResult ReSyncResources_ES(int? skip, int? size, int? year)
        {
            ElasticClient Client = InitialElasticSearch(Constants.Search.Elasticsearch.ResourceIndexName);


            int totalSynced = 0;
            //int totalCount = 0;
            //int pageSize = 100;

            using (ApplicationDbContext biome_db = new ApplicationDbContext())
            {
                var query = biome_db.ResourceDocuments.Where(b => b.AtResourceMetaData != null && b.CreatedAt.Year == year.Value);
                if (skip.HasValue && size.HasValue)
                    query = query.Skip(skip.Value).Take(size.Value);

                List<long> ResourceIDsToResync = query.Select(t => t.Id).ToList();

                foreach (long resId in ResourceIDsToResync)
                {
                    var es_exist = Client.DocumentExists<ResourceViewModel.ResourceDocumentES>(resId);

                    if (!es_exist.Exists)
                    {
                        resourcesService.AddIndexForResourceDocument(resId);
                        totalSynced++;
                    }
                    
                    
                }

            }


            return ServiceResult.SuccessWithOutput(totalSynced);
        }
        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        public ServiceResult IndexMapResourceDocumentRecords()
        {
            //return ServiceResult.SuccessWithOutput("Elastic Search Unavailable");

            //Display Map files in BIOME CR.

            //Commented because Elastic Search libs have been removed.
            var resourceDocuments = resourcesService.GetMapResourceDocumentForIndex();
            //var node = new Uri(ConfigurationManager.AppSettings[BIOME.Constants.Configuration.Server.AppSettings.ElasticesearchServer]);
            //var settings = new ConnectionSettings(node);
            //var client = new ElasticClient(settings);
            string protectedFilePath = ConfigurationManager.AppSettings[BIOME.Constants.Configuration.Server.AppSettings.ProtectedFilePath];
            string resFilePath = BIOME.Constants.Configuration.FilePath.Contents.Resource.ResourceFile.ToLocalPath();
            foreach (var item in resourceDocuments)
            {
                //var indexName = Path.GetFileNameWithoutExtension(item.FilePath);
                //if (client.IndexExists(indexName).Exists)
                //if (client.Indices.Exists(indexName).Exists)
                //  continue;

                string resourceDocumentPath = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, protectedFilePath, resFilePath, item.ServerFileName));
                if (item.AtResourceDocumentType != null && item.AtResourceDocumentType.DocumentTypeName == "Map")
                {
                    resourcesService.IndexMapDocument(item.Id, resourceDocumentPath);
                }
                else if (item.AtResourceStructureTemplate != null && item.AtResourceStructureTemplate.StructureTemplateType == Constants.Resource.ReourceType.StructuredDataGIS)
                {
                    resourcesService.IndexMapGIS_Document(item.Id, resourceDocumentPath, item.AtResourceStructureTemplate.ResourceStructureTemplateColumns.ToList());
                }
            }


            return ServiceResult.Success;
            

        }

        [AutomaticRetry(OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        public ServiceResult IndexMapResourceDocumentRecordsByID(long ID)
        {
            resourcesService.AddIndexForMapResourceDocument(ID);
            return ServiceResult.Success;

        }

        public ServiceResult GetResourceLayers()
        {
            var startIndex = ConfigurationManager.AppSettings["resourceLayer"].IndexOf('/') + 1;
            string groupName = ConfigurationManager.AppSettings["resourceLayer"].Substring(
                startIndex,
                ConfigurationManager.AppSettings["resourceLayer"].IndexOf('/', startIndex) - startIndex
                );

            var groups = migrationService.GetResourceGroups(groupName);

            var result = new List<MapLayerGroup>();
            foreach (var group in groups)
            {
                using (var w = new WebClient())
                {
                    var layerName = group.LayerName.Substring(0, group.LayerName.LastIndexOf('/') + 1);
                    string address = ConfigurationManager.AppSettings["arcgisUrl"] + "rest/services/" + layerName + "layers?f=json";
                    var json_data = w.DownloadString(address);
                    var arcgisLayer = !string.IsNullOrEmpty(json_data) ? JsonConvert.DeserializeObject<ArcGISLayerGroup>(json_data) : null;
                    if (arcgisLayer == null || arcgisLayer.layers == null)
                    {
                        continue;
                    }

                    migrationService.RemoveLayerGroup(groupName);
                    foreach (var layer in arcgisLayer.layers)
                    {
                        if (layer.type != "Feature Layer")
                            continue;
                        MapLayerGroup layerGroup = new MapLayerGroup();
                        layerGroup.GroupName = group.GroupName;
                        layerGroup.LayerName = layerName + layer.id;
                        layerGroup.HumanLayerName = layer.name;
                        switch (layer.geometryType)
                        {
                            case "esriGeometryPolygon":
                                layerGroup.GeometryType = "POLYGON";
                                break;
                            case "esriGeometryPoint":
                            case "esriGeometryMultipoint":
                                layerGroup.GeometryType = "POINT";
                                break;
                            case "esriGeometryPolyline":
                                layerGroup.GeometryType = "POLYLINE";
                                break;
                            default:
                                continue;
                        }

                        var alg = migrationService.AddLayerGroup(layerGroup);
                        if (alg) result.Add(layerGroup);
                    }
                }
            }

            return ServiceResult.SuccessWithOutput(result.Select(r => r.LayerName).ToList());
        }

        public ServiceResult GetMavenLayers()
        {
            try
            {
                MavenLayer mavenLayer = null;
                List<Service> services = new List<Service>();
                using (var w = new WebClient())
                {
                    string address = ConfigurationManager.AppSettings["arcgisUrl"] + "rest/services/maven?f=json";

                    var json_data = w.DownloadString(address);
                    mavenLayer = !string.IsNullOrEmpty(json_data) ? JsonConvert.DeserializeObject<MavenLayer>(json_data) : null;
                    if (mavenLayer == null || mavenLayer.services == null)
                    {
                        //throw new InvalidOperationException("Can't get maven layers from: " + address);
                        logger.Error("GetMavenLayers: Can't get maven layers from: " + address);
                    }

                    services.AddRange(mavenLayer.services);
                }

                migrationService.RemoveLayerGroup(null);

                foreach (var service in services)
                {
                    if (service.type != "MapServer")
                    {
                        // only process MapServer
                        continue;
                    }

                    string address = ConfigurationManager.AppSettings["arcgisUrl"] + "rest/services/" + service.name + "/MapServer/layers?f=json";
                    //logger.Info("Download layer address: " + address);
                    string json_data = string.Empty;
                    using (var w = new WebClient())
                    {
                        json_data = w.DownloadString(address);
                        //if (string.IsNullOrEmpty(json_data))
                        //    logger.Info("No content");
                        //else
                        //    logger.Info("Json content: " + json_data);
                    }
                    var g = !string.IsNullOrEmpty(json_data) ? JsonConvert.DeserializeObject<ArcGISLayerGroup>(json_data) : null;

                    if (g == null || g.layers == null)
                        continue;

                    foreach (var layer in g.layers)
                    {
                        if (layer.type != "Feature Layer")
                            continue;
                        MapLayerGroup layerGroup = new MapLayerGroup();
                        layerGroup.LayerName = service.name + "/MapServer/" + layer.id;
                        layerGroup.GroupName = layer.parentLayer == null ? service.name.Substring(6) : layer.parentLayer.name;
                        layerGroup.HumanLayerName = layer.name;
                        switch (layer.geometryType)
                        {
                            case "esriGeometryPolygon":
                                layerGroup.GeometryType = "POLYGON";
                                break;
                            case "esriGeometryPoint":
                            case "esriGeometryMultipoint":
                                layerGroup.GeometryType = "POINT";
                                break;
                            case "esriGeometryPolyline":
                                layerGroup.GeometryType = "POLYLINE";
                                break;
                            default:
                                continue;
                        }

                        migrationService.AddLayerGroup(layerGroup);
                    }
                }
            }
            catch (Exception ex) {

                logger.Error("GetMavenLayers: " + ex.Message);
                return ServiceResult.FailWithOutput(ex.Message);
                
            }
            
            return ServiceResult.Success;
        }


        //
        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult CleanUserAuthExpiredTicket()
        {
            string Output = "";
            try
            {
                //check created date to clear
                //var _formatter = new Microsoft.Owin.Security.DataHandler.TicketDataFormat(dataPro);
                DateTimeOffset now = DateTimeOffset.Now;
                using (ApplicationDbContext _store = new ApplicationDbContext())
                {
                    int ExpireTimeSpanInMinutes = Account.ExpireTimeSpanInMinutes;
                    _store.UserAuthSessionStore.Where(t => System.Data.Entity.DbFunctions.AddMinutes(t.CreatedAt, ExpireTimeSpanInMinutes) < now).ToList().ForEach(t => _store.UserAuthSessionStore.Remove(t));
                    _store.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                Output = Output + ex.Message;
            }
            return ServiceResult.SuccessWithOutput(Output);
        }

        

        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult UpdateUserStatus()
        {

            return ServiceResult.Success;
        }

        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult UpdateMember_e_sign_link_expire()
        {
            string Output = "";
            try
            {
               var newPermitCount = researchService.updateMember_eSign_Expire_NewPermit(null, 0);
               var amendRenewCount = researchService.updateMember_eSign_Expire_Renewal_Amemdment_Permit(null, 0);
               Output = newPermitCount.Result.Output.ToString() + "," + amendRenewCount.Result.Output.ToString();
                
            }
            catch (Exception ex)
            {
                Output = Output + ex.Message;
            }
            return ServiceResult.SuccessWithOutput(Output);
        }

        [Queue(BIOME.Constants.BackgroundJob.QueuePriority.app1)]
        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult SyncFilesApp1()
        {
            string Output = SyncFilesByAppServer(true);
            return ServiceResult.SuccessWithOutput(Output);
        }

        [Queue(BIOME.Constants.BackgroundJob.QueuePriority.app2)]
        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult SyncFilesApp2()
        {
            string Output = SyncFilesByAppServer(false);
            return ServiceResult.SuccessWithOutput(Output);
        }

        public string SyncFilesByAppServer(bool forInternet)
        {
            string Output = SyncFilesByFile(true, true, null);
            return Output ;
        }

        [Queue(BIOME.Constants.BackgroundJob.QueuePriority.app1)]
        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult SyncFilesForApp1ByFile(params long[] storeId)
        {
            string Output = SyncFilesByFile(true,false, storeId);
            return ServiceResult.SuccessWithOutput(Output);
        }

        [Queue(BIOME.Constants.BackgroundJob.QueuePriority.app2)]
        [DisableConcurrentExecution(5 * 60)]
        public ServiceResult SyncFilesForApp2ByFile(params long[] storeId)
        {
            string Output = SyncFilesByFile(false,false, storeId);
            return ServiceResult.SuccessWithOutput(Output);
        }

        public string SyncFilesByFile(bool forInternet,bool forAllFiles,params long[] storeId) {
            StringBuilder Output = new StringBuilder();
            try
            {
                using (ApplicationDbContext _store = new ApplicationDbContext())
                {
                    List<FileStoreTmp> filesToSync = null;
                    var fileQuery = _store.FileStoreTmps.AsQueryable();

                    if (forInternet)
                    {
                        if (forAllFiles)
                        {
                            filesToSync = fileQuery.Where(t => !t.hasDownloadedToInternetApp).ToList();
                        }
                        else
                        {
                            filesToSync = fileQuery.Where(t => !t.hasDownloadedToInternetApp && storeId.Contains(t.Id)).ToList();
                        }
                    }
                    else
                    {
                        if (forAllFiles)
                        {
                            filesToSync = fileQuery.Where(t => !t.hasDownloadedToIntranetApp).ToList();
                        }
                        else {
                            filesToSync = fileQuery.Where(t => !t.hasDownloadedToIntranetApp && storeId.Contains(t.Id)).ToList();
                        }
                            
                    }

                    
                    List<long> succededIDs = new List<long>();
                    foreach (FileStoreTmp file in filesToSync)
                    {
                        try
                        {
                            string fileName = file.FileName;
                            string subPath = "";
                            if (fileName.Contains("|")) {
                                var fileNameDistPath = fileName.Split('|');
                                if (fileNameDistPath.Length > 0) {
                                    fileName = fileNameDistPath[0];
                                }
                                if (fileNameDistPath.Length > 1)
                                {
                                    subPath = fileNameDistPath[1];
                                }

                            }

                            string dstfileName = "";
                            bool checkDstfileName = false;
                            if (forInternet)
                            {
                                dstfileName = Path.GetFullPath(fileName).Replace(Constants.Configuration.BIOMEUploadFileRootPathIntranet()
                                    , Constants.Configuration.BIOMEUploadFileRootPathInternet());

                                checkDstfileName = dstfileName.Contains(Constants.Configuration.BIOMEUploadFileRootPathInternet());
                            }
                            else
                            {
                                dstfileName = Path.GetFullPath(fileName).Replace(Constants.Configuration.BIOMEUploadFileRootPathInternet()
                                    , Constants.Configuration.BIOMEUploadFileRootPathIntranet());
                                checkDstfileName = dstfileName.Contains(Constants.Configuration.BIOMEUploadFileRootPathIntranet());
                                
                            }
                        //E:\Websites\Biome\biome-intranet2-ncodestaging.ntrix.sg\..\gccIntranet\UploadFiles\
                        //E:\Websites\Biome\gccIntranet\Content\User\ProfilePic\

                            if (checkDstfileName)
                            {
                                if (!File.Exists(dstfileName))
                                {
                                    File.WriteAllBytes(dstfileName, file.Data);
                                    succededIDs.Add(file.Id);
                                }
                                else
                                {
                                    succededIDs.Add(file.Id);
                                }

                                Output.Append("Source: " + fileName + ", Dest:" + dstfileName);

                            }
                            else {
                                Output.Append("Dest path wrong: Source: " + fileName + ", Dest:" + dstfileName);
                            }
                            


                            if (!subPath.IsNullOrEmpty())
                            {
                                string distSubPath = "";
                                if (forInternet)
                                {
                                    distSubPath = Path.GetFullPath(subPath).Replace(Constants.Configuration.BIOMEUploadFileRootPathIntranet()
                                        , Constants.Configuration.BIOMEUploadFileRootPathInternet());
                                }
                                else
                                {
                                    distSubPath = Path.GetFullPath(fileName).Replace(Constants.Configuration.BIOMEUploadFileRootPathInternet()
                                        , Constants.Configuration.BIOMEUploadFileRootPathIntranet());
                                }
                                string fileExt = System.IO.Path.GetExtension(dstfileName).ToLower();
                                if (fileExt == ".zip")
                                {
                                    extractGuideZipFile(dstfileName, distSubPath);
                                }
                            }

                            /*if (isAppsHostedInSameServer)
                            {

                                
                                if (forInternet)
                                {
                                    File.WriteAllBytes(fileName.Replace("biome-intranet2-ncodestaging.ntrix.sg", "biome-internet2-ncodestaging.ntrix.sg")
                                        .Replace(@"/gccIntranet", @"/gccInternet")
                                        .Replace(@"\gccIntranet", @"\gccInternet")
                                        , file.Data);
                                }
                                else
                                    File.WriteAllBytes(fileName.Replace("biome-internet2-ncodestaging.ntrix.sg", "biome-intranet2-ncodestaging.ntrix.sg")
                                         .Replace(@"/gccInternet", @"/gccIntranet")
                                        .Replace(@"\gccInternet", @"\gccIntranet")
                                        , file.Data);

                                succededIDs.Add(file.Id);
                            }
                            else
                            {

                            }*/




                        }
                        catch (Exception ex)
                        {
                            Output.Append(file.Id + ": " + ex.Message);
                        }

                    }

                    foreach (FileStoreTmp file in filesToSync.Where(t => succededIDs.Contains(t.Id)).ToList())
                    {
                        _store.FileStoreTmps.Remove(file);
                    }

                    _store.SaveChangesOnly();
                }
            }
            catch (Exception ex)
            {
                Output.Append(ex.Message);
                if (ex.InnerException != null)
                    Output.Append(ex.InnerException.Message);
            }
            return Output.ToString();
        }

        private void extractGuideZipFile(string realFileName,string subPath) {

            //BIOME.Constants.Configuration.ImagePath.Contents.Sighting.GuidePath;
            //var imagePath = BIOME.Constants.Configuration.ImagePath.Contents.Survey.SpeciesUpload + guideDetail.Title + "/";
            //string subPath = Server.MapPath(imagePath);

#if DEBUG
            subPath = subPath + "_copy";
#endif
            bool exists = System.IO.Directory.Exists(subPath);

            if (!exists)
                System.IO.Directory.CreateDirectory(subPath);
            else
            {
            }

            //System.IO.Compression.ZipFile.ExtractToDirectory(realFileName, subPath);
            using (var stream = System.IO.File.Open(realFileName, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite))
            {
                // Use stream
                System.IO.Compression.ZipArchive zipfile = new System.IO.Compression.ZipArchive(stream);
                ExtractToDirectory(zipfile, subPath, true);
            }



        }
        private void ExtractToDirectory(ZipArchive archive, string destinationDirectoryName, bool overwrite)
        {
            if (!overwrite)
            {
                archive.ExtractToDirectory(destinationDirectoryName);
                return;
            }

            foreach (ZipArchiveEntry file in archive.Entries)
            {

                string completeFileName = Path.Combine(destinationDirectoryName, file.FullName);
                if (file.Name == "")
                {// Assuming Empty for Directory
                    Directory.CreateDirectory(Path.GetDirectoryName(completeFileName));
                    continue;
                }
                // create dirs
                var dirToCreate = destinationDirectoryName;
                for (var i = 0; i < file.FullName.Split('/').Length - 1; i++)
                {
                    var s = file.FullName.Split('/')[i];
                    dirToCreate = Path.Combine(dirToCreate, s);
                    if (!Directory.Exists(dirToCreate))
                        Directory.CreateDirectory(dirToCreate);
                }
                file.ExtractToFile(completeFileName, true);


            }
        }


        #endregion
    }



    // Commented because Elastic Search libs have been removed.
    /* public class MyJsonNetSerializer : JsonNetSerializer
     {
         //JsonConverter[] converters = { new GroupConverter() };
         public MyJsonNetSerializer(IConnectionSettingsValues settings) : base(settings)
         {
         }

         protected override void ModifyJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings)
         {
             settings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
             //settings.Converters = converters;
         }
     }*/




    /*
        public class VanillaSerializer : IElasticsearchSerializer
        {
            public T Deserialize<T>(Stream stream) => throw new NotImplementedException();

            public object Deserialize(Type type, Stream stream) => throw new NotImplementedException();

            public Task<T> DeserializeAsync<T>(Stream stream, CancellationToken cancellationToken = default(CancellationToken)) =>
                throw new NotImplementedException();

            public Task<object> DeserializeAsync(Type type, Stream stream, CancellationToken cancellationToken = default(CancellationToken)) =>
                throw new NotImplementedException();

            public void Serialize<T>(T data, Stream stream, SerializationFormatting formatting = SerializationFormatting.Indented) =>
                throw new NotImplementedException();

            public Task SerializeAsync<T>(T data, Stream stream, SerializationFormatting formatting = SerializationFormatting.Indented,
                CancellationToken cancellationToken = default(CancellationToken)) =>
                throw new NotImplementedException();
        }
    */
#endregion


}
