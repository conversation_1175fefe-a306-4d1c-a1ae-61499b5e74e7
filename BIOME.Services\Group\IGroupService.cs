﻿using BIOME.Models;
using BIOME.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public interface IGroupService
    {
        Group GetGroupById(long id);
        Group GetHighestGroupFromList(IEnumerable<Group> groups);
        IEnumerable<UserGroupViewModel.SelectedUserGroupViewModel> GetGroupsHierarchy();
        Task<IEnumerable<UserGroupViewModel.SelectedUserGroupViewModel>> GetGroupsHierarchyForGroupAsync(long groupId);
        IEnumerable<UserGroupViewModel.SelectedUserGroupViewModel> GetGroupsHierarchyForGroup(Group group);
        IEnumerable<Group> GetAllChildrenGroups(IEnumerable<Group> groups);
        IEnumerable<Group> GetAllChildrenGroups(Group group);
        bool IsMainGroup(Group group);
        bool IsSubGroup(Group group);
        bool IsSubSubGroup(Group group);

        List<MainGroup> GetAllMainGroups();
        Group GetGroupByName(string groupName);
        MainGroup GetGroupByDomain(string domain);
        Task<ServiceResult> AddParentGroupAsync(long userId, UserGroupViewModel.UserGroupMainCreateViewModel createVM);
        Task<ServiceResult> AddSubGroupAsync(long userId, UserGroupViewModel.UserGroupSubCreateViewModel createVM);
        Task<ServiceResult> AddSubSubGroupAsync(long userId, UserGroupViewModel.UserGroupSubSubCreateViewModel createVM);

        Task<ServiceResult> EditParentGroupAsync(long userId, UserGroupViewModel.UserGroupMainEditViewModel editVM);
        Task<ServiceResult> EditSubGroupAsync(long userId, UserGroupViewModel.UserGroupSubEditViewModel editVM);
        Task<ServiceResult> EditSubSubGroupAsync(long userId, UserGroupViewModel.UserGroupSubSubEditViewModel editVM);
    }
}