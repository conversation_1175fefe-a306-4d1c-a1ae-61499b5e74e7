﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Autofac" version="6.3.0" targetFramework="net48" />
  <package id="Elasticsearch.Net" version="7.17.5" targetFramework="net48" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net452" />
  <package id="EntityFramework.DynamicFilters" version="1.4.10.1" targetFramework="net452" />
  <package id="GeoJSON.Net" version="0.1.47" targetFramework="net48" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.0" targetFramework="net48" />
  <package id="Microsoft.CSharp" version="4.6.0" targetFramework="net48" />
  <package id="NEST" version="7.17.5" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="5.0.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.4" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="5.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.2" targetFramework="net48" />
</packages>