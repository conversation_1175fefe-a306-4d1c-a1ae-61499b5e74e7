﻿using BIOME.ViewModels;
using BIOME.ViewModels.API.v1_0;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public interface IMaintenanceNoticeService
    {
        bool GetStatus();
        DateTimeOffset GetStartFrom();
        DateTimeOffset GetEndAt();
        string GetNoticeTitle();
        string GetNoticeMessage();
        MaintenanceNoticeViewModel GetMaintenanceNotice();
        ApiModelSystem.ResponseNotice GetAPIMaintenanceNotice();
        Task<ServiceResult> UpdateAndRefreshMaintenanceNotice(long userId,MaintenanceNoticeViewModel maintenanceNoticeVM);
    }
}