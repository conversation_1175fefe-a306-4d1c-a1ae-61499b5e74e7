﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace BIOME.Models
{
    public class LayerGroup
    {
        public string name { get; set; }
        public ArcGISLayerGroup group { get; set; }
    }

    public class ArcGISLayerGroup
    {
        public ArcGISLayer[] layers { get; set; }
        public object[] tables { get; set; }
    }

    public class ArcGISLayer
    {
        public float? currentVersion { get; set; }
        public int? id { get; set; }
        public string name { get; set; }
        public string type { get; set; }
        public string description { get; set; }
        public string geometryType { get; set; }
        public Sourcespatialreference sourceSpatialReference { get; set; }
        public string copyrightText { get; set; }
        public LayerInfo parentLayer { get; set; }
        public LayerInfo[] subLayers { get; set; }
        public int? minScale { get; set; }
        public int? maxScale { get; set; }
        public Drawinginfo drawingInfo { get; set; }
        public bool defaultVisibility { get; set; }
        public ArcGISExtent extent { get; set; }
        public bool hasAttachments { get; set; }
        public string htmlPopupType { get; set; }
        public string displayField { get; set; }
        public object typeIdField { get; set; }
        public ArcGISField[] fields { get; set; }
        public ArcGISIndex[] indexes { get; set; }
        public object[] relationships { get; set; }
        public bool canModifyLayer { get; set; }
        public bool canScaleSymbols { get; set; }
        public bool hasLabels { get; set; }
        public string capabilities { get; set; }
        public int? maxRecordCount { get; set; }
        public bool supportsStatistics { get; set; }
        public bool supportsAdvancedQueries { get; set; }
        public string supportedQueryFormats { get; set; }
        public Ownershipbasedaccesscontrolforfeatures ownershipBasedAccessControlForFeatures { get; set; }
        public bool useStandardizedQueries { get; set; }
        public Advancedquerycapabilities advancedQueryCapabilities { get; set; }
        public bool supportsDatumTransformation { get; set; }
    }

    public class LayerInfo
    {
        public long? id { get; set; }
        public string name { get; set; }
    }

    public class Sourcespatialreference
    {
        public int? wkid { get; set; }
        public int? latestWkid { get; set; }
    }

    public class Drawinginfo
    {
        public Renderer renderer { get; set; }
        public int? transparency { get; set; }
        public object labelingInfo { get; set; }
    }

    public class Renderer
    {
        public string type { get; set; }
        public string field1 { get; set; }
        public object field2 { get; set; }
        public object field3 { get; set; }
        public object defaultSymbol { get; set; }
        public object defaultLabel { get; set; }
        public Uniquevalueinfo[] uniqueValueInfos { get; set; }
        public string fieldDelimiter { get; set; }
    }

    public class Uniquevalueinfo
    {
        public ArcGISSymbol symbol { get; set; }
        public string value { get; set; }
        public string label { get; set; }
        public string description { get; set; }
    }

    public class ArcGISSymbol
    {
        public string type { get; set; }
        public string url { get; set; }
        public string imageData { get; set; }
        public string contentType { get; set; }
        public double width { get; set; }
        public double height { get; set; }
        public int? angle { get; set; }
        public int? xoffset { get; set; }
        public int? yoffset { get; set; }
        public string style { get; set; }
        public int[] color { get; set; }
    }

    public class ArcGISExtent
    {
        public float? xmin { get; set; }
        public float? ymin { get; set; }
        public float? xmax { get; set; }
        public float? ymax { get; set; }
        public ArcGISSpatialreference spatialReference { get; set; }
    }

    public class ArcGISSpatialreference
    {
        public int? wkid { get; set; }
        public int? latestWkid { get; set; }
    }

    public class Ownershipbasedaccesscontrolforfeatures
    {
        public bool allowOthersToQuery { get; set; }
    }

    public class Advancedquerycapabilities
    {
        public bool useStandardizedQueries { get; set; }
        public bool supportsStatistics { get; set; }
        public bool supportsOrderBy { get; set; }
        public bool supportsDistinct { get; set; }
        public bool supportsPagination { get; set; }
        public bool supportsTrueCurve { get; set; }
        public bool supportsReturningQueryExtent { get; set; }
        public bool supportsQueryWithDistance { get; set; }
        public bool supportsSqlExpression { get; set; }
    }

    public class ArcGISField
    {
        public string name { get; set; }
        public string type { get; set; }
        public string alias { get; set; }
        public int? length { get; set; }
        public object domain { get; set; }
    }

    public class ArcGISIndex
    {
        public string name { get; set; }
        public string fields { get; set; }
        public bool isAscending { get; set; }
        public bool isUnique { get; set; }
        public string description { get; set; }
    }

    public class MavenLayer
    {
        public float? currentVersion { get; set; }
        public object[] folders { get; set; }
        public Service[] services { get; set; }
    }

    public class Service
    {
        public string name { get; set; }
        public string type { get; set; }
    }

}