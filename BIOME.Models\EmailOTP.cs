﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace BIOME.Models
{
    public class EmailOTP : Entity<long>,IDescribableEntity
    {
        public long LoginUserId { get; set; }
        [NotMapped]
        public ApplicationUser LoginUser { get; set; }
        
        [MaxLength(320)]
        public string Email { get; set; }
        public string OTPCode { get; set; }
        public bool IsValid { get; set; }
        public DateTime OTPExpiredDate { get; set; }
        public DateTime OTPCreatedDate { get; set; }
        public string Describe()
        {
            return "{ LoginUserId : \"" + LoginUserId + "\", Email : \"" + Email + "\", OTPCode : \"" + OTPCode + "\", IsValid : \"" + IsValid
                + "\", OTPExpiredDate : \"" + OTPExpiredDate + "\", OTPCreatedDate : \"" + OTPCreatedDate   + "}";
        }
        public EmailOTP()
        {
        }

        public EmailOTP(long userId, string email, string code, DateTime dtCreatedDate)
        {
            LoginUserId = userId;
            Email = email;
            OTPCode = code;
            IsValid = true;
            OTPCreatedDate = dtCreatedDate;
            OTPExpiredDate = dtCreatedDate.AddMinutes(15);
        }
    }
}
