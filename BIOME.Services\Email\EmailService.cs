//using ActionMailerNext;
//using ActionMailerNext.Implementations;
//using ActionMailerNext.Implementations.SMTP;
//using ActionMailerNext.Standalone;
using BIOME.Models;
using BIOME.Models.EmailTemplates;
using BIOME.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Net.Mime;
using System.Text;
using System.Text.RegularExpressions;
using System.ComponentModel;
using System.Threading.Tasks;
using Utilities.Helpers;
using System.Configuration;
using Hangfire.Logging.LogProviders;
//using ActionMailerNext.Standalone.Helpers;
using SendGrid.Helpers.Mail.Model;
using System.IO;
using SendGrid;
using System.Net.Http;
using DocumentFormat.OpenXml.Wordprocessing;
//using SendGrid.Helpers.Mail;
//using System.Web.Mail;
//using Mandrill.Models;
//using Mandrill.Models;
//using Nest;
//using RestSharp.Extensions;
using Ganss.Xss;
using System.Web;

namespace BIOME.Services
{
    public class EmailService : ServiceBase, IEmailService
    {
        #region Fields

        //private readonly EmailController emailController;
        private readonly SmtpClient smtpClient;
        //private readonly SmtpMailSender smtpMailSender;
        private readonly ApplicationDbContext dbContext;
        private readonly IPageService pageService;
        private readonly ISystemParametersService systemParametersService;
        private readonly ITodoListService todoListService;


        private string internetEmailIP = "";
        private string intranetEmailIP = "";
        private string internetEmailBaseUrl = "";
        private string intranetEmailBaseUrl = "";
        private string internetEmailIPSecure = "";
        private string intranetEmailIPSecure = "";

        private log4net.ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private static readonly NLog.Logger NLogger = NLog.LogManager.GetCurrentClassLogger();
        #endregion

        #region Constructors

        public EmailService(
            ISystemParametersService systemParametersService,
            IPageService pageService,
            ApplicationDbContext dbContext,
            ITodoListService todoListService,
            string internetEmailIP,
            string intranetEmailIP,
            string internetEmailBaseUrl,
            string intranetEmailBaseUrl,
            string internetEmailIPSecure,
            string intranetEmailIPSecure

            )
        {
            //emailController = new EmailController();
            smtpClient = new SmtpClient();
            smtpClient.ConfigureActualPickupLocation();
            smtpClient.SendCompleted += new SendCompletedEventHandler(SendCompletedCallback);
            //smtpMailSender = new SmtpMailSender(smtpClient, emailController);
            this.pageService = pageService;
            this.systemParametersService = systemParametersService;
            this.dbContext = dbContext;
            this.todoListService = todoListService;

            this.internetEmailIP = internetEmailIP;
            this.intranetEmailIP = intranetEmailIP;
            this.internetEmailBaseUrl = internetEmailBaseUrl;
            this.intranetEmailBaseUrl = intranetEmailBaseUrl;
            this.internetEmailIPSecure = internetEmailIPSecure;
            this.intranetEmailIPSecure = intranetEmailIPSecure;

        }


        public string InternetEmailIP
        {
            get
            {
                return internetEmailIP;
            }
        }

        public string IntranetEmailIP
        {
            get
            {
                return intranetEmailIP;
            }
        }

        public string InternetEmailIPSecure
        {
            get
            {
                return internetEmailIPSecure;
            }
        }

        public string IntranetEmailIPSecure
        {
            get
            {
                return intranetEmailIPSecure;
            }
        }

        public string InternetEmailBaseUrl
        {
            get
            {
                return internetEmailBaseUrl;
            }
        }

        public string IntranetEmailBaseUrl
        {
            get
            {
                return intranetEmailBaseUrl;
            }
        }

        #endregion

        #region Public Methods

        public bool SendResetPassword(string title, string recipient, string resetPasswordLink, string contactUsLink)
        {
            //fix for issue NPARK/BIOME/NCODE/2020_0085
            var siteType = ConfigurationManager.AppSettings["SiteType"];
            string resetPasswordFullUrl = GetEnvironmentUrl(recipient, resetPasswordLink, siteType);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            if (resetPasswordFullUrl != null && resetPasswordFullUrl.Contains("email="))
            {
                resetPasswordFullUrl += "&code1=true";
            }

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<url_pub_reset_password>", $"<a href=\"{resetPasswordFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("RESET_PASSWORD", fields, recipient);
        }

        public bool SendPasswordResetSuccess(string title, string recipient, string contactUsLink)
        {
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<email_technical_support>", $"{pageService.GetContactInfo().TechEmail}");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("ACCOUNT_PASSWORD_RESET_SUCCESSFULLY", fields, recipient);
        }

        public bool SendActivateAccount(string title, string recipient, string activateAccountLink, string contactUsLink)
        {
            //fix for issue NPARK/BIOME/NCODE/2020_0085
            var siteType = ConfigurationManager.AppSettings["SiteType"];
            string activateAccountFullUrl = GetEnvironmentUrl(recipient, activateAccountLink, siteType);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink, siteType);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<email_address>", recipient);
            fields.Add("<url_pub_email_verification>", $"<a href=\"{activateAccountFullUrl}\">activate link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");
            fields.Add("<system_config_account_expiry_period>", $"{systemParametersService.GetAccountExpiryPeriod()}");
            fields.Add("<system_config_password_expiry_period>", $"{systemParametersService.GetPasswordExpiryPeriod()}");

            return SendEmailTemplate("NEW_ACCOUNT_ACTIVATION", fields, recipient);
        }

        public bool SendActivateAccountWithTempPassword(string title, string recipient, string tempPassword, string activateAccountLink, string contactUsLink)
        {
            //fix for issue NPARK/BIOME/NCODE/2020_0085
            var siteType = ConfigurationManager.AppSettings["SiteType"];
            string activateAccountFullUrl = GetEnvironmentUrl(recipient, activateAccountLink, siteType);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink, siteType);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<email_address>", recipient);
            fields.Add("<temp_password>", tempPassword);
            fields.Add("<url_pub_email_verification>", $"<a href=\"{activateAccountFullUrl}\">activate link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");
            fields.Add("<system_config_account_expiry_period>", $"{systemParametersService.GetAccountExpiryPeriod()}");
            fields.Add("<system_config_password_expiry_period>", $"{systemParametersService.GetPasswordExpiryPeriod()}");
            return SendEmailTemplate("NEW_ACCOUNT_ACTIVATION_WITH_TEMP_PASSWORD", fields, recipient);
        }

        public bool SendAccountLoginOTP(string title, string recipient, string optCode, string optExpiryDate, string contactUsLink)
        {
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<otp_code>", optCode);
            fields.Add("<otp_expiry_date>", optExpiryDate);
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");
            return SendEmailTemplate("ACCOUNT_LOGIN_OTP", fields, recipient);
        }

        public bool SendPasswordExpiry(string title, string recipient, string passwordExpiryDate, string passwordResetLink, string contactUsLink)
        {
            string passwordResetFullUrl = GetEnvironmentUrl(recipient, passwordResetLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            if (passwordResetFullUrl != null && passwordResetFullUrl.Contains("email="))
            {
                passwordResetFullUrl += "&code1=true";
            }

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<password_expiry_date>", passwordExpiryDate);
            fields.Add("<url_pub_password_reset>", $"<a href=\"{passwordResetFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl.ToPublicUrl()}\">contact us</a>");
            fields.Add("<system_config_password_expiry_period>", $"{systemParametersService.GetPasswordExpiryPeriod()}");

            return SendEmailTemplate("ACCOUNT_PASSWORD_EXPIRY_NOTIFICATION", fields, recipient);
        }

        public bool SendPasswordExpiryNotification(string title, string recipient, string passwordExpiryDate, string passwordResetLink, string contactUsLink)
        {
            string passwordResetFullUrl = GetEnvironmentUrl(recipient, passwordResetLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            if (passwordResetFullUrl != null && passwordResetFullUrl.Contains("email="))
            {
                passwordResetFullUrl += "&code1=true";
            }

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<password_expiry_date>", passwordExpiryDate);
            fields.Add("<url_pub_password_reset>", $"<a href=\"{passwordResetFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");
            fields.Add("<system_config_password_expiry_period>", $"{systemParametersService.GetPasswordExpiryPeriod()}");

            return SendEmailTemplate("ACCOUNT_PASSWORD_14-DAYS_BEFORE_EXPIRY_NOTIFICATION", fields, recipient);
        }

        public bool SendAccountExpiry(string title, string recipient, string accountExpiryDate, string accountResetLink, string contactUsLink)
        {
            //Fixes for NPARK/BIOME/NCODE/2020_0089.
            var siteType = ConfigurationManager.AppSettings["SiteType"];
            string accountResetFullUrl = GetEnvironmentUrl(recipient, accountResetLink, siteType);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink, siteType);

            if (accountResetFullUrl != null && accountResetFullUrl.Contains("email="))
            {
                accountResetFullUrl += "&code1=true";
            }

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<account_expiry_date>", accountExpiryDate);
            fields.Add("<url_pub_account_reset>", $"<a href=\"{accountResetFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");
            fields.Add("<system_config_account_expiry_period>", $"{systemParametersService.GetAccountExpiryPeriod()}");

            return SendEmailTemplate("ACCOUNT_EXPIRY_NOTIFICATION", fields, recipient);
        }

        public bool SendAccountExpiryFromScheduler(string title, string recipient, string accountExpiryDate, string accountResetLink, string contactUsLink)
        {
            //var siteType = ConfigurationManager.AppSettings["SiteType"];
            string accountResetFullUrl = GetEnvironmentUrl(recipient, accountResetLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            if (accountResetFullUrl != null && accountResetFullUrl.Contains("email="))
            {
                accountResetFullUrl += "&code1=true";
            }

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<account_expiry_date>", accountExpiryDate);
            fields.Add("<url_pub_account_reset>", $"<a href=\"{accountResetFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");
            fields.Add("<system_config_account_expiry_period>", $"{systemParametersService.GetAccountExpiryPeriod()}");

            return SendEmailTemplate("ACCOUNT_EXPIRY_NOTIFICATION_FROM_SCHEDULER", fields, recipient);
        }

        public bool SendAccountExpiryNotification(string title, string recipient, string accountExpiryDate, string accountResetLink, string contactUsLink)
        {
            //var siteType = ConfigurationManager.AppSettings["SiteType"];
            string accountResetFullUrl = GetEnvironmentUrl(recipient, accountResetLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            if (accountResetFullUrl != null && accountResetFullUrl.Contains("email="))
            {
                accountResetFullUrl += "&code1=true";
            }

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<account_expiry_date>", accountExpiryDate);
            fields.Add("<url_pub_account_reset>", $"<a href=\"{accountResetFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");
            fields.Add("<system_config_account_expiry_period>", $"{systemParametersService.GetAccountExpiryPeriod()}");

            return SendEmailTemplate("ACCOUNT_14-DAYS_BEFORE_EXPIRY_NOTIFICATION", fields, recipient);
        }

        public bool SendAccountResetSuccess(string title, string recipient, string loginLink, string contactUsLink)
        {
            string loginFullUrl = GetEnvironmentUrl(recipient, loginLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<url_Internet_user_login>", $"<a href=\"{loginFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("ACCOUNT_EXTENSION_NOTIFICATION", fields, recipient);
        }

       /* public bool SendAccountSuppendOrDeAactivate(string recipient, string UserID, string userName, string roles, string Datesuppend, string DateDeactive, string Status, bool isTransferred)
        {
            var fields = new Dictionary<string, string>();
            fields.Add("<user_id>", UserID);
            fields.Add("<user_name>", userName);
            fields.Add("<user_existingroles>", roles);
            fields.Add("<datesuppend>", Datesuppend);
            fields.Add("<datedeactive>", DateDeactive);
            fields.Add("<status>", Status);

            if (isTransferred)
                return SendEmailTemplate("ACCOUNT_STATUS_CHANGE", fields, recipient);
            else
                return SendEmailTemplate("ACCOUNT_STATUS_CHANGE_EXIT", fields, recipient);
        }*/
        /*public bool SendAccountChangeSummary(string recipient, string list_updated_users, string list_failed_toupdate_users, string list_users_not_exist, string ace_file_record_count, string errorSummary) //Account Log Audit CR
        {
            var fields = new Dictionary<string, string>();
            fields.Add("<ace_file_record_count>", ace_file_record_count);
            fields.Add("<list_users_not_exist>", list_users_not_exist);
            fields.Add("<list_updated_users>", list_updated_users);
            fields.Add("<list_failed_to_update_users>", list_failed_toupdate_users);
            fields.Add("<error_summary>", errorSummary);



            return SendEmailTemplate("ACCOUNT_STATUS_CHANGE_SUMMARY", fields, recipient);

        }*/

        public bool SendSightingVerified(string recipient, string personName, string sightingTitle, string sightingIdString, string detailLink, string contactUsLink)
        {
            string detailFullUrl = GetEnvironmentUrl(recipient, detailLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<sighting_title>", sightingTitle);
            fields.Add("<sightingid_link_url_pub_sightingdetails>", $"<a href=\"{detailFullUrl}\">{sightingIdString}</a>");
            fields.Add("<url_pub_sighting_details>", $"<a href=\"{detailFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("NOTIFICATION_OF_SIGHTING_VERIFIED", fields, recipient);
        }

        public bool SendNewCommentSighting(string recipient, string personName, string fullName, string profileLink, string sightingIdString, string detailLink, string comment, string contactUsLink)
        {
            string profileFullUrl = GetEnvironmentUrl(recipient, profileLink);
            string detailFullUrl = GetEnvironmentUrl(recipient, detailLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<sighting_id>", sightingIdString);
            fields.Add("<commenterName_link_url_commenterprofilepage>", $"<a href=\"{profileFullUrl}\">{fullName}</a>");
            fields.Add("<sightingid_link_url_pub_sightingdetails>", $"<a href=\"{detailFullUrl}\">{sightingIdString}</a>");
            fields.Add("<comment_details>", comment);
            fields.Add("<url_pub_sighting_details>", detailFullUrl);
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("NEW_COMMENT_ON_SIGHTING", fields, recipient);
        }

        public bool SendChangeSightingName(string recipient, string personName, string sightingTitle, string sightingIdString, string detailLink, string contactUsLink)
        {
            string detailFullUrl = GetEnvironmentUrl(recipient, detailLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<sighting_id>", sightingIdString);
            fields.Add("<sighting_title>", sightingTitle);
            fields.Add("<sightingid_link_url_pub_sightingdetails>", $"<a href=\"{detailFullUrl}\">{sightingIdString}</a>");
            fields.Add("<url_pub_sighting_details>", $"<a href=\"{detailFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("CHANGE_OF_SPECIES_NAME_AND_SCIENTIFIC_NAME_DUE_TO_VERIFICATION", fields, recipient);
        }

        public bool SendInappropriateSightingWarning(string recipient, string personName, string sighting_date_stamp, string sighting_time_stamp, string sightingTitle, string sightingDetailLink, string contactUsLink)
        {
            string sightingDetailFullUrl = GetEnvironmentUrl(recipient, sightingDetailLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<sighting_date_stamp>", sighting_date_stamp);
            fields.Add("<sighting_time_stamp>", sighting_time_stamp);
            fields.Add("<sightingtitle_link_sightingdetails>", $"<a href=\"{sightingDetailFullUrl}\">{sightingTitle}</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("INAPPROPRIATE_SIGHTING_WARNING_BLACKLISTING_TO_USER", fields, recipient);
        }

        public bool SendInappropriateSightingWarningAdmin(string recipient, string personName, string sighting_date_stamp, string sighting_time_stamp, string sightingTitle, string sightingDetailLink, string authorName, string authorProfileLink, string flaggerName, string flaggerProfileLink, string adminEditLink, string contactUsLink)
        {
            string sightingDetailFullUrl = GetEnvironmentUrl(recipient, sightingDetailLink);
            string authorProfileFullUrl = GetEnvironmentUrl(recipient, authorProfileLink);
            string flaggerProfileFullUrl = GetEnvironmentUrl(recipient, flaggerProfileLink);
            string adminEditFullUrl = GetEnvironmentUrl(recipient, adminEditLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<sighting_date_stamp>", sighting_date_stamp);
            fields.Add("<sighting_time_stamp>", sighting_time_stamp);
            fields.Add("<sightingtitle_link_sightingdetails>", $"<a href=\"{sightingDetailFullUrl}\">{sightingTitle}</a>");
            fields.Add("<sightingsarthorname_link_url_arthorprofilepage >", $"<a href=\"{authorProfileFullUrl}\">{authorName}</a>");
            fields.Add("<flaggername_link_url_flaggerprofilepage >", $"<a href=\"{flaggerProfileFullUrl}\">{flaggerName}</a>");
            fields.Add("<url_pri_inappropriate_sighting>", $"<a href=\"{adminEditFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("INAPPROPRIATE_SIGHTING_WARNING_BLACKLISTING_TO_ADMIN", fields, recipient);
        }

        public bool SendSightingCensored(string recipient, string personName, string sightingTitle, string sightingIdString, string detailLink, string contactUsLink)
        {
            string detailFullUrl = GetEnvironmentUrl(recipient, detailLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<sighting_id>", sightingIdString);
            fields.Add("<sighting_title>", sightingTitle);
            fields.Add("<sightingid_link_url_pub_sightingdetails>", $"<a href=\"{detailFullUrl}\">{sightingIdString}</a>");
            fields.Add("<url_pub_sighting_details>", detailFullUrl);
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("NOTIFICATION_OF_SIGHTING_CENSORED_AUTO-FILTERING", fields, recipient);
        }

        public bool SendAccountBlacklisted(string recipient, string personName, string mySightingLink, string contactUsLink)
        {
            string mySightingFullUrl = GetEnvironmentUrl(recipient, mySightingLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<sightings_link_url_mysightings>", $"<a href=\"{mySightingFullUrl}\">sightings</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("ACCOUNT_BLACKLISTED", fields, recipient);
        }

        public bool SendNewFollower(string title, string recipient, int numberOfFollowers, string profileLink, string contactUsLink)
        {
            string profileFullUrl = GetEnvironmentUrl(recipient, profileLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<number_of_followers>", numberOfFollowers.ToString());
            fields.Add("<url_pub_my_profile_page>", $"<a href=\"{profileFullUrl}\">{profileFullUrl}</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("NEW_FOLLOWER", fields, recipient);
        }

        public bool SendRequestProject(string recipient, string personName, string projectTitle, string detailLink, string adminProjectLink, string applicantFullname, string contactUsLink)
        {
            string detailFullUrl = GetEnvironmentUrl(recipient, detailLink);
            string adminProfileFullUrl = GetEnvironmentUrl(recipient, adminProjectLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<project_name>", projectTitle);
            fields.Add("<projectname_link_url_pub_projectdetails>", $"<a href=\"{detailFullUrl}\">{projectTitle}</a>");
            fields.Add("<project_requestor_name>", applicantFullname);
            fields.Add("<url_pub_manage_projects>", $"<a href=\"{adminProfileFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("REQUEST_FOR_PROJECT_SENT_TO_SYSTEM_ADMIN", fields, recipient);
        }

        public bool SendApproveMembership(string recipient, string personName, string status, string projectTitle, string projectIdString, string detailLink, string ownerFullname, string ownerEmail, string contactUsLink)
        {
            string detailFullUrl = GetEnvironmentUrl(recipient, detailLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<membership_status>", status);
            fields.Add("<project_id>", projectIdString);
            fields.Add("<projectname_link_url_pub_projectdetails>", $"<a href=\"{detailFullUrl}\">{projectTitle}</a>");
            fields.Add("<project_manager_name>", ownerFullname);
            fields.Add("<project_manager_email>", ownerEmail);
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("NOTIFICATION_ON_APPROVED/REJECTED_MEMBERSHIP", fields, recipient);
        }

        public bool SendJoinProject(string recipient, string personName, string projectTitle, string projectIdString, string detailLink, string profileLink, string applicantFullname, string contactUsLink)
        {
            string detailFullUrl = GetEnvironmentUrl(recipient, detailLink);
            string profileFullUrl = GetEnvironmentUrl(recipient, profileLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<project_id>", projectIdString);
            fields.Add("<project_name>", projectTitle);
            fields.Add("<newmembername_link_url_pub_newmemberprofilepage>", $"<a href=\"{profileFullUrl}\">{applicantFullname}</a>");
            fields.Add("<projectname_link_url_pub_projectdetails>", $"<a href=\"{detailFullUrl}\">{projectTitle}</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("JOIN_PROJECT_SENT_TO_PROJECT_REQUESTOR", fields, recipient);
        }

        public bool SendSuspendProject(string recipient, string personName, string projectIdString, string projectTitle, string projectStatus, string detailLink, string myProjectsLink, string contactUsLink)
        {
            string detailFullUrl = GetEnvironmentUrl(recipient, detailLink);
            string myProjectsFullUrl = GetEnvironmentUrl(recipient, myProjectsLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<project_id>", projectIdString);
            fields.Add("<project_name>", projectTitle);
            fields.Add("<project_status>", projectStatus);
            fields.Add("<projectname_link_url_pub_projectdetails>", $"<a href=\"{detailFullUrl}\">{projectTitle}</a>");
            fields.Add("<url_pub_my_projects>", myProjectsFullUrl);
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("SUSPEND_PROJECT_SENT_TO_PROJECT_ADMIN", fields, recipient);
        }

        public bool SendApprovalProject(string recipient, string personName, string projectIdString, string projectTitle, string projectStatus, string detailLink, string myProjectsLink, string contactUsLink)
        {
            string detailFullUrl = GetEnvironmentUrl(recipient, detailLink);
            string myProjectsFullUrl = GetEnvironmentUrl(recipient, myProjectsLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<project_id>", projectIdString);
            fields.Add("<project_name>", projectTitle);
            fields.Add("<project_status>", projectStatus);
            fields.Add("<projectname_link_url_pub_projectdetails>", $"<a href=\"{detailFullUrl}\">{projectTitle}</a>");
            fields.Add("<url_pub_my_projects>", $"<a href=\"{myProjectsFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("APPROVAL/REJECTION_OF_PROJECT_SENT_TO_REQUESTER", fields, recipient);
        }

        public bool SendReinstateProject(string recipient, string personName, string projectIdString, string projectTitle, string projectStatus, string detailLink, string myProjectsLink, string contactUsLink)
        {
            string detailFullUrl = GetEnvironmentUrl(recipient, detailLink);
            string myProjectsFullUrl = GetEnvironmentUrl(recipient, myProjectsLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<project_id>", projectIdString);
            fields.Add("<project_name>", projectTitle);
            fields.Add("<project_status>", projectStatus);
            fields.Add("<projectname_link_url_pub_projectdetails>", $"<a href=\"{detailFullUrl}\">{projectTitle}</a>");
            fields.Add("<url_pub_my_projects>", myProjectsFullUrl);
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("REINSTATE_PROJECT_SENT_TO_PROJECT_ADMIN", fields, recipient);
        }

        public bool SendProjectInviteMember(string recipient, string personName, string projectTitle, string projectIdString, string detailLink, string joinLink, string contactUsLink, long recipientId, long audit_userId)
        {
            string detailFullUrl = GetEnvironmentUrl(recipient, detailLink);
            string joinFullUrl = GetEnvironmentUrl(recipient, joinLink);
            string contactUsFullUrl = GetEnvironmentUrl(recipient, contactUsLink);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<project_id>", projectIdString);
            fields.Add("<url_pub_join_project>", $"<a href=\"{joinFullUrl}\">{joinFullUrl}</a>");
            fields.Add("<projectname_link_url_pub_projectdetails>", $"<a href=\"{detailFullUrl}\">{projectTitle}</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            ToDoListViewModel.ToDoListUIViewModel toDoListUIViewModel = new ToDoListViewModel.ToDoListUIViewModel();
            toDoListUIViewModel.UserId = recipientId;
            toDoListUIViewModel.TaskType = (int)BIOME.Enumerations.ToDoListing.TaskType.INVITATION_TO_JOIN_PROJECT_NON_SEARCHABLE_PROJECTS;
            toDoListUIViewModel.IsCompleted = false;
            toDoListUIViewModel.Link = joinLink;
            toDoListUIViewModel.ObjectId = long.Parse(projectIdString);

            todoListService.AddToDoList(toDoListUIViewModel, audit_userId);

            return SendEmailTemplate("INVITATION_TO_JOIN_PROJECT_NON-SEARCHABLE-PROJECTS", fields, recipient);
        }

        //TBC - move link to controller

        public bool SendPermitApplicationNotificationToSiteManager(string title, string recipient, string applicationType, string applicationId, string researchTitle, string applicantName, string applicationUrl, string contactUsUrl)
        {
            var applicationFullUrl = GetEnvironmentUrl(recipient, new Uri(applicationUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<type_of_application>", applicationType);
            fields.Add("<research_permit_title>", researchTitle);
            fields.Add("<permit_application_id>", applicationId);
            fields.Add("<applicant_name>", applicantName);
            fields.Add("<url_pri_new_permit_application>", $"<a href=\"{applicationFullUrl}\">this Link</a>"); //{applicationUrl} for shorten url
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("PERMIT_APPLICATION_NOTIFICATION_TO_SITE_MANAGER", fields, recipient);
        }

        public bool SendPermitApplicationNotificationToApplicant(string title, string recipient, string applicationType, string applicationId, string applicationStatus, string researchTitle, string applicationUrl, string contactUsUrl)
        {
            var applicationFullUrl = GetEnvironmentUrl(recipient, new Uri(applicationUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            System.Globalization.TextInfo textInfo = new System.Globalization.CultureInfo("en-US", false).TextInfo;
            applicationType = textInfo.ToTitleCase(applicationType.ToLower());

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<type_of_application>", applicationType);
            fields.Add("<research_permit_title>", researchTitle);
            fields.Add("<permit_application_id>", applicationId);
            fields.Add("<status>", applicationStatus);
            fields.Add("<url_pub_my_permit_application>", $"<a href=\"{applicationFullUrl}\">this Link</a>");
            fields.Add("<url_pri_new_permit_application>", $"<a href=\"{applicationFullUrl}\">this Link</a>");
            fields.Add("<url_pub_contact_us>", contactUsFullUrl);

            return SendEmailTemplate("RESEARCH_PERMIT_APPLICATION_STATUS_TO_APPLICANT", fields, recipient);
        }

        public bool SendPermitApplicationNotificationToPermitManagerOrSiteManager(string title, string recipient, string applicationType, string applicationId, string applicationStatus, string researchTitle, string applicationUrl, string contactUsUrl)
        {
            var applicationFullUrl = GetEnvironmentUrl(recipient, new Uri(applicationUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            System.Globalization.TextInfo textInfo = new System.Globalization.CultureInfo("en-US", false).TextInfo;
            applicationType = textInfo.ToTitleCase(applicationType.ToLower());

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<type_of_application>", applicationType);
            fields.Add("<research_permit_title>", researchTitle);
            fields.Add("<permit_application_id>", applicationId);
            fields.Add("<status>", applicationStatus);
            fields.Add("<url_pub_my_permit_application>", $"<a href=\"{applicationFullUrl}\">this Link</a>");
            fields.Add("<url_pri_new_permit_application>", $"<a href=\"{applicationFullUrl}\">this Link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">This Link</a>");

            return SendEmailTemplate("RESEARCH_PERMIT_APPLICATION_STATUS_TO_SITE_MANAGER/PERMIT_MANAGER", fields, recipient);
        }

        public bool SendPermitApplicationNotificationToPermitManager(string title, string recipient, string applicationType, string applicationId, string applicationSummary, string researchTitle, string applicantName, string applicationUrl, string contactUsUrl, string attachmentFilePath, string miscAttachment = "")
        {
            var applicationFullUrl = GetEnvironmentUrl(recipient, new Uri(applicationUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<type_of_application>", applicationType);
            fields.Add("<research_permit_title>", researchTitle);
            fields.Add("<permit_application_id>", applicationId);
            fields.Add("<applicant_name>", applicantName);
            fields.Add("<new_permit_application_summary>", applicationSummary);
            fields.Add("<url_pri_new_permit_application>", $"<a href=\"{applicationFullUrl}\">this Link</a>"); //shorten url
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            System.Net.Mail.Attachment data = new System.Net.Mail.Attachment(attachmentFilePath, MediaTypeNames.Application.Pdf);
            var attachments = new List<System.Net.Mail.Attachment>()
            {
                data
            };
            if (!miscAttachment.Equals(string.Empty)) attachments.Add(new System.Net.Mail.Attachment(miscAttachment));
            
            return SendEmailTemplate("PERMIT_APPLICATION_NOTIFICATION_TO_RESEARCH_PERMIT_MANAGER", fields, recipient, attachments);
        }

        public bool SendPermitApplicationSiteApprovedToPermitManager(string title, string recipient, string applicationType, string applicationId, string researchTitle, string siteManagerName, string nameOfSites, string applicationUrl, string contactUsUrl)
        {
            var applicationFullUrl = GetEnvironmentUrl(recipient, new Uri(applicationUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            System.Globalization.TextInfo textInfo = new System.Globalization.CultureInfo("en-US", false).TextInfo;
            applicationType = textInfo.ToTitleCase(applicationType.ToLower());

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<type_of_application>", applicationType);
            fields.Add("<research_permit_title>", researchTitle);
            fields.Add("<permit_application_id>", applicationId);
            fields.Add("<site_managers_name>", siteManagerName);
            fields.Add("<name(s) of site(s)>", nameOfSites);
            fields.Add("<url_pri_new_permit_application>", $"<a href=\"{applicationFullUrl}\">this Link</a>"); //{applicationUrl}
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("SITE_MANAGER_HAS_APPROVED_THE_APPLICATION_TO_RESEARCH_PERMIT_MANAGER", fields, recipient);
        }

        public bool SendPermitApplicationApprovedToApplicant(string title, string recipient, string researchTitle, string applicationId, string permitUrl, string attachmentFilePath, string passFilePath, string applicationUrl, string contactUsUrl)
        {
            var applicationFullUrl = GetEnvironmentUrl(recipient, new Uri(applicationUrl).PathAndQuery);
            var permitFullUrl = GetEnvironmentUrl(recipient, new Uri(permitUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<permitapplicationid_link_url_permitapplication>", $"<a href=\"{permitFullUrl}\">{applicationId}</a>");
            fields.Add("<research_permit_title>", researchTitle);
            fields.Add("<permit_application_id>", applicationId);
            fields.Add("<url_pub_permit_application>", $"<a href=\"{applicationUrl}\">{applicationFullUrl}</a>");
            fields.Add("<url_pub_permit>", $"<a href=\"{permitFullUrl}\">This Link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            Attachment data = new Attachment(attachmentFilePath, MediaTypeNames.Application.Pdf);
            data.Name = "PermitLetter_" + applicationId.Replace('/', '_') + ".pdf";
            Attachment passData = new Attachment(passFilePath, MediaTypeNames.Application.Pdf);
            passData.Name = "PermitPass_" + applicationId.Replace('/', '_') + ".pdf";
            var attachments = new List<Attachment>()
            {
                data,
                passData
            };

            return SendEmailTemplate("ISSUANCE_OF_PERMIT_TO_RESEARCH_PERMIT_APPLICANT", fields, recipient, attachments);
        }

        public bool SendPermitApplicationAmendedToApplicant(string title, string recipient, string researchTitle, string applicationId, string permitUrl, string attachmentFilePath, string passFilePath, string applicationUrl, string contactUsUrl, string applicationType)
        {
            var applicationFullUrl = GetEnvironmentUrl(recipient, new Uri(applicationUrl).PathAndQuery);
            var permitFullUrl = GetEnvironmentUrl(recipient, new Uri(permitUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<applicant_name>", title);
            fields.Add("<type_of_application>", applicationType);
            fields.Add("<permitapplicationid_link_url_permitapplication>", $"<a href=\"{permitFullUrl}\">{applicationId}</a>");
            fields.Add("<research_permit_title>", researchTitle);
            fields.Add("<permit_application_id>", applicationId);
            fields.Add("<url_pub_my_permit_application>", $"<a href=\"{applicationFullUrl}\">this link</a>");
            fields.Add("<url_pub_permit>", $"<a href=\"{permitFullUrl}\">{permitFullUrl}</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            Attachment data = new Attachment(attachmentFilePath, MediaTypeNames.Application.Pdf);
            data.Name = "PermitLetter_" + applicationId.Replace('/', '_') + ".pdf";
            Attachment passData = new Attachment(passFilePath, MediaTypeNames.Application.Pdf);
            passData.Name = "PermitPass_" + applicationId.Replace('/', '_') + ".pdf";
            var attachments = new List<Attachment>()
            {
                data,
                passData
            };

            return SendEmailTemplate("RESEARCH_PERMIT/PASS_AMENDED_TO_APPLICANT", fields, recipient, attachments);
        }

        public bool SendReminderToApplicant(string title, string recipient, string researchTitle, string applicationId, string permitExpiryDate, string permitUrl, string contactUsUrl)
        {
            var permitFullUrl = GetEnvironmentUrl(recipient, new Uri(permitUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<research_permit_title>", researchTitle);
            fields.Add("<permit_expiry_date>", permitExpiryDate);
            fields.Add("<permit_application_id>", applicationId);
            fields.Add("<url_pub_permit>", $"<a href=\"{permitFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("REMINDER_TO_APPLICANT", fields, recipient);
        }

        public bool SendRequestSurvey(string recipient, string personName, string surveyTitle, string detailUrl, string adminSurveyUrl, string applicantFullname, string contactUsUrl)
        {
            var detailFullUrl = GetEnvironmentUrl(recipient, new Uri(detailUrl).PathAndQuery);
            var adminSurveyFullUrl = GetEnvironmentUrl(recipient, new Uri(adminSurveyUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<survey_name>", surveyTitle);
            fields.Add("<surveyname_link_url_pub_surveydetails>", $"<a href=\"{detailFullUrl}\">{surveyTitle}</a>");
            fields.Add("<survey_requestor_name>", applicantFullname);
            fields.Add("<url_pub_manage_surveys>", $"<a href=\"{adminSurveyFullUrl}\">this Link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("REQUEST_FOR_SURVEY_SENT_TO_SYSTEM_ADMIN", fields, recipient);
        }

        public bool SendAmendmentPendingSurveyToSystemAdmin(string recipient, string personName, string surveyTitle, string survey_requestor_name, string survey_requestor_email, string amendment_submitted_by_name, string amendment_submitted_by_email, string contactUsUrl)
        {
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<survey_name>", surveyTitle);
            fields.Add("<survey_owner_name>", survey_requestor_name);
            fields.Add("<survey_owner_email>", survey_requestor_email);
            fields.Add("<amendment_submitted_by_name>", amendment_submitted_by_name);
            fields.Add("<amendment_submitted_by_email>", amendment_submitted_by_email);
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("AMENDMENT_PENDING_OF_SURVEY_SENT_TO_SYSTEM_ADMIN", fields, recipient);
        }

        public bool SendApproveOrRejectSurvey(string recipient, string personName, string surveyTitle, string surveyIdString, string surveyStatus, string detailUrl, string adminSurveyUrl, string contactUsUrl)
        {
            var detailFullUrl = GetEnvironmentUrl(recipient, new Uri(detailUrl).PathAndQuery);
            var adminSurveyFullUrl = GetEnvironmentUrl(recipient, new Uri(adminSurveyUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);
            
            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<survey_id>", surveyIdString);           
            fields.Add("<survey_status>", surveyStatus);
            fields.Add("<survey_name>", surveyTitle);
            fields.Add("<surveyName_link_url_pub_surveydetails>", $"<a href=\"{detailFullUrl}\">{surveyTitle}</a>");           
            fields.Add("<url_pub_my_surveys>", $"<a href=\"{adminSurveyFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("APPROVAL/REJECTION_OF_SURVEY_SENT_TO_REQUESTER", fields, recipient);
        }
        //Survey CR 2023 #4
        public bool SendApproveOrRejectAmendmentSurveyToMember(string recipient, string personName, string surveyTitle, string surveyIdString, string surveyStatus, string contactUsUrl,string survey_requestor_name,string survey_requestor_email, string survey_admin_list) 
        {
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<survey_id>", surveyIdString);
            fields.Add("<survey_status>", surveyStatus);
            fields.Add("<survey_name>", surveyTitle);
            fields.Add("<survey_owner_name>", survey_requestor_name);
            fields.Add("<survey_owner_email>", survey_requestor_email);
            fields.Add("<survey_admin_list>", survey_admin_list);
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("APPROVAL/REJECTION_OF_AMENDMENT_SURVEY_SENT_TO_MEMBER", fields, recipient);
        }

        //Survey CR 2023 #4
        public bool SendAmendmentPendingSurveyToMember(string recipient, string personName, string surveyTitle, string surveyIdString, string contactUsUrl, string survey_requestor_name, string survey_requestor_email, string survey_admin_list)
        {
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<survey_id>", surveyIdString);
            fields.Add("<survey_name>", surveyTitle);
            fields.Add("<survey_owner_name>", survey_requestor_name);
            fields.Add("<survey_owner_email>", survey_requestor_email);
            fields.Add("<survey_admin_list>", survey_admin_list);
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("AMENDMENT_PENDING_OF_SURVEY_SENT_TO_MEMBER", fields, recipient);
        }

        public bool SendAdminEditSurvey(string recipient, string personName, string surveyTitle, string detailUrl, string adminSurveyUrl, string contactUsUrl, bool isOwner, string edited_by_sysadmin_name, string edited_by_sysadmin_email)
        {
            var detailFullUrl = GetEnvironmentUrl(recipient, new Uri(detailUrl).PathAndQuery);
            var adminSurveyFullUrl = GetEnvironmentUrl(recipient, new Uri(adminSurveyUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);
            
            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);           
            fields.Add("<survey_name>", surveyTitle);
            fields.Add("<edited_by_sysadmin_name>", edited_by_sysadmin_name);
            fields.Add("<edited_by_sysadmin_email>", edited_by_sysadmin_email);
            fields.Add("<Surveyname_link_url_pub_surveydetails>", $"<a href=\"{detailFullUrl}\">{surveyTitle}</a>");
            fields.Add("<url_pub_my_surveys>", $"<a href=\"{adminSurveyFullUrl}\">this link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            if(isOwner)
            {
                return SendEmailTemplate("ADMIN_EDIT_OF_SURVEY_SENT_TO_OWNER", fields, recipient);
            }

            return SendEmailTemplate("ADMIN_EDIT_OF_SURVEY_SENT_TO_OTHER_ADMIN", fields, recipient);
        }

        public bool SendSurveyInviteMember(string recipient, string personName, string surveyTitle, string surveyIdString, string detailUrl, string joinUrl, string contactUsUrl,long recipientId, long audit_userId)
        {
            var detailFullUrl = GetEnvironmentUrl(recipient, new Uri(detailUrl).PathAndQuery);
            var joinFullUrl = GetEnvironmentUrl(recipient, new Uri(joinUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);
            
            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<survey_id>", surveyIdString);
            fields.Add("<url_pub_join_survey>", $"<a href=\"{joinFullUrl}\">this link</a>");
            fields.Add("<surveyname_link_url_pub_surveydetails>", $"<a href=\"{detailFullUrl}\">{surveyTitle}</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            ToDoListViewModel.ToDoListUIViewModel toDoListUIViewModel = new ToDoListViewModel.ToDoListUIViewModel();
            toDoListUIViewModel.UserId = recipientId;
            toDoListUIViewModel.TaskType = (int)BIOME.Enumerations.ToDoListing.TaskType.INVITATION_TO_JOIN_PROJECT_NON_SEARCHABLE_SURVEYS;
            toDoListUIViewModel.IsCompleted = false;
            toDoListUIViewModel.Link = new Uri(joinUrl).PathAndQuery;
            toDoListUIViewModel.ObjectId = long.Parse(surveyIdString);

            todoListService.AddToDoList(toDoListUIViewModel, audit_userId);

            return SendEmailTemplate("INVITATION_TO_JOIN_PROJECT_NON-SEARCHABLE-SURVEYS", fields, recipient);
        }

        public bool SendSiteVisitNotificationToSiteManager(string researchPermitId, string researchPermitTitle, string title, string recipient, string permitApplicationUrl, string applicantName, string siteVisitDetail, string siteVisitUrl, string contactUsUrl)
        {
            var permitApplicationFullUrl = GetEnvironmentUrl(recipient, new Uri(permitApplicationUrl).PathAndQuery);
            var siteVisitFullUrl = GetEnvironmentUrl(recipient, new Uri(siteVisitUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<permit_application_id>", researchPermitId);
            fields.Add("<research_permit_title>", researchPermitTitle);
            fields.Add("<person_name>", title);
            fields.Add("<permitapplicationid_link_url_pri_permit>", $"<a href=\"{permitApplicationFullUrl}\">" + researchPermitTitle + "</a>");
            fields.Add("<applicant_name>", applicantName);
            fields.Add("<site_visit_details>", siteVisitDetail);
            fields.Add("<url_pri_site_visit>", $"<a href=\"{siteVisitFullUrl}\">this Link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("REQUEST_FOR_SITE_VISIT", fields, recipient);
        }

        public bool SendDiscussionNotificationEmail(string title, string recipient, string newQuestionLink, string newQuestion, string nameOfUserWhoPosted, string permitApplicationUrl, string contactUsUrl, string researchPermitTitle, string permiteApplicationId, bool isApplicant
            , List<BIOME.ViewModels.DiscussionForumViewModel.DiscussionForumAttachDocumentModel> attachDocumentVMList)
        {
            var permitApplicationFullUrl = GetEnvironmentUrl(recipient, new Uri(permitApplicationUrl).PathAndQuery);
            var newQuestionFullLink = GetEnvironmentUrl(recipient, new Uri(newQuestionLink).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);
            
            var fields = new Dictionary<string, string>();
            fields.Add("<research_permit_title>", researchPermitTitle);
            fields.Add("<permit_application_id>", permiteApplicationId);
            fields.Add("<person_name>", title);
            fields.Add("<name_of_user_who_posted>", nameOfUserWhoPosted.ToString());
            fields.Add("<permitapplicationid_link_url_pri_newpermitapplication>", $"<a href=\"{permitApplicationFullUrl}\">{researchPermitTitle}</a>");
            fields.Add("<post_details>", newQuestion);
            fields.Add("<url_pri_discussion_forum_threadxxx>", $"<a href=\"{newQuestionFullLink}\">this Link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            if(attachDocumentVMList!=null && attachDocumentVMList.Count() > 0)
            {
                var attachments = new List<Attachment>();
                Attachment attach_data = null;
                foreach (var attachObj in attachDocumentVMList)
                {
                    attach_data = new Attachment(attachObj.FilePath);
                    attach_data.Name = attachObj.OriginalFileName;

                    attachments.Add(attach_data);
                }

                return SendEmailTemplate("NEW_ACTIVITY/REPLY_POSTED_ON_DISCUSSION_FORUM", fields, recipient, attachments);
            }
            else
            {
                return SendEmailTemplate("NEW_ACTIVITY/REPLY_POSTED_ON_DISCUSSION_FORUM", fields, recipient);
            }
          
        }

        //CR3&CR4 Phase1
        public bool SendDiscussionDelete_NotificationEmail(string title, string recipient, string newQuestionLink, string newQuestion, string nameOfUserWhoDeleted, string permitApplicationUrl, string contactUsUrl, string researchPermitTitle, string permiteApplicationId)
        {
            var permitApplicationFullUrl = GetEnvironmentUrl(recipient, new Uri(permitApplicationUrl).PathAndQuery);
            var newQuestionFullLink = GetEnvironmentUrl(recipient, new Uri(newQuestionLink).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<research_permit_title>", researchPermitTitle);
            fields.Add("<permit_application_id>", permiteApplicationId);
            fields.Add("<person_name>", title);
            fields.Add("<name_of_user_who_deleted>", nameOfUserWhoDeleted.ToString());
            fields.Add("<permitapplicationid_link_url_pri_newpermitapplication>", $"<a href=\"{permitApplicationFullUrl}\">{researchPermitTitle}</a>");
            fields.Add("<post_details>", newQuestion);
            fields.Add("<url_pri_discussion_forum_threadxxx>", $"<a href=\"{newQuestionFullLink}\">this Link</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("DELETE_POST_ON_DISCUSSION_FORUM", fields, recipient);

        }
        public bool SendUploadResourcePermission(string recipient, string personName, string NameOfRequester, string url_pub_my_profile_page, string AccountUrl)
        {
            var myProfileFullUrl = GetEnvironmentUrl(recipient, new Uri(url_pub_my_profile_page).PathAndQuery);
            var accountFullLink = GetEnvironmentUrl(recipient, new Uri(AccountUrl).PathAndQuery);
            
            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<name_of_requester>", NameOfRequester);
            fields.Add("<url_pub_my_profile_page>", $"<a href=\"{myProfileFullUrl}\">" + NameOfRequester + "</a>");
            fields.Add("<url_user_account_link>", $"<a href=\"{accountFullLink}\">this Link</a>");
            return SendEmailTemplate("REQUEST_UPLOAD_RESOURCE_PERMISSION", fields, recipient);
        }
        public bool SendDownloadResourcePermission(string recipient, string cc, string personName, string NameOfRequester, string url_pub_my_profile_page, string MetaTitle, string ResourceDocUrl, string AccountPermissionUrl
            ,long recipientId,long audit_userId,long permissionId, long CCId)
        {
            //fixes for wrong link - NPARK/BIOME/NCODE/2020_0108
            //var siteType = ConfigurationManager.AppSettings["SiteType"]; //Fixed to use based on site type
            //var siteType = "Internet";//always use internet URL cos recepient may not be access to Intranet.
            var myProfileFullUrl = GetEnvironmentUrl(recipient, new Uri(url_pub_my_profile_page).PathAndQuery);
            var accountPermissionFullUrl = GetEnvironmentUrl(recipient, new Uri(AccountPermissionUrl).PathAndQuery);
            var ResourceDocFullUrl = GetEnvironmentUrl(recipient, new Uri(ResourceDocUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<name_of_requester>", NameOfRequester);
            fields.Add("<metadatatitle>", MetaTitle);
            fields.Add("<url_pub_my_profile_page>", $"<a href=\"{myProfileFullUrl}\">" + NameOfRequester + "</a>");
            fields.Add("<datasettitle_link_url_datasetmetadatapage>", $"<a href=\"{ResourceDocFullUrl}\">" + MetaTitle + "</a>");
            fields.Add("<resourcedocumentfilepermissin_apporve_link>", $"<a href=\"{accountPermissionFullUrl}\">this Link</a>");

            ToDoListViewModel.ToDoListUIViewModel toDoListUIViewModel = new ToDoListViewModel.ToDoListUIViewModel();
            toDoListUIViewModel.UserId = recipientId;
            toDoListUIViewModel.TaskType = (int)BIOME.Enumerations.ToDoListing.TaskType.REQUEST_RESOURCE_DOWNLOAD_PERMISSION;
            toDoListUIViewModel.IsCompleted = false;
            toDoListUIViewModel.Link = new Uri(AccountPermissionUrl).PathAndQuery;
            toDoListUIViewModel.ObjectId = permissionId;
            todoListService.AddToDoList(toDoListUIViewModel, audit_userId);

            if (CCId != 0) {

                toDoListUIViewModel = new ToDoListViewModel.ToDoListUIViewModel();
                toDoListUIViewModel.UserId = CCId;
                toDoListUIViewModel.TaskType = (int)BIOME.Enumerations.ToDoListing.TaskType.REQUEST_RESOURCE_DOWNLOAD_PERMISSION;
                toDoListUIViewModel.IsCompleted = false;
                toDoListUIViewModel.Link = new Uri(AccountPermissionUrl).PathAndQuery;
                toDoListUIViewModel.ObjectId = permissionId;
                todoListService.AddToDoList(toDoListUIViewModel, audit_userId);

            }



            return SendEmailTemplate("REQUEST_RESOURCE_DOWNLOAD_PERMISSION", fields, recipient, null, cc);
        }

        public bool SendDownloadResourcePermissionStatus(string recipient, string cc, string personName, string status, string MetaTitle, string ResourceDocUrl)
        {
            var ResourceDocFullUrl = GetEnvironmentUrl(recipient, new Uri(ResourceDocUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<status>", status);
            fields.Add("<metadatatitle>", MetaTitle);
            fields.Add("<datasettitle_link_url_datasetmetadatapage>", $"<a href=\"{ResourceDocFullUrl}\">" + MetaTitle + "</a>");

            return SendEmailTemplate("REQUEST_RESOURCE_DOWNLOAD_PERMISSION_REPLY", fields, recipient, null, cc);
        }

        public bool SendUploadResourcePermissionApprove(string recipient, string personName)
        {
            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);

            return SendEmailTemplate("REQUEST_UPLOAD_RESOURCE_PERMISSION_APPROVED", fields, recipient);
        }

        //CR3&CR4 Phase2
        public bool SendEmailToAdminForResourceUpload(string recipient, string personName, string MetaTitle,string submittedBy, string ResourceDocUrl, string url_resource_approval, string uploader_profile_link, string uploader_email)
        {
            var ResourceDocFullUrl = GetEnvironmentUrl(recipient, new Uri(ResourceDocUrl).PathAndQuery);
            var ResourcApprovalFullUrl = GetEnvironmentUrl(recipient, new Uri(url_resource_approval).PathAndQuery);
            var UploaderProfileFullUrl = GetEnvironmentUrl(recipient, new Uri(uploader_profile_link).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<metadatatitle>", MetaTitle);
            fields.Add("<uploader_name>", submittedBy);
            fields.Add("<url_resource_metadata>", $"<a href=\"{ResourceDocFullUrl}\">" + MetaTitle + "</a>");
            fields.Add("<url_resource_approval>", $"<a href=\"{ResourcApprovalFullUrl}\">" + ResourcApprovalFullUrl + "</a>");
            fields.Add("<uploader_name_with_profile_link>", $"<a href=\"{UploaderProfileFullUrl}\">" + submittedBy + "</a>");
            fields.Add("<uploader_email>", uploader_email);

            return SendEmailTemplate("RESOURCE_UPLOAD_EMAIL_TO_ADMIN", fields, recipient);
        }


        //CR3&CR4 Phase2
        public bool SendEmailResourceUploadApprovalStatusChanged(string recipient, string personName, string status, string MetaTitle, string ResourceDocUrl)
        {
            var ResourceDocFullUrl = GetEnvironmentUrl(recipient, new Uri(ResourceDocUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<status>", status);
            fields.Add("<metadatatitle>", MetaTitle);
            fields.Add("<url_resource_metadata>", $"<a href=\"{ResourceDocFullUrl}\">" + MetaTitle + "</a>");

            return SendEmailTemplate("RESOURCE_UPLOAD_APPROVAL_STATUS_CHANGED", fields, recipient);
        }

        //CR3&CR4 Phase2
        public bool SendEmailToSiteManagerForResourceUpload(string recipient, string personName, string MetaTitle, string submittedBy, string ResourceDocUrl, string uploader_profile_link,string research_applicaton_link, string permit_title)
        {
            var ResourceDocFullUrl = GetEnvironmentUrl(recipient, new Uri(ResourceDocUrl).PathAndQuery);
            var UploaderProfileFullUrl = GetEnvironmentUrl(recipient, new Uri(uploader_profile_link).PathAndQuery);
            var Research_applicatonFullUrl = GetEnvironmentUrl(recipient, new Uri(research_applicaton_link).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<metadatatitle>", MetaTitle);
            fields.Add("<uploader_name>", submittedBy);
            fields.Add("<research_permit_title>", permit_title);
            fields.Add("<uploader_name_with_profile_link>", $"<a href=\"{UploaderProfileFullUrl}\">" + submittedBy + "</a>");
            fields.Add("<url_permit_application>", $"<a href=\"{Research_applicatonFullUrl}\">"+ permit_title + "</a>"); 
            fields.Add("<url_resource_metadata>", $"<a href=\"{ResourceDocFullUrl}\">" + MetaTitle + "</a>");
            
            return SendEmailTemplate("RESOURCE_UPLOAD_EMAIL_TO_SITE_MANAGER", fields, recipient);
        }

        public bool SendEmailToPermitManagerForResourceUpload(string recipient, string personName, string MetaTitle, string submittedBy, string ResourceDocUrl, string uploader_profile_link, string research_applicaton_link, string permit_title)
        {
            var ResourceDocFullUrl = GetEnvironmentUrl(recipient, new Uri(ResourceDocUrl).PathAndQuery);
            var UploaderProfileFullUrl = GetEnvironmentUrl(recipient, new Uri(uploader_profile_link).PathAndQuery);
            var Research_applicatonFullUrl = GetEnvironmentUrl(recipient, new Uri(research_applicaton_link).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<metadatatitle>", MetaTitle);
            fields.Add("<uploader_name>", submittedBy);
            fields.Add("<research_permit_title>", permit_title);
            fields.Add("<url_resource_metadata>", $"<a href=\"{ResourceDocFullUrl}\">" + MetaTitle + "</a>");
            fields.Add("<uploader_name_with_profile_link>", $"<a href=\"{UploaderProfileFullUrl}\">" + submittedBy + "</a>");
            fields.Add("<url_permit_application>", $"<a href=\"{Research_applicatonFullUrl}\">" + permit_title + "</a>");

            return SendEmailTemplate("RESOURCE_UPLOAD_EMAIL_TO_PERMIT_MANAGER", fields, recipient);
        }

        public bool SendEmailReminderToSiteManagerForPermitApplication(string recipient, string personName, string NameOfApplicant, string url_pub_my_profile_page,string applicationUrl,string dateSubmiited,string applicationtitle, string contactUsUrl)
        {
            var myProfileFullUrl = GetEnvironmentUrl(recipient, new Uri(url_pub_my_profile_page).PathAndQuery);
            var applicationFullUrl = GetEnvironmentUrl(recipient, new Uri(applicationUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<name_of_applicant>", NameOfApplicant);
            fields.Add("<date_of_application_submitted>", dateSubmiited);
            fields.Add("<permit_application_title>", applicationtitle);
            fields.Add("<url_pri_new_permit_application>", $"<a href=\"{applicationFullUrl}\">here</a>");
            fields.Add("<url_pub_my_profile_page>", $"<a href=\"{myProfileFullUrl}\">"+ NameOfApplicant + "</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("RESEARCH_PERMIT_EMAIL_REMINDER_TO_SITE_MANAGER", fields, recipient);
        }

        public bool SendEmailReminderToPermitManagerForPermitApplication(string recipient, string personName, string NameOfApplication, string Date, string EmailTemplate, long recipientId, long permitId, string contactUsUrl)
        {
            var emailTemplateFullUrl = GetEnvironmentUrl(recipient, new Uri(EmailTemplate).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<research_permit_title>", NameOfApplication);
            fields.Add("<permit_application_submissioin_datetime>", Date);
            fields.Add("<url_emailtemplate>", $"<a href=\"{emailTemplateFullUrl}\">" + "this Link" + "</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            ToDoListViewModel.ToDoListUIViewModel toDoListUIViewModel = new ToDoListViewModel.ToDoListUIViewModel();
            toDoListUIViewModel.RoleId = Constants.UserRoles.PermitManagerId;
            toDoListUIViewModel.UserId = recipientId;
            toDoListUIViewModel.TaskType = (int)BIOME.Enumerations.ToDoListing.TaskType.RESEARCH_PERMIT_EMAIL_REMINDER_TO_PERMIT_MANAGER;
            toDoListUIViewModel.IsCompleted = false;
            toDoListUIViewModel.Link = new Uri(EmailTemplate).PathAndQuery;
            toDoListUIViewModel.ObjectId = permitId;

            todoListService.AddToDoList(toDoListUIViewModel,0);

            return SendEmailTemplate("RESEARCH_PERMIT_EMAIL_REMINDER_TO_PERMIT_MANAGER", fields, recipient);
        }

        public bool SendEmailReminderToSiteManagerForPermitApplicationPending(string recipient, string personName, string NameOfApplicant, string url_pub_my_profile_page, string applicationUrl, string dateSubmiited, string applicationtitle, string contactUsUrl)
        {
            //NLogger.Info(url_pub_my_profile_page);
            //NLogger.Info(applicationUrl);
            //NLogger.Info(contactUsUrl);
            string myProfileFullUrl = "", applicationFullUrl="", contactUsFullUrl="";
            try
            {
                myProfileFullUrl = GetEnvironmentUrl(recipient, new Uri(url_pub_my_profile_page).PathAndQuery);
                applicationFullUrl = GetEnvironmentUrl(recipient, new Uri(applicationUrl).PathAndQuery);
                contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);
            }
            catch {
                myProfileFullUrl = "";
                applicationFullUrl = "";
                contactUsFullUrl = "";
            }

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<name_of_applicant>", NameOfApplicant);
            fields.Add("<date_of_application_submitted>", dateSubmiited);
            fields.Add("<permit_application_title>", applicationtitle);
            fields.Add("<url_pri_new_permit_application>", $"<a href=\"{applicationFullUrl}\">here</a>");
            fields.Add("<url_pub_my_profile_page>", $"<a href=\"{myProfileFullUrl}\">" + NameOfApplicant + "</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("RESEARCH_PERMIT_EMAIL_REMINDER_TO_SITE_MANAGER_PENDING", fields, recipient);
        }
     

        /*public bool SendFeedbackEmail(string[] recipients, string user, string userEmail, string category, string content)
        {
            string senderEmail = ConfigurationManager.AppSettings["SenderEmail"];
            var model = new SendFeedbackEmailInfo("No Reply", senderEmail, $"BIOME Feedback Received from {userEmail}", recipients.ToList(), null);
            model.DisplayName = $"{user} - {userEmail}";
            model.Category = category;
            model.Content = content;

            var feedbackEmail = emailController.SendFeedbackEmail(model);

            var mr = smtpMailSender.Deliver(feedbackEmail);

            return mr.All(r => r.DeliveryStatus == DeliveryStatus.DELIVERED);
        }*/

        /*public bool SendDiscussionAnswerNotificationEmail(string title, string recipient, string newAnswerLink, string newAnswer)
        {
            string senderEmail = ConfigurationManager.AppSettings["SenderEmail"];
            var model = new DiscussionAnaswerNotificationEmailInfo("No Reply", senderEmail, "New Answer Notification", new List<string>() { recipient }, title);
            newAnswerLink = ConvertIntranetURL(newAnswerLink);
            model.NewAnswerLink = newAnswerLink;
            model.Answer = newAnswer;

            var discussionAnswerNotificationEmail = emailController.SendDiscussionAnswerNotification(model);

            var mr = smtpMailSender.Deliver(discussionAnswerNotificationEmail);

            return mr.All(r => r.DeliveryStatus == DeliveryStatus.DELIVERED);
        }

        public bool SendDiscussionReminderEmail(string title, string recipient, string questionLink, string question)
        {
            string senderEmail = ConfigurationManager.AppSettings["SenderEmail"];
            var model = new DiscussionNotificationEmailInfo("No Reply", senderEmail, "Question Reminder", new List<string>() { recipient }, title);
            questionLink = ConvertIntranetURL(questionLink);
            model.NewQuestionLink = questionLink;
            model.Question = question;

            var discussionNotificationEmail = emailController.SendDiscussionNotification(model);

            var mr = smtpMailSender.Deliver(discussionNotificationEmail);

            return mr.All(r => r.DeliveryStatus == DeliveryStatus.DELIVERED);
        }*/

        //CR3&CR4 Phase1
        public bool SendDiscussionForumInvitationToPI(string title, string recipient, string signupUrl, string researchPermitTitle, string permitApplicationUrl)
        {
            var permitApplicationFullUrl = GetEnvironmentUrl(recipient, new Uri(permitApplicationUrl).PathAndQuery);
            //var newQuestionFullLink = GetEnvironmentUrl(recipient, new Uri(newQuestionLink).PathAndQuery);
            var signupFullUrl = GetEnvironmentUrl(recipient, new Uri(signupUrl).PathAndQuery);
            string personName = title;
            if (!string.IsNullOrEmpty(title))
            {
                var arr_name = title.Split(' ');
                if (arr_name.Length >= 1)
                    personName = arr_name[0];

            }
            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<research_permit_title>", researchPermitTitle);
            fields.Add("<url_sign_up>", $"<a href=\"{signupFullUrl}\">here</a>");
            fields.Add("<research_permit_url>", $"<a href=\"{permitApplicationFullUrl}\">" + researchPermitTitle + "</a>"); //shorten url

            return SendEmailTemplate("INVITE_PRINCIPAL_INVESTIGATOR_TO_JOIN_DISCUSSION_FORUM", fields, recipient);

        }
        //

        //CR3&CR4 Phase1
        public bool SendAssignMainApplicantSubmitted(string title, string recipient, string previous_applicant_name,string previous_applicant_email, string researchPermitTitle,string permitApplicationUrl)
        {
            //var permitApplicationFullUrl = GetEnvironmentUrl(recipient, new Uri(permitApplicationUrl).PathAndQuery);
            //var newQuestionFullLink = GetEnvironmentUrl(recipient, new Uri(newQuestionLink).PathAndQuery);
            //var signupFullUrl = GetEnvironmentUrl(recipient, new Uri(signupUrl).PathAndQuery);

            var permitApplicationFullUrl = GetEnvironmentUrl(recipient, new Uri(permitApplicationUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<previous_applicant_name>", previous_applicant_name);
            fields.Add("<previous_applicant_email>", previous_applicant_email);
            fields.Add("<research_permit_title>", researchPermitTitle);
            //added for NPARK/BIOME/NCODE/2020_0104
            fields.Add("<research_permit_url>", $"<a href=\"{permitApplicationFullUrl}\">" + researchPermitTitle + "</a>"); //shorten url

            return SendEmailTemplate("ASSIGNED_MAIN_APPLICANT", fields, recipient);

        }
        //

        public bool SendAuditLogMonthlyReport(string title, string startDate, string endDate, string recipient, string reportPath, string reportName,string cc, string startMonthYear, string sysadmin_list)
        {
            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<start_date_of_report_period>", startDate);
            fields.Add("<end_date_of_report_period>", endDate);
            fields.Add("<month_year_of_report>", startMonthYear);
            fields.Add("<list_of_sysadmin>", sysadmin_list);

            string repFile = reportPath + reportName;
            //string logFileFile = reportPath + "\\" + "Audit Log Additional Info Description.pdf"; ;
            Attachment rep = new Attachment(repFile, MediaTypeNames.Application.Pdf);
            //Attachment logInfo = new Attachment(logFileFile, MediaTypeNames.Application.Pdf);
            var attachments = new List<Attachment>()
            {
                rep
            };

            if(string.IsNullOrEmpty(cc))
                return SendEmailTemplate("AUDITLOG_MONTHLY_REPORT", fields, recipient, attachments);
            else
                return SendEmailTemplate("AUDITLOG_MONTHLY_REPORT", fields, recipient, attachments,cc);

        }

        public bool Send_E_AcknowledgementForPermitApplication(string title, string recipient, string main_applicant, string main_applicant_email, string research_permit_title, string sign_link, string sign_link_expire_date, string AcknowledgeSignLinkCode)
        {

            sign_link = sign_link.Replace("code_placeholder", AcknowledgeSignLinkCode);
            //Link is availale for non-BIOME user too. use internet web.
            var sign_linkFullUrl = InternetEmailBaseUrl + new Uri(sign_link).PathAndQuery;
            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<main_applicant>", main_applicant);
            fields.Add("<main_applicant_email>", main_applicant_email);
            fields.Add("<research_permit_title>", research_permit_title);
            fields.Add("<sign_link>", $"<a href=\"{sign_linkFullUrl}\">click here</a>");
            fields.Add("<sign_link_expire_date>", sign_link_expire_date);
            
            return SendEmailTemplate("E_ACKNOWLEDGEMENT_FOR_PERMIT_APPLICATION", fields, recipient);

        }
        public bool Send_To_MainApplicant_E_AcknowledgementSignedByAllMembers(string title, string recipient, string research_permit_title,string applicationType)
        {

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", title);
            fields.Add("<research_permit_title>", research_permit_title);
            fields.Add("<type_of_application>", applicationType);

            return SendEmailTemplate("E_ACKNOWLEDGEMENT_SIGNED_BY_ALL_MEMBERS_NOTIFICATION_TO_MAIN_APPLICANT", fields, recipient);

        }
        //Survey CR 2023 
        public bool SendAssignedSurveyOwner(string recipient, string personName, string surveyTitle, string contactUsUrl)
        {
            //var detailFullUrl = GetEnvironmentUrl(recipient, new Uri(detailUrl).PathAndQuery);
            var contactUsFullUrl = GetEnvironmentUrl(recipient, new Uri(contactUsUrl).PathAndQuery);

            var fields = new Dictionary<string, string>();
            fields.Add("<person_name>", personName);
            fields.Add("<survey_name>", surveyTitle);
            //fields.Add("<surveyname_link_url_pub_surveydetails>", $"<a href=\"{detailFullUrl}\">{surveyTitle}</a>");
            fields.Add("<url_pub_contact_us>", $"<a href=\"{contactUsFullUrl}\">contact us</a>");

            return SendEmailTemplate("ASSIGNED_SURVEY_OWNER", fields, recipient);
        }


        public bool Send_USER_ACCOUNT_DISABLEMENT_CAM(string recipient, string staff_name, string list_updated_users,string errorSummary) //CAM API
        {
            var fields = new Dictionary<string, string>();
            fields.Add("<staff_name>", staff_name);
            fields.Add("<list_updated_users>", list_updated_users);
            fields.Add("<error_summary>", errorSummary);

            

            return SendEmailTemplate("USER_ACCOUNT_DISABLEMENT_CAM", fields, recipient);

        }
        public bool Send_USER_ACCOUNT_REMOVAL_CAM(string recipient, string staff_name, string list_updated_users, string errorSummary) //CAM API
        {
            var fields = new Dictionary<string, string>();
            fields.Add("<staff_name>", staff_name);
            fields.Add("<list_updated_users>", list_updated_users);
            fields.Add("<error_summary>", errorSummary);



            return SendEmailTemplate("USER_ACCOUNT_REMOVAL_CAM", fields, recipient);

        }

        #endregion

        #region TestEmail

       /* public bool TestEmail()
        {
            var model = new EmailInfoBase("Me", "<EMAIL>", "subject", new List<string>() { "<EMAIL>" }, "Tester");
            var testEmailRER = emailController.TestEmail(model);

            var mr = smtpMailSender.Deliver(testEmailRER);

            return mr.All(r => r.DeliveryStatus == DeliveryStatus.DELIVERED);
        }*/

        public async Task SendEmailAsync(EmailTemplateViewModel emailTemplateVM)
        {
            MailMessage mailMessage = new MailMessage(emailTemplateVM.From, emailTemplateVM.To);
            if (!string.IsNullOrEmpty(emailTemplateVM.CC))
            {
                mailMessage.CC.Add(emailTemplateVM.CC);
            }
            if (!string.IsNullOrEmpty(emailTemplateVM.Subject))
            {
                mailMessage.Subject = emailTemplateVM.Subject;
            }
            if (!string.IsNullOrEmpty(emailTemplateVM.Content))
            {
                mailMessage.Body = emailTemplateVM.Content;
            }
            mailMessage.IsBodyHtml = emailTemplateVM.IsBodyHtml;

            await smtpClient.SendMailAsync(mailMessage);
        }

        public bool CustomSendEmail(EmailTemplateViewModel emailTemplateVM)
        {
            try
            {
                AsyncHelper.RunSync(() => SendEmail(emailTemplateVM));

                return true;
            }
            catch (Exception e)
            {
                var token = Guid.NewGuid();
                NLogger.Info("Sending email: " + token.ToString() + "\r\n  To: " + emailTemplateVM.To + "\r\n  Subject: " + emailTemplateVM.Subject + "\r\n Exception: " + e.ToString());
                return false;
            }
        }

        public async Task SendEmail(EmailTemplateViewModel emailTemplateVM)
        {

            MailMessage mailMessage = new MailMessage(emailTemplateVM.From, emailTemplateVM.To);
            if (!string.IsNullOrEmpty(emailTemplateVM.CC))
            {
                mailMessage.CC.Add(emailTemplateVM.CC);
            }
            if (!string.IsNullOrEmpty(emailTemplateVM.Subject))
            {
                mailMessage.Subject = emailTemplateVM.Subject;
            }
            if (!string.IsNullOrEmpty(emailTemplateVM.Content))
            {
                mailMessage.Body = emailTemplateVM.Content;
            }
            mailMessage.IsBodyHtml = emailTemplateVM.IsBodyHtml;

            if (emailTemplateVM.Attachments.Count > 0)
            {
                foreach (var attachment in emailTemplateVM.Attachments)
                {
                    mailMessage.Attachments.Add(attachment);
                }
            }

            //smtpClient.Send(mailMessage);
            var token = Guid.NewGuid();
            NLogger.Info("Sending email: " + token.ToString() + "\r\n  To: " + emailTemplateVM.To + "\r\n  Subject: " + emailTemplateVM.Subject);
            await smtpClient.SendMailAsync(mailMessage);

            if (emailTemplateVM.Attachments.Count > 0)
                mailMessage.Attachments.Dispose();

            

        }
        public async Task SendEmailviaSendGridAPI(EmailTemplateViewModel emailTemplateVM) {


            try
            {
                var apiKey = ConfigurationManager.AppSettings["SendGridAPIKey"];
                string integration_tier_sendgrid_api_url_host = ConfigurationManager.AppSettings["SendGridAPIURL"]; //in API management, URL surfix is /sendgrid/v3/mail/send which tranlate to sendgrid rest URL https://api.sendgrid.com/v3/mail/send. SendGridClient auto add v3/mail/send
                var client = new SendGridClient(apiKey, integration_tier_sendgrid_api_url_host);
                var mailMessage = new SendGrid.Helpers.Mail.SendGridMessage();
                SendGrid.Helpers.Mail.EmailAddress from,to;

                bool isSendGridEmailTestMode = false;
                bool.TryParse(ConfigurationManager.AppSettings["SendGridEmailTestMode"],out isSendGridEmailTestMode);
                if (isSendGridEmailTestMode)
                {
                    string testEmail = ConfigurationManager.AppSettings["SupportEmail"] ?? "<EMAIL>";
                    if (!emailTemplateVM.To.Equals(testEmail, StringComparison.OrdinalIgnoreCase))
                        return;


                    //from = new SendGrid.Helpers.Mail.EmailAddress("<EMAIL>");
                    //from = new SendGrid.Helpers.Mail.EmailAddress("<EMAIL>"); 
                    from = new SendGrid.Helpers.Mail.EmailAddress("<EMAIL>");
                    to = new SendGrid.Helpers.Mail.EmailAddress(testEmail);

                    mailMessage.SetFrom(from);
                    mailMessage.AddTo(to);
                    if (!string.IsNullOrEmpty(emailTemplateVM.CC))
                    {
                        mailMessage.AddCc(testEmail);
                    }
                }
                else {
                    from = new SendGrid.Helpers.Mail.EmailAddress(emailTemplateVM.From);
                    to = new SendGrid.Helpers.Mail.EmailAddress(emailTemplateVM.To);

                    mailMessage.SetFrom(from);
                    mailMessage.AddTo(to);

                    if (!string.IsNullOrEmpty(emailTemplateVM.CC))
                    {
                        mailMessage.AddCc(emailTemplateVM.CC);
                    }
                }
                
                if (!string.IsNullOrEmpty(emailTemplateVM.Subject))
                {
                    mailMessage.Subject = emailTemplateVM.Subject;
                }
                mailMessage.HtmlContent = emailTemplateVM.Content;
                if (emailTemplateVM.Attachments.Count > 0)
                {
                    foreach (var attachment in emailTemplateVM.Attachments)
                    {
                        string attachBase64Str = "";
                        using (MemoryStream stream = new MemoryStream())
                        {
                            attachment.ContentStream.CopyTo(stream);
                            attachBase64Str = Convert.ToBase64String(stream.ToArray());
                        }

                        mailMessage.AddAttachment(new SendGrid.Helpers.Mail.Attachment() { Type = attachment.ContentType.Name, Filename = attachment.Name, Content = attachBase64Str });
                        //mailMessage.Attachments.Add(attachment);
                    }
                }
                mailMessage.TrackingSettings = new SendGrid.Helpers.Mail.TrackingSettings()
                {
                    ClickTracking = new SendGrid.Helpers.Mail.ClickTracking()
                    {
                        Enable = false,
                    },
                    OpenTracking = new SendGrid.Helpers.Mail.OpenTracking()
                    {
                        Enable = false,
                    },
                    SubscriptionTracking = new SendGrid.Helpers.Mail.SubscriptionTracking()
                    {
                        Enable = false
                    }
                };
                //var token = Guid.NewGuid();
                NLogger.Info("Sending email via SendGrid API: " + "To: " + emailTemplateVM.To + "\r\n  Subject: " + emailTemplateVM.Subject);


                //var response = client.RequestAsync(SendGrid.BaseClient.Method.POST, mailMessage.Serialize(),null, integration_tier_sendgrid_api_Url).Result;
                
                var response = await client.SendEmailAsync(mailMessage);

                if (response.IsSuccessStatusCode)
                {
                    NLogger.Info("Email Sending Successful via SendGrid API. ");
                }
                else
                {

                    
                    NLogger.Info("Email Sending failed via SendGrid API: " + " HttpStatusCode: " + response.StatusCode.ToString());
                }

                /*if (emailTemplateVM.Attachments.Count > 0)
                    mailMessage.Attachments.Dispose();*/
            }
            catch (Exception ex) {

                NLogger.Info("Email Sending failed via SendGrid API: " + " Error: " + ex.ToString());
            }
            
        }
        public async Task<bool> SendFeedbackEmail(string[] recipients, string user, string userEmail, string category, string content)
        {

            

            try
            {
                logger.Info("Sending feedback email to: " + string.Join(",", recipients));

                string senderEmail = ConfigurationManager.AppSettings["SenderEmail"];
                //var model = new SendFeedbackEmailInfo("No Reply", senderEmail, $"BIOME Feedback Received from {userEmail}", recipients.ToList(), null);
                string DisplayName = $"{user} - {userEmail}";
                //string Category = category;
                //string Content = content;

                var apiKey = ConfigurationManager.AppSettings["SendGridAPIKey"];
                string integration_tier_sendgrid_api_url_host = ConfigurationManager.AppSettings["SendGridAPIURL"]; //in API management, URL surfix is /sendgrid/v3/mail/send which tranlate to sendgrid rest URL https://api.sendgrid.com/v3/mail/send. SendGridClient auto add v3/mail/send
                var client = new SendGridClient(apiKey, integration_tier_sendgrid_api_url_host);
                var mailMessage = new SendGrid.Helpers.Mail.SendGridMessage();
                SendGrid.Helpers.Mail.EmailAddress from, to;

                bool isSendGridEmailTestMode = false;
                bool.TryParse(ConfigurationManager.AppSettings["SendGridEmailTestMode"], out isSendGridEmailTestMode);
                if (isSendGridEmailTestMode)
                {
                    string testEmail = ConfigurationManager.AppSettings["SupportEmail"] ?? "<EMAIL>";

                    //from = new SendGrid.Helpers.Mail.EmailAddress("<EMAIL>");
                    from = new SendGrid.Helpers.Mail.EmailAddress("<EMAIL>");
                    //from = new SendGrid.Helpers.Mail.EmailAddress("<EMAIL>"); 
                    //from = new SendGrid.Helpers.Mail.EmailAddress("<EMAIL>");
                    to = new SendGrid.Helpers.Mail.EmailAddress(testEmail);

                    mailMessage.SetFrom(from);
                    mailMessage.AddTo(to);
                    
                }
                else
                {
                    from = new SendGrid.Helpers.Mail.EmailAddress(senderEmail);
                    //to = new SendGrid.Helpers.Mail.EmailAddress(emailTemplateVM.To);

                    mailMessage.SetFrom(from);
                    foreach (string rec in recipients) {
                        mailMessage.AddTo(rec);
                    }
                    

                  
                }
#if UAT
                mailMessage.Subject = $"[UAT] BIOME Feedback Received from {userEmail}";
#else
                mailMessage.Subject = $"BIOME Feedback Received from {userEmail}";
#endif

                string template = System.IO.File.ReadAllText(System.Web.Hosting.HostingEnvironment.MapPath("/PDF Templates/feedbackapi00123.html"));
                mailMessage.HtmlContent = template.Replace("{Category}", category).Replace("{DisplayName}", DisplayName).Replace("{Content}", content);
                
                mailMessage.TrackingSettings = new SendGrid.Helpers.Mail.TrackingSettings()
                {
                    ClickTracking = new SendGrid.Helpers.Mail.ClickTracking()
                    {
                        Enable = false,
                    },
                    OpenTracking = new SendGrid.Helpers.Mail.OpenTracking()
                    {
                        Enable = false,
                    },
                    SubscriptionTracking = new SendGrid.Helpers.Mail.SubscriptionTracking()
                    {
                        Enable = false
                    }
                };
                logger.Info("Sending email via SendGrid API: " + "To: " + string.Join(",", recipients) + "\r\n  Subject: " + mailMessage.Subject);

                var response = await client.SendEmailAsync(mailMessage);
                
                if (response.IsSuccessStatusCode)
                {
                    logger.Info("Email Sending Successful via SendGrid API. ");

                    return true;
                }
                else
                {


                    logger.Info("Email Sending failed via SendGrid API: " + " HttpStatusCode: " + response.StatusCode.ToString());
                    return false;
                }

                

            }
            catch (Exception ex)
            {

                logger.Info("Email Sending failed via SendGrid API: " + " Error: " + ex.ToString());
                return false;
            }

        }

        public void SendTestingEmail(string to, string subject)
        {
            AsyncHelper.RunSync(() => SendTestingEmailSend(to, subject));
        }
        public async Task SendTestingEmailSend(string to,string subject)
        {
            string senderEmail = ConfigurationManager.AppSettings["SenderEmail"];
            MailMessage mailMessage = new MailMessage(senderEmail, to);
            mailMessage.Subject = subject;
            mailMessage.Body = "This is testing email from BIOME system.";
            mailMessage.IsBodyHtml = true;

            await smtpClient.SendMailAsync(mailMessage);

        }
        private static void SendCompletedCallback(object sender, AsyncCompletedEventArgs e)
        {
            // Get the unique identifier for this asynchronous operation.
            String token = e.UserState.ToString();
            //log4net.ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            if (e.Cancelled)
            {
                NLogger.Info("Email Sending canceled: " + token);
            }
            if (e.Error != null)
            {
                NLogger.Info("Email Sending failed: " + token + "\r\n Exception: " + e.Error.ToString());
            }
            else
            {
                NLogger.Info("Email Sending Successful. ");
            }

        }

        #endregion

        #region Email Template

        public List<EmailTemplateViewModel.EmailTemplateEditViewModel> GetEmailTemplateList()
        {
            List<EmailTemplateViewModel.EmailTemplateEditViewModel> result = null;

            try
            {
                var emailTemplateList = (from b
                          in dbContext.EmailTemplates
                                         select b).ToList<EmailTemplate>();

                result = new List<EmailTemplateViewModel.EmailTemplateEditViewModel>();

                foreach (var item in emailTemplateList)
                {
                    EmailTemplateViewModel.EmailTemplateEditViewModel emailTemplateEditVM = new EmailTemplateViewModel.EmailTemplateEditViewModel();

                    emailTemplateEditVM.Id = item.Id;
                    emailTemplateEditVM.TemplateName = item.TemplateName;
                    emailTemplateEditVM.TemplateDisplayName = item.TemplateName.Replace('_', ' ');
                    emailTemplateEditVM.Subject = item.Subject;
                    emailTemplateEditVM.EmailBody = item.EmailBody;
                    emailTemplateEditVM.UpdatedAt = item.UpdatedAt;

                    result.Add(emailTemplateEditVM);
                }
            }
            catch (Exception ex)
            {
                return null;
            }

            return result;
        }

        public EmailTemplate GetEmailTemplate(string TemplateName)
        {
            var query = (from b in dbContext.EmailTemplates
                         where string.Equals(b.TemplateName, TemplateName)
                         select b).FirstOrDefault();
            return query;
        }

        //eg:
        //Dictionary<string, string> Fields = new Dictionary<string, string>();
        //Fields.Add("<project_name>", "Test Name");
        //Fields.Add("<First Name>", "Admin");
        //emailService.SendEmailTemplate("Request for project (sent to System Admin)", Fields, "<EMAIL>");
        public bool SendEmailTemplate(string TemplateName, Dictionary<string, string> Fields, string To, ICollection<System.Net.Mail.Attachment> attachments = null, string cc = "")
        {
            string From = ConfigurationManager.AppSettings["SenderEmail"];
            string Subject = "";
            //var environmentPrefix = ConfigurationManager.AppSettings["EnvironmentPrefix"];
            try
            {
                EmailTemplate emailTemplate = GetEmailTemplate(TemplateName);
                if (emailTemplate == null) return false;

                #if UAT
                    Subject = "[UAT] " + emailTemplate.Subject;
                #else
                    Subject = emailTemplate.Subject;
                #endif

                
                string EmailBody = emailTemplate.EmailBody;
                foreach (var item in Fields)
                {
                    if (item.Value != null)
                    {
                        Subject = Subject.Replace(item.Key, item.Value);
                        EmailBody = EmailBody.Replace("<placeholder>" + item.Key + "</placeholder>", item.Value);
                    }
                }

                Subject = Subject.Replace("\r\n", "").Replace("\n", "");
                if (string.Equals(TemplateName, "NOTIFICATION_ON_APPROVED/REJECTED_MEMBERSHIP"))
                {
                    string status = Fields["<membership_status>"];
                    if (string.Equals(status, "Approved"))
                    {
                        EmailBody = EmailBody.Replace("<placeholder><if_approved></placeholder>", "").Replace("<placeholder></if_approved></placeholder>", "");
                    }
                    else
                    {
                        //string pattern = @"<placeholder><if_approved></placeholder>(.+?)<placeholder></if_approved></placeholder>";
                        //Regex rgx = new Regex(pattern);
                        //EmailBody = rgx.Replace(EmailBody, "");
                        int indexstart = EmailBody.IndexOf("<placeholder><if_approved></placeholder>");
                        int indexend = EmailBody.IndexOf("<placeholder></if_approved></placeholder>");
                        EmailBody = EmailBody.Remove(indexstart, indexend - indexstart + "<placeholder></if_approved></placeholder>".Length);
                    }
                }
                if (Subject.Contains("<")) return false;
                //if (EmailBody.Contains("<placeholder>")) return false;

                EmailTemplateViewModel emailTemplateVM = new EmailTemplateViewModel();
                emailTemplateVM.From = From;
                emailTemplateVM.To = To;
                emailTemplateVM.CC = cc;
                emailTemplateVM.Subject = Subject;
                emailTemplateVM.Content = EmailBody;
                emailTemplateVM.IsBodyHtml = true;

                if (attachments != null)
                {
                    emailTemplateVM.Attachments = attachments;
                    
                }

#if INTERNET 

                bool UseSendGridEmail = false;
                bool.TryParse(ConfigurationManager.AppSettings["UseSendGridEmail"], out UseSendGridEmail);
                if (UseSendGridEmail)
                {
                    bool isSendGridEmailTestMode = false;
                    bool.TryParse(ConfigurationManager.AppSettings["SendGridEmailTestMode"], out isSendGridEmailTestMode);
                    if(isSendGridEmailTestMode)
                    {
                        if (TemplateName.Equals("RESET_PASSWORD"))
                        {
                            AsyncHelper.RunSync(() => SendEmailviaSendGridAPI(emailTemplateVM));
                        }
                        
                    }
                    else
                    {
                        AsyncHelper.RunSync(() => SendEmailviaSendGridAPI(emailTemplateVM));
                    }
                }
                else
                {
                    AsyncHelper.RunSync(() => SendEmail(emailTemplateVM));
                }
                

#else
                AsyncHelper.RunSync(() => SendEmail(emailTemplateVM));
#endif





                //Insert Inbox data
                if (emailTemplate.IsInboxable)
                {
                    Inbox newInbox = new Inbox();
                    newInbox.ReceiverEmail = To;
                    newInbox.SenderEmail = From;
                    newInbox.Title = Subject;
                    newInbox.Description = EmailBody;
                    AddInboxItem(newInbox);
                }


                
            }
            catch (Exception e)
            {
                var token = Guid.NewGuid();

                
                NLogger.Info("Sending email: " + token.ToString() + "\r\n  To: " + To + "\r\n  Subject: " + Subject + "\r\n Exception: " + e.ToString());

#if UAT
                if (TemplateName.Equals("ACCOUNT_LOGIN_OTP"))
                {
                    throw e;
                }
#endif

                //logger.Info("Sending email: " + token.ToString() + "\r\n  To: " + To + "\r\n  Subject: " + Subject + "\r\n Exception: " + e.ToString());




            }

            return true;
        }

        public List<string> GetFieldName(string emailBody)
        {
            string pattern = @"<placeholder>(.*?)</placeholder>";

            List<string> fieldList = new List<string>();

            try
            {
                foreach (Match match in Regex.Matches(emailBody, pattern))
                {
                    string group1 = match.Groups[1].ToString();
                    fieldList.Add(group1);
                }
            }
            catch (Exception ex)
            {
                return new List<string>();
            }

            return fieldList;
        }

        public bool UpdateEmailTemplate(EmailTemplateViewModel.EmailTemplateEditViewModel emailVM, int userId = 0)
        {
            EmailTemplate emailTemplate = dbContext.EmailTemplates.Find(emailVM.Id);
            dbContext.Entry(emailTemplate).State = System.Data.Entity.EntityState.Modified;

            // First save all placeholder content
            var placeholders = new Dictionary<string, string>();
            var placeholderCount = 0;
            var tempBody = System.Text.RegularExpressions.Regex.Replace(
                emailVM.EmailBody,
                @"<placeholder>(.*?)</placeholder>",
                match => {
                    var key = string.Format("PLACEHOLDER_{0}", placeholderCount++);
                    placeholders[key] = match.Groups[1].Value;
                    return string.Format("<placeholder>{0}</placeholder>", key);
                }
            );

            // Decode the email body
            string decodedBody = HttpUtility.HtmlDecode(tempBody);
            
            // Initialize the HtmlSanitizer
            var sanitizer = new HtmlSanitizer();
            
            // Add allowed tags
            sanitizer.AllowedTags.Add("placeholder");
            
            // Sanitize the content
            string sanitizedContent = sanitizer.Sanitize(decodedBody);
            
            // Restore placeholder content
            foreach (var placeholder in placeholders)
            {
                sanitizedContent = sanitizedContent.Replace(
                    string.Format("<placeholder>{0}</placeholder>", placeholder.Key),
                    string.Format("<placeholder>{0}</placeholder>", placeholder.Value)
                );
            }

            // Remove any remaining script tags
            emailVM.Subject = Regex.Replace(emailVM.Subject, "<script.*?</script>", "", RegexOptions.IgnoreCase);
            emailTemplate.Subject = emailVM.Subject;
            emailTemplate.EmailBody = sanitizedContent;

            return (dbContext.SaveChanges(false, userId, 0) > 0);
        }

        public string ConvertInternetURL(string urlString)
        {
            try
            {
                urlString = urlString.Replace(InternetEmailIP, InternetEmailBaseUrl);
                urlString = urlString.Replace(InternetEmailIPSecure, InternetEmailBaseUrl);
                urlString = urlString.Replace(IntranetEmailIP, InternetEmailBaseUrl);
                urlString = urlString.Replace(IntranetEmailIPSecure, InternetEmailBaseUrl);
                urlString = urlString.Replace(IntranetEmailBaseUrl, InternetEmailBaseUrl);
            }
            catch (Exception e) { }
            return urlString;
        }

        public string ConvertIntranetURL(string urlString)
        {
            try
            {
                urlString = urlString.Replace(InternetEmailIP, IntranetEmailBaseUrl);
                urlString = urlString.Replace(InternetEmailIPSecure, IntranetEmailBaseUrl);
                urlString = urlString.Replace(IntranetEmailIP, IntranetEmailBaseUrl);
                urlString = urlString.Replace(IntranetEmailIPSecure, IntranetEmailBaseUrl);
#if !UAT
                if (!urlString.Contains(IntranetEmailBaseUrl))
                {
                    urlString = urlString.Replace(InternetEmailBaseUrl, IntranetEmailBaseUrl);
                }
#endif
            }
            catch (Exception e) { }
            return urlString;
        }

#endregion

#region Private Methods

        private bool AddInboxItem(Inbox newInbox)
        {
            dbContext.Inboxes.Add(newInbox);
            dbContext.SaveChanges();

            return true;
        }

        private string GetEnvironmentUrl(string email, string url, string forceEnv = "")
        {
            if (!string.IsNullOrEmpty(forceEnv))
            {
                if (forceEnv == "Intranet")
                {
                    return IntranetEmailBaseUrl + url;
                } else if (forceEnv == "Internet")  //Fixes for NPARK/BIOME/NCODE/2020_0085
                {
                    return InternetEmailBaseUrl + url;
                }
            }
            if (url.Contains("/Admin/") || email.Contains("gov.sg"))
            {
                return IntranetEmailBaseUrl + url;
            } else
            {
                return InternetEmailBaseUrl + url;
            }
        }
#endregion

#region Email OTP
        public bool AddEmailOTP(long userId, string email, string code, DateTime createdDatetime)
        {
            bool ret = false;
            try
            {
                EmailOTP otp = new EmailOTP(userId, email, code, createdDatetime);
                dbContext.EmailOTPs.Add(otp);
                // Update previous OTP code as Invalid
                DateTime dt = DateTime.Now;
                var query = from b in dbContext.EmailOTPs
                            where b.LoginUserId == userId && b.OTPExpiredDate >= dt && b.IsValid == true
                            select b;
                if (query.Count() > 0)
                {
                    foreach (var emailOTP in query.ToList())
                    {
                        emailOTP.IsValid = false;
                        dbContext.Entry(emailOTP).State = System.Data.Entity.EntityState.Modified;
                    }
                }

                ret = (dbContext.SaveChanges() > 0);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return ret;
        }

        public bool IsValidEmailOTP(long userId, string code)
        {
            bool valid = false;
            try
            {
                DateTime dt = DateTime.Now;
                var query = from b in dbContext.EmailOTPs
                            where b.LoginUserId == userId && b.OTPCode == code && b.OTPExpiredDate >= dt && b.IsValid == true
                            select b;
                if (query.Count() == 1)
                {
                    valid = true;
                    var emailOTP = query.ToList()[0];
                    emailOTP.IsValid = false;
                    dbContext.Entry(emailOTP).State = System.Data.Entity.EntityState.Modified;
                    dbContext.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return valid;
        }


#endregion
    }

    public static class EmailServiceExtension
    {
        public static void SendEmail(this IEmailService emailService, EmailTemplateViewModel emailTemplateVM)
        {
            if (emailService == null)
            {
                throw new ArgumentNullException(nameof(emailService));
            }
            AsyncHelper.RunSync(() => emailService.SendEmailAsync(emailTemplateVM));
        }

        public static string ToPublicUrl(this string urlString)
        {

            return urlString.Replace("biome-intranet", "biome").Replace(":81/", "/");
        }

        public static string ToIntranetUrl(this string urlString)
        {
            var replaced = urlString.Replace("biome.nparks", "biome-intranet.nparks");
#if STAGING && AWS
            replaced = replaced.Replace("ec2-54-251-15-170.ap-southeast-1.compute.amazonaws.com/", "ec2-54-251-15-170.ap-southeast-1.compute.amazonaws.com:81/");
#elif UAT
            replaced = replaced.Replace("biome-staging.nparks.gov.sg/", "biome-staging.nparks.gov.sg:81/").Replace("biome-uat.nparks.gov.sg/", "biome-uat.nparks.gov.sg:81/");
#endif
            return replaced;

        }
    }
}
