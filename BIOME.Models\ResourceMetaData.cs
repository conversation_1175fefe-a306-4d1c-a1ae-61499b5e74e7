﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResourceMetaData:Entity<long>,ISoftDelete, IDescribableEntity 
    {
        public string Title { get; set; }
        public DateTimeOffset PreiodOfStudyFrom { get; set; }
        public DateTimeOffset PeriodOfStudyTo { get; set; }

        public string Description { get; set; }

        public string Organization { get; set; }

        public string LocationType { get; set; }

        public string MultipileLocationDrawn { get; set; } //CR3/4 Phase 2.
        

        [JsonIgnore]
        public virtual ICollection<ResourceDocument> ResourceDocuments { get; set; }
        
        public virtual ICollection<ResourceMetaDataAuthor> ResourceMetaDataAuthors { get; set; }

        [JsonIgnore]
        public virtual IList<Group> Groups { get; set; }
       
        public virtual IList<ConfSightingCategory> Categories { get; set; }

        public virtual IList<ConfListOther> OtherCategories { get; set; }

        public virtual IList<ConfHabitat> HabitatCategories { get; set;}

        public virtual IList<ConfEnvironment> EnvironmentCategories { get; set; }

        public virtual ICollection<ResourceMetaLocation> ResourceMetaLocations { get; set; }

        public long UploaderId { get; set; }

        public bool IsDeleted { get; set; }

        public int ApprovalStatus { get; set; }
        public string ApprovalRemarks { get; set; }
        public long ApprovalStatusUpdatedBy { get; set; }
        public DateTimeOffset ApprovalStatusUpdatedOn { get; set; }

        public long ResearchPermitApplicationID { get; set; }
        
        public string Describe()
        {
            return "{ Title : \"" + Title + "\", PreiodOfStudyFrom : \"" + PreiodOfStudyFrom  + "\", PeriodOfStudyTo : \"" + PeriodOfStudyTo
                + "\", Description : \"" + Description + "\", Organization : \"" + Organization + "\", UploaderId : \"" + UploaderId
                + "\", LocationType : \"" + LocationType
                + "\", ApprovalStatus : \"" + ApprovalStatus
                + "\", ApprovalRemarks : \"" + ApprovalRemarks
                + "\", ApprovalStatusUpdatedBy : \"" + ApprovalStatusUpdatedBy
                + "\", ApprovalStatusUpdatedOn : \"" + ApprovalStatusUpdatedOn
                + "\", IsDeleted : \"" + IsDeleted + "}";
        }

        public ResourceMetaData()
        {
            ResourceDocuments = new List<ResourceDocument>();
            ResourceMetaDataAuthors = new List<ResourceMetaDataAuthor>();
            ResourceMetaLocations = new List<ResourceMetaLocation>();
            Groups = new List<Group>();
            Categories = new List<ConfSightingCategory>();
            OtherCategories = new List<ConfListOther>();
            HabitatCategories = new List<ConfHabitat>();
            EnvironmentCategories = new List<ConfEnvironment>();
        }
    }

    
}
