﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class EmailTemplate : Entity<long>, IDescribableEntity
    {
        public string TemplateName { get; set; }
        public string Subject { get; set; }
        public string EmailBody { get; set; }
        public virtual IList<EmailTemplateField> EmailTemplateFields { get; set; }

        public bool IsInboxable { get; set; }

        public EmailTemplate()
        {
            EmailTemplateFields = new List<EmailTemplateField>();
        }
        public string Describe()
        {
            return "{ Subject : \"" + Subject + "\", TemplateName : \"" + TemplateName + "\", EmailBody : \"" + EmailBody + "\", IsInboxable : \"" + IsInboxable + "}";
        }

    }
}
