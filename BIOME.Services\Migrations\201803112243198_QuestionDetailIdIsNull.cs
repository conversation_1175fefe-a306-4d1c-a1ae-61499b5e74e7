namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class QuestionDetailIdIsNull : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.SurveyQuestionsAnswers", "QuestionDetailId", "dbo.SurveyQuestionDetails");
            DropIndex("dbo.SurveyQuestionsAnswers", new[] { "QuestionDetailId" });
            AlterColumn("dbo.SurveyQuestionsAnswers", "QuestionDetailId", c => c.<PERSON>());
            CreateIndex("dbo.SurveyQuestionsAnswers", "QuestionDetailId");
            AddForeignKey("dbo.SurveyQuestionsAnswers", "QuestionDetailId", "dbo.SurveyQuestionDetails", "Id");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.SurveyQuestionsAnswers", "QuestionDetailId", "dbo.SurveyQuestionDetails");
            DropIndex("dbo.SurveyQuestionsAnswers", new[] { "QuestionDetailId" });
            AlterColumn("dbo.SurveyQuestionsAnswers", "QuestionDetailId", c => c.<PERSON>(nullable: false));
            CreateIndex("dbo.SurveyQuestionsAnswers", "QuestionDetailId");
            AddForeignKey("dbo.SurveyQuestionsAnswers", "QuestionDetailId", "dbo.SurveyQuestionDetails", "Id", cascadeDelete: true);
        }
    }
}
