namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class StudyPeriodDatesInPermitApplication : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.ResearchPermitApplications", "StudyPeriodStart", c => c.DateTimeOffset(precision: 7));
            AddColumn("dbo.ResearchPermitApplications", "StudyPeriodEnd", c => c.DateTimeOffset(precision: 7));
        }
        
        public override void Down()
        {
            DropColumn("dbo.ResearchPermitApplications", "StudyPeriodEnd");
            DropColumn("dbo.ResearchPermitApplications", "StudyPeriodStart");
        }
    }
}
