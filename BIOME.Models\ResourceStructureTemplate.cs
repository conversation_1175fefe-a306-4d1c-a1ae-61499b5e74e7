﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResourceStructureTemplate : Entity<long>,IDescribableEntity
    {
        public string StructureTemplateName { get; set; }
        public string StructureTemplateType { get; set; }
        public bool IsActive { get; set; }

        public string Describe()
        {
            return "{ StructureTemplateName : \"" + StructureTemplateName + "\", StructureTemplateType : \"" + StructureTemplateType + "\", IsActive : \"" + IsActive + "}";
        }

        [JsonIgnore]
        public virtual IList<ResourceDocument> ResourceDocuments { get; set; }

        public virtual IList<ResourceStructureTemplateColumn> ResourceStructureTemplateColumns { get; set; }

        //[NotMapped]
        //public IList<Object> StructureDataList { get; set;}

        public ResourceStructureTemplate()
        {
            ResourceDocuments = new List<ResourceDocument>();
            ResourceStructureTemplateColumns = new List<ResourceStructureTemplateColumn>();
            //StructureDataList = new List<Object>();
        }
    }
}
