﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class SightingVerificationVote : Entity<long>,IDescribableEntity
    {
        public long VerificationNameId { get; set; }
        public long ByUserId { get; set; }
        public long AtSightingId { get; set; }
        [ForeignKey("AtSightingId")]
        public virtual SightingDetail AtSighting { get; set; }
        public string Describe()
        {
            return "{ VerificationNameId : \"" + VerificationNameId + "\", ByUserId : \"" + ByUserId + "\", AtSightingId : \"" + AtSightingId   + "}";
        }
        public SightingVerificationVote()
        {

        }
    }
}
