﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class HomepageContact : Entity<long>
    {
        [MaxLength(66)]
        public string GeneralName { get; set; }
        public string GeneralContact { get; set; }
        [MaxLength(320)]
        public string GeneralEmail { get; set; }
        [MaxLength(66)]
        public string TechName { get; set; }
        public string TechContact { get; set; }
        [MaxLength(320)]
        public string TechEmail { get; set; }
    }
}
