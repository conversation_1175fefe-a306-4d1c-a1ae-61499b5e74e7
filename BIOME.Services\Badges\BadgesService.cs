﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BIOME.Models;
using System.IO;
using CsvHelper;
using BIOME.ViewModels;
using Microsoft.Owin.Security.Provider;

namespace BIOME.Services
{
    public class BadgesService : ServiceBase, IBadgesService
    {
        #region Fields

        private readonly ApplicationDbContext dbContext;

        #endregion

        #region Constructors

        public BadgesService(ApplicationDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        #endregion

        #region Admin Report

        public List<ReportViewModel.GroupNameNumber> GetBadgeUserNumberList()
        {
            var query = from b in dbContext.UserInfoBadges.Include("Badge")
                        group b.Badge.Title by b.Badge.Title into g
                        select new { Title = g.Key, Count = g.Count() };

            List<ReportViewModel.GroupNameNumber> listGroupNameNumber = new List<ReportViewModel.GroupNameNumber>();
            foreach (var item in query)
            {
                listGroupNameNumber.Add(new ReportViewModel.GroupNameNumber { groupName = item.Title, number = item.Count });
                
            }

            return listGroupNameNumber.OrderByDescending(m => m.number).ToList();
        }

        #endregion

        #region PublicMethods

        public List<UserInfoBadge> GetUserBadgeList(long userId)
        {
            var query = (from b in dbContext.UserInfoBadges
                         where b.UserId == userId
                         orderby b.Id ascending
                         select b).ToList<UserInfoBadge>();

            return query;
        }
        //added to fix NPARK/BIOME/NCODE/2020_0123
        public List<long> GetUserBadgeUserIDList()
        {
            var query = (from b in dbContext.UserInfoBadges
                         select b.UserId).ToList();

            return query;
        }

        public List<BadgeDetail> GetAllBadgeList()
        {
            var query = (from b in dbContext.BadgeDetails
                         where b.IsActive && !b.IsHidden
                         orderby b.Id ascending
                         select b).ToList<BadgeDetail>();

            return query;
        }

        public List<UserInfoScore> GetUserScoreList(long userId)
        {
            var query = (from b in dbContext.UserInfoScores
                         where b.UserId == userId
                         select b).ToList<UserInfoScore>();

            return query;
        }

        public BadgeDetail GetBadgeDetail(long badgeId)
        {
            var query = (from b in dbContext.BadgeDetails
                        where b.Id == badgeId
                        select b).FirstOrDefault();
            if (query != null)
            {
                dbContext.Entry(query).State = System.Data.Entity.EntityState.Detached;
                return query;
            }

            return null;
        }

        public List<BadgeDetail> GetBadgeDetailList(string badgeCategory, int score)
        {
            var query = (from b in dbContext.BadgeDetails
                         where b.BadgeCategory == badgeCategory && b.TargetScore <= score && b.IsActive && !b.IsHidden
                         select b).ToList();
            return query;
        }

        public BadgeDetail GetLastBadgeDetail()
        {
            var query = (from b in dbContext.BadgeDetails
                         orderby b.Id descending
                         select b).First();
            return query;
        }

        public bool DelBadgeDetail(long badgeId)
        {
            BadgeDetail badgeDetail = new BadgeDetail { Id = badgeId };
            dbContext.BadgeDetails.Attach(badgeDetail);
            dbContext.BadgeDetails.Remove(badgeDetail);

            bool ret = false;
            if (dbContext.SaveChanges() > 0) ret = true;
            return ret;
        }

        public UserInfoBadge GetUserBadge(long userId, long badgeId)
        {
            var query = from b in dbContext.UserInfoBadges
                        where b.UserId == userId && b.BadgeId == badgeId
                        select b;

            if (query != null)
                return query.FirstOrDefault();
            else return null;
            /*foreach (var item in query)
            {
                return item;
            }*/

            //return null;
        }

        public bool AddUserBadge(long userId, long badgeId)
        {
            UserInfoBadge userBadge = new UserInfoBadge();
            userBadge.BadgeId = badgeId;
            userBadge.UserId = userId;

            dbContext.UserInfoBadges.Add(userBadge);
            bool ret = false;
            if (dbContext.SaveChanges(false, Convert.ToInt32(userId)) > 0) ret = true;
            return ret;
        }

        public UserInfoScore GetUserScore(long userId, string badgeCategory)
        {
            var query = from b in dbContext.UserInfoScores
                        where b.UserId == userId && b.BadgeCategory == badgeCategory
                        select b;

            if (query != null)
                return query.FirstOrDefault();
            else return null;
            /*foreach (var item in query)
            {
                return item;
            }

            return null;*/
        }

        public bool AddUserScore(long userId, string badgeCategory, int score = 0)
        {
            UserInfoScore userScore = new UserInfoScore();
            userScore.UserId = userId;
            userScore.BadgeCategory = badgeCategory;
            userScore.Score = score;

            dbContext.UserInfoScores.Add(userScore);
            bool ret = false;
            if (dbContext.SaveChanges() > 0) ret = true;
            return ret;
        }

        public bool UpdateUserScore(long userId, string badgeCategory, int score)
        {
            var query = from b in dbContext.UserInfoScores
                        where b.UserId == userId && b.BadgeCategory == badgeCategory
                        select b;
            UserInfoScore userScore = null;
            foreach (var item in query)
            {
                userScore = item;
            }
            if (userScore == null)
            {
                AddUserScore(userId, badgeCategory, score);
                return true;
            }
            dbContext.Entry(userScore).State = System.Data.Entity.EntityState.Modified;
            userScore.Score = score;
            bool ret = false;
            if (dbContext.SaveChanges() > 0) ret = true;
            return ret;
        }

        public bool IncreaseUserScore(long userId, string badgeCategory)
        {
            int score = 1;
            UserInfoScore userInfoScore = GetUserScore(userId, badgeCategory);
            if (userInfoScore != null) score = userInfoScore.Score + 1;

            UpdateUserScore(userId, badgeCategory, score);
            SetCategoryBadge(userId, badgeCategory, score);
            return true;
        }

        public bool SetUserScore(long userId, string badgeCategory, int score)
        {
            UpdateUserScore(userId, badgeCategory, score);
            SetCategoryBadge(userId, badgeCategory, score);
            return true;
        }

        public bool SetCategoryBadge(long userId, string badgeCategory, int score)
        {
            List<BadgeDetail> badgeDetailList = GetBadgeDetailList(badgeCategory, score);
            bool HasNewBadge = false;
            foreach (var badgeDetail in badgeDetailList)
            {
                long badgeId = badgeDetail.Id;
                UserInfoBadge userInfoBadge = GetUserBadge(userId, badgeId);
                if (userInfoBadge == null)
                {
                    AddUserBadge(userId, badgeId);
                    HasNewBadge = true;
                    //set userinfo hasnewbadge
                }
            }

            if (HasNewBadge) UpdateUserHasNewBadge(userId, true);

            return true;
        }

        public bool UpdateUserHasNewBadge(long userId, bool HasNewBadge)
        {
            var applicationUser = dbContext.Users.Find(userId);
            dbContext.Entry(applicationUser).State = System.Data.Entity.EntityState.Modified;
            applicationUser.HasNewBadge = HasNewBadge;
            return (dbContext.SaveChanges() > 0);
        }

        #endregion
    }
}
