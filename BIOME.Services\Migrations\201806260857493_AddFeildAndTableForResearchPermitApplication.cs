namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddFeildAndTableForResearchPermitApplication : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.ResearchPermitApplicationConfSightingCategories",
                c => new
                    {
                        ResearchPermitApplication_Id = c.<PERSON>(nullable: false),
                        ConfSightingCategory_Id = c.<PERSON>(nullable: false),
                    })
                .PrimaryKey(t => new { t.ResearchPermitApplication_Id, t.ConfSightingCategory_Id })
                .ForeignKey("dbo.ResearchPermitApplications", t => t.ResearchPermitApplication_Id, cascadeDelete: true)
                .ForeignKey("dbo.ConfSightingCategories", t => t.ConfSightingCategory_Id, cascadeDelete: true)
                .Index(t => t.ResearchPermitApplication_Id)
                .Index(t => t.ConfSightingCategory_Id);
            
            AddColumn("dbo.ResearchPermitApplications", "StudyLocationDescription", c => c.String());
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.ResearchPermitApplicationConfSightingCategories", "ConfSightingCategory_Id", "dbo.ConfSightingCategories");
            DropForeignKey("dbo.ResearchPermitApplicationConfSightingCategories", "ResearchPermitApplication_Id", "dbo.ResearchPermitApplications");
            DropIndex("dbo.ResearchPermitApplicationConfSightingCategories", new[] { "ConfSightingCategory_Id" });
            DropIndex("dbo.ResearchPermitApplicationConfSightingCategories", new[] { "ResearchPermitApplication_Id" });
            DropColumn("dbo.ResearchPermitApplications", "StudyLocationDescription");
            DropTable("dbo.ResearchPermitApplicationConfSightingCategories");
        }
    }
}
