﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace BIOME.Models
{
    public class ToDoList : Entity<long>, IDescribableEntity 
    {
        public int TaskType { get; set; }
        public int RoleId { get; set; }
        public string Link{ get; set; }
        public bool IsCompleted { get; set; }
        public DateTimeOffset? DateCompleted { get; set; }
        public long CompletedByUserId { get; set; }
        public long UserId { get; set; }
        public long ObjectId { get; set; }

        [NotMapped]
        public string TaskTypeString
        {
            get
            {
                if (TaskType == (int)BIOME.Enumerations.ToDoListing.TaskType.RESEARCH_PERMIT_EMAIL_REMINDER_TO_PERMIT_MANAGER) return "Pending Site Manager To Take Action on Permit Application";
                else if (TaskType == (int)BIOME.Enumerations.ToDoListing.TaskType.INVITATION_TO_JOIN_PROJECT_NON_SEARCHABLE_PROJECTS) return "Invitation To Join Project";
                else if (TaskType == (int)BIOME.Enumerations.ToDoListing.TaskType.INVITATION_TO_JOIN_PROJECT_NON_SEARCHABLE_SURVEYS) return "Invitation To Join Survey";
                else if (TaskType == (int)BIOME.Enumerations.ToDoListing.TaskType.REQUEST_RESOURCE_DOWNLOAD_PERMISSION) return "Request Resource Download Permission";
                return "";
            }
        }
        public string Describe()
        {
            return "{ TaskType : \"" + TaskType + "\", RoleId : \"" + RoleId + "\", Link : \"" + Link + "\", IsCompleted : \"" + IsCompleted
                + "\", DateCompleted : \"" + DateCompleted + "\", CompletedByUserId : \"" + CompletedByUserId + "\", UserId : \"" + UserId + "\", ObjectId : \"" + ObjectId + "}";
        }
        public ToDoList()
        {
            
        }
    }

}
