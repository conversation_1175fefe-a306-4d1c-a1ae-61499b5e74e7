﻿using AutoMapper;
using BIOME.Models;
using BIOME.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public class SightingIsExpertVerifiedResolver : ValueResolver<SightingDetail, bool>
    {
        protected override bool ResolveCore(SightingDetail source)
        {
            return source.ApprovedById > 0;
        }
    }
}
