﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
   public class Feedback : Entity<long>,IDescribableEntity
    {
        public Feedback()
        {

        }
        public string Subject { get; set; }
        public string Name { get; set; }
        
        [MaxLength(320)]
        public string Email { get; set; }
        public string Description { get; set; }
        public string Describe()
        {
            return "{ Subject : \"" + Subject + "\", Name : \"" + Name + "\", Email : \"" + Email + "\", Description : \"" + Description + "}";
        }

    }
}
