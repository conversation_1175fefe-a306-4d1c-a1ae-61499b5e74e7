﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Enumerations
{
    public class FileDownload
    {
        public enum FileType
        {
            Permit,
            Resource,
            DiscussionForum,
        }
        public enum Status
        {
           
            Reject = 0,
            Approve = 1,
        }
        public static readonly List<object> StatusOption = new List<object>
        {
            new { value = (int)Status.Approve , text = "Approve"},
             new { value =(int) Status.Reject , text = "Reject"},
          
           
           
        };
    }
}
