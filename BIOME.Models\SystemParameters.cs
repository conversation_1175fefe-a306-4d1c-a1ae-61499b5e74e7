﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class SystemParameters : Entity<long>, IDescribableEntity
    {
        public int AccountInactivePeriod { get; set; } //in days
        public int PasswordExpiryPeriod { get; set; } //in days
        public int AuditLogsStoragePeriod { get; set; } //in days
        public int SightingsMinVotesQualify { get; set; }
        public float SightingsMinPercentAgree { get; set; } // 2 decimal places
        public int SightingsFeaturedPeriodDays { get; set; }
        public int SightingsFeaturedLikeNum { get; set; }
        public int PaginationPageSize { get; set; }
        public string AuditLogMonthlyReportRecipients { get; set; }
        
        public int RenewPermitBeforeExpiryPeriod { get; set; } //in days
        public int LatestVerCodeiOS { get; set; } //iOS app build number (CFBundleVersion). //Software update check CR.
        public int LatestVerCodeAndroid { get; set; }//Android app version code. //Software update check CR.
        public string Describe()
        {
            return "{ AccountInactivePeriod : \"" + AccountInactivePeriod + "}";
        }
    }
}
