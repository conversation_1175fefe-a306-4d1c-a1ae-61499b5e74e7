﻿using BIOME.Constants;
using BIOME.Enumerations;
using BIOME.Models;
using BIOME.ViewModels;
using BIOME.Services;
using Hangfire;
using Microsoft.AspNet.Identity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Utilities.Helpers;

namespace BIOME.Services
{
    public class BackgroundJobService : IBackgroundJobService
    {
        public abstract class BackgroundJobScheduleOptions
        {
            
        }
        public class BackgroundJobScheduleImmediately : BackgroundJobScheduleOptions
        {
            
        }

        public class BackgroundJobSceduleDelayed : BackgroundJobScheduleOptions
        {
            public TimeSpan Delay { get; set; }
        }

        public class BackgroundJobScheduleLaterDate : BackgroundJobScheduleOptions
        {
            public DateTimeOffset LaterDate { get; set; }
        }

        public class BackgroundJobSceduleRecurrsive : BackgroundJobScheduleOptions
        {
            public string Cron { get; set; }
            public TimeZoneInfo TimeZoneInfo { get; set; }
            public string QueueName { get; set; }
        }

        #region Fields

        private readonly ApplicationDbContext dbContext;
        private log4net.ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
       
        #endregion

        #region Constructors

        public BackgroundJobService(ApplicationDbContext dbContext )
        {
            this.dbContext = dbContext;
            
        }

        #endregion

        #region Public Methods

        public void ScheduleRecurrsiveAccountManagement()
        {
            string jobId = Constants.BackgroundJob.BatchJob.AccountManagement;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }
            
            logger.Info("ScheduleRecurrsiveAccountManagement: add job CleanUpAccountExpiry");
            //Hangfire.BackgroundJob.Enqueue<IBatchJobService>(batch => batch.CleanUpAccountExpiry());
            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.CleanUpAccountExpiry(), Cron.Daily(0), timezone, Constants.BackgroundJob.QueuePriority.Critical);
        }
        /*public void ScheduleRecurrsiveUserUpdateFromACE()
        {
            string jobId = Constants.BackgroundJob.BatchJob.UpdateUserStatusFromACE;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }

            logger.Info("ScheduleRecurrsiveUserUpdateFromACE: add job UpdateUserStatusFromACE");
            //Hangfire.BackgroundJob.Enqueue<IBatchJobService>(batch => batch.CleanUpAccountExpiry());
            try
            {
                //RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.UpdateUserStatusFromACE(), Cron.Daily(), timezone, Constants.BackgroundJob.QueuePriority.Critical);
                //Run daily at 12PM
                RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.UpdateUserStatusFromACE(), "0 12 * * *", timezone, Constants.BackgroundJob.QueuePriority.Critical);
                
            }
            catch (Exception  ex)
            {
                logger.Error(ex.Message);
            }
        }
        */
        public void ScheduleRecurrsiveUserExportToACE()
        {
            string jobId = Constants.BackgroundJob.BatchJob.UserExportToACE;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }

            logger.Info("ScheduleRecurrsiveUserExportToACE: add job UserExportToACE");
            try
            {
                RecurringJob.AddOrUpdate<IBatchJobService>(jobId, BIOME.Constants.BackgroundJob.QueuePriority.app2, batch => batch.ExportUserToACE(), Cron.Daily(1), new RecurringJobOptions() { TimeZone = timezone });

            }
            catch (Exception ex)
            {
                logger.Error(ex.Message);
            }

        }

        public void ScheduleRecurrsiveReminderToSiteManagerForPermitApplication(string profileUrl,string emailTemplateUrl,string applicationurl,string contactUsUrl)
        {
            string jobId = Constants.BackgroundJob.BatchJob.ReminderToSiteManagerForPermitApplication;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }

            logger.Info("ReminderToSiteManagerForPermitApplication: add job ReminderToSiteManagerForPermitApplication");
            //Hangfire.BackgroundJob.Enqueue<IBatchJobService>(batch => batch.CleanUpAccountExpiry());
            try
            {
                RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.SendRemiderEmailForSiteManagerPermitApplication(profileUrl,emailTemplateUrl,  applicationurl,contactUsUrl), Cron.Daily(), timezone, Constants.BackgroundJob.QueuePriority.Critical);//Phase4
            }
            catch (Exception ex)
            {
                logger.Error(ex.Message);
            }
        }

        public void ScheduleRecurrsiveReminderToSiteManagerForPermitApplicationPending(string profileUrl,string emailTemplateUrl, string applicationurl,string contactUsUrl)
        {
            string jobId = Constants.BackgroundJob.BatchJob.ReminderToSiteManagerForPermitApplicationPending;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }

            logger.Info("ReminderToSiteManagerForPermitApplicationPending: add job ReminderToSiteManagerForPermitApplicationPending");
            //Hangfire.BackgroundJob.Enqueue<IBatchJobService>(batch => batch.CleanUpAccountExpiry());
            try
            {
                 RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.SendRemiderEmailForSiteManagerPermitApplicationPending(profileUrl, emailTemplateUrl,applicationurl, contactUsUrl), Cron.Daily(), timezone, Constants.BackgroundJob.QueuePriority.Critical);//Phase4
            }
            catch (Exception ex)
            {
                logger.Error(ex.Message);
            }
        }
        public void ScheduleRecurrsiveReminderDiscussion()
        {
           /* string jobId = Constants.BackgroundJob.BatchJob.RemainderDiscussion;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }

            //Hangfire.BackgroundJob.Enqueue<IBatchJobService>(batch => batch.CleanUpAccountExpiry());
            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.SendReminderEmailForUnanswerDiscussion(), Cron.Daily(0), timezone, Constants.BackgroundJob.QueuePriority.Critical);*/
        }

        public void ScheduleRecurrsiveIndexSightingRecords()
        {
            string jobId = Constants.BackgroundJob.BatchJob.IndexSightingRecord;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }

            //Hangfire.BackgroundJob.Enqueue<IBatchJobService>(batch => batch.CleanUpAccountExpiry());
            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.IndexSightingRecords(), Cron.Daily(1), timezone, Constants.BackgroundJob.QueuePriority.Critical);
        }

        
        public void ScheduleResearchApplicationExpire(long id, string permitUrl, string contactUsUrl, BackgroundJobScheduleOptions options)
        {
            string jobId = "";
            string parameters = JsonConvert.SerializeObject(new EmailTemplateViewModel.EmailTemplatePermitExpiringFields(id, permitUrl, contactUsUrl));
            Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;
            if (options is BackgroundJobScheduleImmediately)
            {
                jobId = Hangfire.BackgroundJob.Enqueue<IScheduledJobService>(sjs => sjs.SendEmailForExpiringResearchApplicationOnNoReport(id, permitUrl, contactUsUrl));

            }
            else if (options is BackgroundJobSceduleDelayed)
            {
                if (HasJobScheduled(parameters, BackgroundJobTask.TaskType.ExpiredResearchApplicationSendEmailOnNoReport, schedule))
                {
                    return;
                }

                BackgroundJobSceduleDelayed delayedOptions = (BackgroundJobSceduleDelayed)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.SendEmailForExpiringResearchApplicationOnNoReport(id, permitUrl, contactUsUrl), delayedOptions.Delay);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bj => bj.RemoveCompletedJob(jobId));
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Delayed;
            }
            else if (options is BackgroundJobScheduleLaterDate)
            {
                if (HasJobScheduled(parameters, BackgroundJobTask.TaskType.ExpiredResearchApplicationSendEmailOnNoReport, schedule))
                {
                    return;
                }

                BackgroundJobScheduleLaterDate laterDateOptions = (BackgroundJobScheduleLaterDate)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.SendEmailForExpiringResearchApplicationOnNoReport(id, permitUrl, contactUsUrl), laterDateOptions.LaterDate);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bjs => bjs.RemoveCompletedJob(jobId));
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.LaterDate;
            }
            else if (options is BackgroundJobSceduleRecurrsive)
            {
                BackgroundJobSceduleRecurrsive recurrsiveOptions = (BackgroundJobSceduleRecurrsive)options;
                RecurringJob.AddOrUpdate<IScheduledJobService>(sjs => sjs.SendEmailForExpiringResearchApplicationOnNoReport(id, permitUrl, contactUsUrl), recurrsiveOptions.Cron, recurrsiveOptions.TimeZoneInfo, recurrsiveOptions.QueueName);
            }

            if (!string.IsNullOrEmpty(jobId) && !(options is BackgroundJobSceduleRecurrsive) && !(options is BackgroundJobScheduleImmediately))
            {
                dbContext.BackgroundJobTasks.Add(new BackgroundJobTask
                {
                    JobId = jobId,
                    Parameters = parameters,
                    Task = BackgroundJobTask.TaskType.ExpiredResearchApplicationSendEmailOnNoReport,
                    Schedule = schedule
                });
                dbContext.SaveChanges();
            }
        }

        public void SchedulePasswordExpired(string title, string recipient, DateTimeOffset passwordExpiryDate, Uri passwordResetLink, Uri contactUsLink, BackgroundJobScheduleOptions options)
        {
            string jobId = "";
            string parameters = JsonConvert.SerializeObject(new EmailTemplateViewModel.EmailTemplatePasswordExpiredFields(title, recipient, passwordExpiryDate, passwordResetLink, contactUsLink));
            Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;
            if (options is BackgroundJobScheduleImmediately)
            {
                jobId = Hangfire.BackgroundJob.Enqueue<IScheduledJobService>(sjs => sjs.SendEmailForPasswordExpired(title, recipient, passwordExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), passwordResetLink, contactUsLink));

            }
            else if (options is BackgroundJobSceduleDelayed)
            {
                if (HasJobScheduled(parameters, BackgroundJobTask.TaskType.PasswordExpired, schedule))
                {
                    return;
                }

                BackgroundJobSceduleDelayed delayedOptions = (BackgroundJobSceduleDelayed)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.SendEmailForPasswordExpired(title, recipient, passwordExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), passwordResetLink, contactUsLink), delayedOptions.Delay);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bj => bj.RemoveCompletedJob(jobId));
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Delayed;
            }
            else if (options is BackgroundJobScheduleLaterDate)
            {
                if (HasJobScheduled(parameters, BackgroundJobTask.TaskType.PasswordExpired, schedule))
                {
                    return;
                }

                BackgroundJobScheduleLaterDate laterDateOptions = (BackgroundJobScheduleLaterDate)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.SendEmailForPasswordExpired(title, recipient, passwordExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), passwordResetLink, contactUsLink), laterDateOptions.LaterDate);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bjs => bjs.RemoveCompletedJob(jobId));
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.LaterDate;
            }
            else if (options is BackgroundJobSceduleRecurrsive)
            {
                BackgroundJobSceduleRecurrsive recurrsiveOptions = (BackgroundJobSceduleRecurrsive)options;
                RecurringJob.AddOrUpdate<IScheduledJobService>(sjs => sjs.SendEmailForPasswordExpired(title, recipient, passwordExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), passwordResetLink, contactUsLink), recurrsiveOptions.Cron, recurrsiveOptions.TimeZoneInfo, recurrsiveOptions.QueueName);
            }

            if (!string.IsNullOrEmpty(jobId) && !(options is BackgroundJobSceduleRecurrsive) && !(options is BackgroundJobScheduleImmediately))
            {
                dbContext.BackgroundJobTasks.Add(new BackgroundJobTask
                {
                    JobId = jobId,
                    Parameters = parameters,
                    Task = BackgroundJobTask.TaskType.PasswordExpired,
                    Schedule = schedule
                });
                dbContext.SaveChanges();
            }
        }
        public void SchedulePasswordExpiredNotification(string title, string recipient, DateTimeOffset passwordExpiryDate, Uri passwordResetLink, Uri contactUsLink, BackgroundJobScheduleOptions options)
        {
            string jobId = "";
            string parameters = JsonConvert.SerializeObject(new EmailTemplateViewModel.EmailTemplatePasswordExpiredNotificationFields(title, recipient, passwordExpiryDate, passwordResetLink, contactUsLink));
            Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;
            if (options is BackgroundJobScheduleImmediately)
            {
                jobId = Hangfire.BackgroundJob.Enqueue<IScheduledJobService>(sjs => sjs.SendEmailForPasswordExpiredNotification(title, recipient, passwordExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), passwordResetLink, contactUsLink));

            }
            else if (options is BackgroundJobSceduleDelayed)
            {
                if (HasJobScheduled(parameters, BackgroundJobTask.TaskType.PasswordExpiredNotification, schedule))
                {
                    return;
                }

                BackgroundJobSceduleDelayed delayedOptions = (BackgroundJobSceduleDelayed)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.SendEmailForPasswordExpiredNotification(title, recipient, passwordExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), passwordResetLink, contactUsLink), delayedOptions.Delay);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bj => bj.RemoveCompletedJob(jobId));
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Delayed;
            }
            else if (options is BackgroundJobScheduleLaterDate)
            {
                if (HasJobScheduled(parameters, BackgroundJobTask.TaskType.PasswordExpiredNotification, schedule))
                {
                    return;
                }

                BackgroundJobScheduleLaterDate laterDateOptions = (BackgroundJobScheduleLaterDate)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.SendEmailForPasswordExpiredNotification(title, recipient, passwordExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), passwordResetLink, contactUsLink), laterDateOptions.LaterDate);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bjs => bjs.RemoveCompletedJob(jobId));
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.LaterDate;
            }
            else if (options is BackgroundJobSceduleRecurrsive)
            {
                BackgroundJobSceduleRecurrsive recurrsiveOptions = (BackgroundJobSceduleRecurrsive)options;
                RecurringJob.AddOrUpdate<IScheduledJobService>(sjs => sjs.SendEmailForPasswordExpiredNotification(title, recipient, passwordExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), passwordResetLink, contactUsLink), recurrsiveOptions.Cron, recurrsiveOptions.TimeZoneInfo, recurrsiveOptions.QueueName);
            }

            if (!string.IsNullOrEmpty(jobId) && !(options is BackgroundJobSceduleRecurrsive) && !(options is BackgroundJobScheduleImmediately))
            {
                dbContext.BackgroundJobTasks.Add(new BackgroundJobTask
                {
                    JobId = jobId,
                    Parameters = parameters,
                    Task = BackgroundJobTask.TaskType.PasswordExpiredNotification,
                    Schedule = schedule
                });
                dbContext.SaveChanges();
            }
        }
        //public void ScheduleAccountExpired(string title, string recipient, DateTimeOffset accountExpiryDate, Uri accountResetLink, Uri contactUsLink, BackgroundJobScheduleOptions options)
        //{
        //    string jobId = "";
        //    string parameters = JsonConvert.SerializeObject(new EmailTemplateViewModel.EmailTemplateAccountExpiredFields(title, recipient, accountExpiryDate, accountResetLink, contactUsLink));
        //    Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;
        //    if (options is BackgroundJobScheduleImmediately)
        //    {
        //        jobId = Hangfire.BackgroundJob.Enqueue<IScheduledJobService>(sjs => sjs.SendAccountExpiry(title, recipient, accountExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), accountResetLink, contactUsLink));

        //    }
        //    else if (options is BackgroundJobSceduleDelayed)
        //    {
        //        if (HasJobScheduled(parameters, BackgroundJobTask.TaskType.AccountExpired, schedule))
        //        {
        //            return;
        //        }

        //        BackgroundJobSceduleDelayed delayedOptions = (BackgroundJobSceduleDelayed)options;
        //        jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.SendAccountExpiry(title, recipient, accountExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), accountResetLink, contactUsLink), delayedOptions.Delay);
        //        Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bj => bj.RemoveCompletedJob(jobId));
        //        schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Delayed;
        //    }
        //    else if (options is BackgroundJobScheduleLaterDate)
        //    {
        //        if (HasJobScheduled(parameters, BackgroundJobTask.TaskType.AccountExpired, schedule))
        //        {
        //            return;
        //        }

        //        BackgroundJobScheduleLaterDate laterDateOptions = (BackgroundJobScheduleLaterDate)options;
        //        jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.SendAccountExpiry(title, recipient, accountExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), accountResetLink, contactUsLink), laterDateOptions.LaterDate);
        //        Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bjs => bjs.RemoveCompletedJob(jobId));
        //        schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.LaterDate;
        //    }
        //    else if (options is BackgroundJobSceduleRecurrsive)
        //    {
        //        BackgroundJobSceduleRecurrsive recurrsiveOptions = (BackgroundJobSceduleRecurrsive)options;
        //        RecurringJob.AddOrUpdate<IScheduledJobService>(sjs => sjs.SendAccountExpiry(title, recipient, accountExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), accountResetLink, contactUsLink), recurrsiveOptions.Cron, recurrsiveOptions.TimeZoneInfo, recurrsiveOptions.QueueName);
        //    }

        //    if (!string.IsNullOrEmpty(jobId) && !(options is BackgroundJobSceduleRecurrsive) && !(options is BackgroundJobScheduleImmediately))
        //    {
        //        dbContext.BackgroundJobTasks.Add(new BackgroundJobTask
        //        {
        //            JobId = jobId,
        //            Parameters = parameters,
        //            Task = BackgroundJobTask.TaskType.AccountExpired,
        //            Schedule = schedule
        //        });
        //        dbContext.SaveChanges();
        //    }
        //}
        //public void ScheduleAccountExpiredNotification(string title, string recipient, DateTimeOffset accountExpiryDate, Uri accountResetLink, Uri contactUsLink, BackgroundJobScheduleOptions options)
        //{
        //    string jobId = "";
        //    string parameters = JsonConvert.SerializeObject(new EmailTemplateViewModel.EmailTemplateAccountExpiredNotificationFields(title, recipient, accountExpiryDate, accountResetLink, contactUsLink));
        //    Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;
        //    if (options is BackgroundJobScheduleImmediately)
        //    {
        //        jobId = Hangfire.BackgroundJob.Enqueue<IScheduledJobService>(sjs => sjs.SendAccountExpiryNotification(title, recipient, accountExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), accountResetLink, contactUsLink));

        //    }
        //    else if (options is BackgroundJobSceduleDelayed)
        //    {
        //        if (HasJobScheduled(parameters, BackgroundJobTask.TaskType.AccountExpiredNotification, schedule))
        //        {
        //            return;
        //        }

        //        BackgroundJobSceduleDelayed delayedOptions = (BackgroundJobSceduleDelayed)options;
        //        jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.SendAccountExpiryNotification(title, recipient, accountExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), accountResetLink, contactUsLink), delayedOptions.Delay);
        //        Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bj => bj.RemoveCompletedJob(jobId));
        //        schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Delayed;
        //    }
        //    else if (options is BackgroundJobScheduleLaterDate)
        //    {
        //        if (HasJobScheduled(parameters, BackgroundJobTask.TaskType.AccountExpiredNotification, schedule))
        //        {
        //            return;
        //        }

        //        BackgroundJobScheduleLaterDate laterDateOptions = (BackgroundJobScheduleLaterDate)options;
        //        jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.SendAccountExpiryNotification(title, recipient, accountExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), accountResetLink, contactUsLink), laterDateOptions.LaterDate);
        //        Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bjs => bjs.RemoveCompletedJob(jobId));
        //        schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.LaterDate;
        //    }
        //    else if (options is BackgroundJobSceduleRecurrsive)
        //    {
        //        BackgroundJobSceduleRecurrsive recurrsiveOptions = (BackgroundJobSceduleRecurrsive)options;
        //        RecurringJob.AddOrUpdate<IScheduledJobService>(sjs => sjs.SendAccountExpiryNotification(title, recipient, accountExpiryDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm"), accountResetLink, contactUsLink), recurrsiveOptions.Cron, recurrsiveOptions.TimeZoneInfo, recurrsiveOptions.QueueName);
        //    }

        //    if (!string.IsNullOrEmpty(jobId) && !(options is BackgroundJobSceduleRecurrsive) && !(options is BackgroundJobScheduleImmediately))
        //    {
        //        dbContext.BackgroundJobTasks.Add(new BackgroundJobTask
        //        {
        //            JobId = jobId,
        //            Parameters = parameters,
        //            Task = BackgroundJobTask.TaskType.AccountExpiredNotification,
        //            Schedule = schedule
        //        });
        //        dbContext.SaveChanges();
        //    }
        //}


        public void ScheduleAddIndexForResourceDocument(long id, BackgroundJobScheduleOptions options)
        {
            string jobId = "";
            string parameters = "";
            Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;
            if (options is BackgroundJobScheduleImmediately)
            {
                jobId = Hangfire.BackgroundJob.Enqueue<IScheduledJobService>(sjs => sjs.AddIndexForResourceDocument(id));

            }
            else if (options is BackgroundJobSceduleDelayed)
            {
                if (HasJobScheduled(id.ToString(), BackgroundJobTask.TaskType.AddIndexForResourceDocument, schedule))
                {
                    return;
                }

                BackgroundJobSceduleDelayed delayedOptions = (BackgroundJobSceduleDelayed)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.AddIndexForResourceDocument(id), delayedOptions.Delay);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bj => bj.RemoveCompletedJob(jobId));
                parameters = id.ToString();
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Delayed;
            }
            else if (options is BackgroundJobScheduleLaterDate)
            {
                if (HasJobScheduled(id.ToString(), BackgroundJobTask.TaskType.AddIndexForResourceDocument, schedule))
                {
                    return;
                }

                BackgroundJobScheduleLaterDate laterDateOptions = (BackgroundJobScheduleLaterDate)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.AddIndexForResourceDocument(id), laterDateOptions.LaterDate);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bjs => bjs.RemoveCompletedJob(jobId));
                parameters = id.ToString();
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.LaterDate;
            }
            else if (options is BackgroundJobSceduleRecurrsive)
            {
                BackgroundJobSceduleRecurrsive recurrsiveOptions = (BackgroundJobSceduleRecurrsive)options;
                RecurringJob.AddOrUpdate<IScheduledJobService>(sjs => sjs.AddIndexForResourceDocument(id), recurrsiveOptions.Cron, recurrsiveOptions.TimeZoneInfo, recurrsiveOptions.QueueName);
            }

            if (!string.IsNullOrEmpty(jobId) && !(options is BackgroundJobSceduleRecurrsive) && !(options is BackgroundJobScheduleImmediately))
            {
                dbContext.BackgroundJobTasks.Add(new BackgroundJobTask
                {
                    JobId = jobId,
                    Parameters = parameters,
                    Task = BackgroundJobTask.TaskType.AddIndexForResourceDocument,
                    Schedule = schedule
                });
                dbContext.SaveChanges();
            }
        }

        public void ScheduleAddIndexForMapResourceDocument(long id, BackgroundJobScheduleOptions options)
        {
            string jobId = "";
            string parameters = "";
            Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;
            if (options is BackgroundJobScheduleImmediately)
            {
                jobId = Hangfire.BackgroundJob.Enqueue<IScheduledJobService>(sjs => sjs.AddIndexForMapResourceDocument(id));

            }
            else if (options is BackgroundJobSceduleDelayed)
            {
                if (HasJobScheduled(id.ToString(), BackgroundJobTask.TaskType.AddIndexForMapResourceDocument, schedule))
                {
                    return;
                }

                BackgroundJobSceduleDelayed delayedOptions = (BackgroundJobSceduleDelayed)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.AddIndexForMapResourceDocument(id), delayedOptions.Delay);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bj => bj.RemoveCompletedJob(jobId));
                parameters = id.ToString();
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Delayed;
            }
            else if (options is BackgroundJobScheduleLaterDate)
            {
                if (HasJobScheduled(id.ToString(), BackgroundJobTask.TaskType.AddIndexForMapResourceDocument, schedule))
                {
                    return;
                }

                BackgroundJobScheduleLaterDate laterDateOptions = (BackgroundJobScheduleLaterDate)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.AddIndexForMapResourceDocument(id), laterDateOptions.LaterDate);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bjs => bjs.RemoveCompletedJob(jobId));
                parameters = id.ToString();
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.LaterDate;
            }
            else if (options is BackgroundJobSceduleRecurrsive)
            {
                BackgroundJobSceduleRecurrsive recurrsiveOptions = (BackgroundJobSceduleRecurrsive)options;
                RecurringJob.AddOrUpdate<IScheduledJobService>(sjs => sjs.AddIndexForMapResourceDocument(id), recurrsiveOptions.Cron, recurrsiveOptions.TimeZoneInfo, recurrsiveOptions.QueueName);
            }

            if (!string.IsNullOrEmpty(jobId) && !(options is BackgroundJobSceduleRecurrsive) && !(options is BackgroundJobScheduleImmediately))
            {
                dbContext.BackgroundJobTasks.Add(new BackgroundJobTask
                {
                    JobId = jobId,
                    Parameters = parameters,
                    Task = BackgroundJobTask.TaskType.AddIndexForMapResourceDocument,
                    Schedule = schedule
                });
                dbContext.SaveChanges();
            }
        }

        public void ScheduleCheckFileForIndexResourceDocument(long id, BackgroundJobScheduleOptions options)
        {
            string jobId = "";
            string parameters = "";
            Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;
            if (options is BackgroundJobScheduleImmediately)
            {
                jobId = Hangfire.BackgroundJob.Enqueue<IScheduledJobService>(sjs => sjs.CheckFileForIndexResourceDocument(id));

            }
            else if (options is BackgroundJobSceduleDelayed)
            {
                if (HasJobScheduled(id.ToString(), BackgroundJobTask.TaskType.CheckFileForIndexResourceDocument, schedule))
                {
                    return;
                }

                BackgroundJobSceduleDelayed delayedOptions = (BackgroundJobSceduleDelayed)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.CheckFileForIndexResourceDocument(id), delayedOptions.Delay);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bj => bj.RemoveCompletedJob(jobId));
                parameters = id.ToString();
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Delayed;
            }
            else if (options is BackgroundJobScheduleLaterDate)
            {
                if (HasJobScheduled(id.ToString(), BackgroundJobTask.TaskType.CheckFileForIndexResourceDocument, schedule))
                {
                    return;
                }

                BackgroundJobScheduleLaterDate laterDateOptions = (BackgroundJobScheduleLaterDate)options;
                jobId = Hangfire.BackgroundJob.Schedule<IScheduledJobService>(sjs => sjs.CheckFileForIndexResourceDocument(id), laterDateOptions.LaterDate);
                Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bjs => bjs.RemoveCompletedJob(jobId));
                parameters = id.ToString();
                schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.LaterDate;
            }
            else if (options is BackgroundJobSceduleRecurrsive)
            {
                BackgroundJobSceduleRecurrsive recurrsiveOptions = (BackgroundJobSceduleRecurrsive)options;
                RecurringJob.AddOrUpdate<IScheduledJobService>(sjs => sjs.CheckFileForIndexResourceDocument(id), recurrsiveOptions.Cron, recurrsiveOptions.TimeZoneInfo, recurrsiveOptions.QueueName);
            }

            if (!string.IsNullOrEmpty(jobId) && !(options is BackgroundJobSceduleRecurrsive) && !(options is BackgroundJobScheduleImmediately))
            {
                dbContext.BackgroundJobTasks.Add(new BackgroundJobTask
                {
                    JobId = jobId,
                    Parameters = parameters,
                    Task = BackgroundJobTask.TaskType.CheckFileForIndexResourceDocument,
                    Schedule = schedule
                });
                dbContext.SaveChanges();
            }
        }

        public void ScheduleSightingJob(long id, int jobType, BackgroundJobSceduleDelayed options)
        {
            string jobId = "";
            string parameters = "";
            Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;

            if (HasJobScheduled(id.ToString(), BackgroundJobTask.TaskType.IndexForSighting, schedule))
            {
                return;
            }

            BackgroundJobSceduleDelayed delayedOptions = options;

            if (jobType == 1)
            {
                jobId = Hangfire.BackgroundJob.Schedule<IBatchJobService>(sjs => sjs.IndexSightingRecords(id), delayedOptions.Delay);
            }
            else if (jobType == 2)
            {
                jobId = Hangfire.BackgroundJob.Schedule<IBatchJobService>(sjs => sjs.UpdateSightingsToES(id), delayedOptions.Delay);
            }
            else if (jobType == 3)
            {
                jobId = Hangfire.BackgroundJob.Schedule<IBatchJobService>(sjs => sjs.RemoveSightingsfromES(id), delayedOptions.Delay);
            }

            Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bj => bj.RemoveCompletedJob(jobId));
            parameters = id.ToString();
            schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Delayed;

            if (!string.IsNullOrEmpty(jobId))
            {
                dbContext.BackgroundJobTasks.Add(new BackgroundJobTask
                {
                    JobId = jobId,
                    Parameters = parameters,
                    Task = BackgroundJobTask.TaskType.IndexForSighting,
                    Schedule = schedule
                });
                dbContext.SaveChanges();
            }
        }

        public void ScheduleResourceJob(long id, int jobType, BackgroundJobSceduleDelayed options, int? skip, int? size, int? year)
        {
            string jobId = "";
            string parameters = "";
            Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;

            if (HasJobScheduled(id.ToString(), BackgroundJobTask.TaskType.IndexForResource, schedule))
            {
                return;
            }

            BackgroundJobSceduleDelayed delayedOptions = options;

            if (jobType == 1)
            {
                jobId = Hangfire.BackgroundJob.Schedule<IBatchJobService>(sjs => sjs.Remove_And_ReAdd_ResourceDocToES(id), delayedOptions.Delay);
            }else if (jobType == 2)
            {
                jobId = Hangfire.BackgroundJob.Schedule<IBatchJobService>(sjs => sjs.ReSyncResources_ES(skip,size,year), delayedOptions.Delay);
            }


            Hangfire.BackgroundJob.ContinueWith<IBackgroundJobService>(jobId, bj => bj.RemoveCompletedJob(jobId));
            parameters = id.ToString();
            schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Delayed;

            if (!string.IsNullOrEmpty(jobId))
            {
                dbContext.BackgroundJobTasks.Add(new BackgroundJobTask
                {
                    JobId = jobId,
                    Parameters = parameters,
                    Task = BackgroundJobTask.TaskType.IndexForResource,
                    Schedule = schedule
                });
                dbContext.SaveChanges();
            }
        }

        public bool DeleteScheduledJob(string jobId)
        {
            var deleted = Hangfire.BackgroundJob.Delete(jobId);
            if (deleted)
            {
                RemoveCompletedJob(jobId);
            }
            return deleted;
        }


        public void RemoveCompletedJob(string jobId)
        {
            var jobTask = dbContext.BackgroundJobTasks.FirstOrDefault(t => t.JobId == jobId);
            if (jobTask == null)
            {
                return;
            }

            dbContext.BackgroundJobTasks.Remove(jobTask);
            dbContext.SaveChanges();
        }

        public void ScheduleRecurrsiveIndexApplicationRecords()
        {
            string jobId = Constants.BackgroundJob.BatchJob.IndexApplicationRecord;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }

            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.IndexApplicationRecords(), Cron.Daily(1, 30), timezone, Constants.BackgroundJob.QueuePriority.Critical);
        }

        public void ScheduleRecurrsiveIndexResourceDocumentRecord()
        {
            string jobId = Constants.BackgroundJob.BatchJob.IndexResourceDocumentRecord;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }

            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.IndexResourceDocumentRecords(), Cron.Daily(2), timezone, Constants.BackgroundJob.QueuePriority.Critical);
        }

        public void ScheduleRecurrsiveIndexMapResourceDocumentRecord()
        {
            string jobId = Constants.BackgroundJob.BatchJob.IndexMapResourceDocumentRecord;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(0), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }

            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.IndexMapResourceDocumentRecords(), Cron.Daily(5), timezone, Constants.BackgroundJob.QueuePriority.Critical);
        }

        public bool HasJobScheduled(string parameters, BackgroundJobTask.TaskType taskType, Enumerations.BackgroundJob.Schedule.ScheduleType schedule)
        {
            return dbContext.BackgroundJobTasks.FirstOrDefault(t => t.Parameters == parameters && t.Task == taskType && t.Schedule == schedule) != null;
        }

        public void ScheduleRecurrsiveGetMavenLayers()
        {
            string jobId = Constants.BackgroundJob.BatchJob.GetMavenLayers;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }
            
            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.GetMavenLayers(), Cron.Hourly(4), timezone, Constants.BackgroundJob.QueuePriority.Critical);
        }

        public void ScheduleRecurrsiveGetResourceLayers()
        {
            string jobId = Constants.BackgroundJob.BatchJob.GetResourceLayers;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }
#if !DEBUG
            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.GetResourceLayers(), Cron.Hourly(1), timezone, Constants.BackgroundJob.QueuePriority.Critical);
#endif
        }


        public void ScheduleRecurrsiveCleanUserAuthExpiredTicket()
        {
            string jobId = Constants.BackgroundJob.BatchJob.CleanUserAuthExpiredTicket;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }
            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.CleanUserAuthExpiredTicket(), Cron.Hourly(2), timezone, Constants.BackgroundJob.QueuePriority.Critical);
            
        }

        public void ScheduleRecurrsiveAuditLogMonthlyReport()
        {
            
            string jobId = Constants.BackgroundJob.BatchJob.AuditLogMonthlyReport;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }
            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, BIOME.Constants.BackgroundJob.QueuePriority.app2,batch => batch.SendAuditLogMonthlyReport(false), Cron.Monthly(1,8), new RecurringJobOptions() { TimeZone = timezone });

        }

        public void ScheduleUpdateMember_e_sign_link_expire()
        {
            string jobId = Constants.BackgroundJob.BatchJob.UpdateMember_e_sign_link_expire;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }
            //run every 4 hour
            //0 */4 * * *
            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, batch => batch.UpdateMember_e_sign_link_expire(), Cron.Hourly(5), timezone, Constants.BackgroundJob.QueuePriority.Critical);

        }

        public void ScheduleSyncFilesApp1()
        {
            string jobId = Constants.BackgroundJob.BatchJob.ScheduleSyncFilesApp1;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }
            //run every 4 hour
            //0 */4 * * *
            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, Constants.BackgroundJob.QueuePriority.app1, batch => batch.SyncFilesApp1(), Cron.Daily(6)
                , new RecurringJobOptions() { TimeZone = timezone });

        }
        public void ScheduleSyncFilesApp2()
        {
            string jobId = Constants.BackgroundJob.BatchJob.ScheduleSyncFilesApp2;

            var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
            if (timezone == null)
            {
                timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
            }
            //run every 4 hour
            //0 */4 * * *
            RecurringJob.AddOrUpdate<IBatchJobService>(jobId, Constants.BackgroundJob.QueuePriority.app2,batch => batch.SyncFilesApp2(), Cron.Daily(6)
                , new RecurringJobOptions() { TimeZone = timezone});

        }

        public void ScheduleSyncFilesForApp1ByFile(BackgroundJobSceduleDelayed options, params long[] storeId)
        {
            //string jobId = "";
            //string parameters = "";

            //Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;
            BackgroundJobSceduleDelayed delayedOptions = options;
            Hangfire.BackgroundJob.Schedule<IBatchJobService>(BIOME.Constants.BackgroundJob.QueuePriority.app1,sjs => sjs.SyncFilesForApp1ByFile(storeId), delayedOptions.Delay);
            //Hangfire.BackgroundJob.Enqueue<IBatchJobService>(sjs => sjs.SyncFilesForApp1ByFile(storeId))
            //Hangfire.BackgroundJob.Enqueue()

            //jobId = Hangfire.BackgroundJob.Schedule<IBatchJobService>(sjs => sjs.SyncFilesForApp1ByFile(storeId), delayedOptions.Delay);


        }
        public void ScheduleSyncFilesForApp2ByFile(BackgroundJobSceduleDelayed options, params long[] storeId)
        {
           // Enumerations.BackgroundJob.Schedule.ScheduleType schedule = Enumerations.BackgroundJob.Schedule.ScheduleType.Immediate;
            BackgroundJobSceduleDelayed delayedOptions = options;
            Hangfire.BackgroundJob.Schedule<IBatchJobService>(BIOME.Constants.BackgroundJob.QueuePriority.app2,sjs => sjs.SyncFilesForApp2ByFile(storeId), delayedOptions.Delay);

        }
        #endregion

        #region Private Methods



        #endregion
    }
}
