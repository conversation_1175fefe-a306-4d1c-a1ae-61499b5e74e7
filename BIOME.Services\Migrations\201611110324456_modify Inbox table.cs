namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class modifyInboxtable : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Inboxes", "ReceiverEmail", c => c.String());
            AddColumn("dbo.Inboxes", "SenderEmail", c => c.String());
            DropColumn("dbo.Inboxes", "Sender");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Inboxes", "Sender", c => c.String());
            DropColumn("dbo.Inboxes", "SenderEmail");
            DropColumn("dbo.Inboxes", "ReceiverEmail");
        }
    }
}
