﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Constants
{
    public static class BackgroundJob
    {
        public static class QueuePriority
        {
            public const string Critical = "critical";
            public const string High = "high";
            public const string Default = "default";
            public const string Low = "low";
            public const string app1 = "app1";
            public const string app2 = "app2";
        }
        public static class BatchJob
        {
            public const string AccountManagement = "AccountManagement";
            public const string RemainderDiscussion = "RemainderDiscussion";
            public const string IndexSightingRecord = "IndexSightingRecord";
            public const string IndexProjectRecord = "IndexProjectRecord";
            public const string IndexApplicationRecord = "IndexApplicationRecord";
            public const string IndexResourceDocumentRecord = "IndexResourceDocumentRecord";
            public const string IndexMapResourceDocumentRecord = "IndexMapResourceDocumentRecord";
            public const string GetMavenLayers = "GetMavenLayers";
            public const string GetResourceLayers = "GetResourceLayers";
            public const string UpdateUserStatusFromACE = "UpdateUserStatusFromACE";
            public const string UserExportToACE = "UserExportToACE";
            public const string ReminderToSiteManagerForPermitApplication = "ReminderToSiteManagerForPermitApplication";
            public const string ReminderToSiteManagerForPermitApplicationPending = "ReminderToSiteManagerForPermitApplicationPending";
            public const string CleanUserAuthExpiredTicket = "CleanUserAuthExpiredTicket";
            public const string AuditLogMonthlyReport = "AuditLogMonthlyReport";
            public const string UpdateMember_e_sign_link_expire = "UpdateMember_e_sign_link_expire";
            public const string ScheduleSyncFilesApp1 = "ScheduleSyncFilesApp1";
            public const string ScheduleSyncFilesApp2 = "ScheduleSyncFilesApp2";
        }
    }
}
