﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchReport : Entity<long>,IDescribableEntity
    {
        public string FileName { get; set; }
        public string RealFileName { get; set; }

        public string Describe()
        {
            return "{ FileName : \"" + FileName + "\", RealFileName : \"" + RealFileName + "}";
        }
        [JsonIgnore]
        public virtual ResearchApplication AtResearchApplication { get; set; }
        //CR3&CR4 Phase1
        public long ResearchPermitApplication_Id { get; set; }

        [NotMapped]
        public string RealFileNameNotEmpty
        {
            get {
                if (!String.IsNullOrEmpty(RealFileName))
                    return RealFileName;
                else
                    return FileName;
            }
        }
    }
}
