﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;
namespace BIOME.Models
{
   public class SurveyQuestions:Entity<long>,IDescribableEntity 
    {
        public string Title { get; set; }
        public bool IsBefore { get; set; }
        public long QuestionTypeID { get; set; }
        [NotMapped]
        public SurveyQuestionType SurveyQuestionType { get; set; }
        public bool IsCompulsory { get; set; }
        //public bool IsModified { get; set; }
        public bool IsRemoved { get; set; }
        //public long LinkedQuestionID { get; set; }
        public int Order { get; set; }

        [JsonIgnore]
        public virtual Survey Survey { get; set; }
        public virtual List<SurveyQuestionDetail> SurveyQuestionDetail { get; set; }

        public string Describe()
        {
            return "{ Title : \"" + Title + "\", IsBefore : \"" + IsBefore + "\", QuestionTypeID : \"" + QuestionTypeID + "\", IsCompulsory : \"" + IsCompulsory + "\", Survey : \"" + Survey?.Id + "}";
        }
        public SurveyQuestions()
            {
            SurveyQuestionDetail = new List<SurveyQuestionDetail>();
            }
    }
}
