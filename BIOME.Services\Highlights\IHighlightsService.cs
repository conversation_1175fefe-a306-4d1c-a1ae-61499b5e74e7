﻿using BIOME.Models;
using BIOME.ViewModels;
using MvcPaging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public interface IHighlightsService
    {
        IQueryable<InternetHighlight> GetInternetHighlights();
        IQueryable<IntranetHighlight> GetIntranetHighlights();
        AdminHomepageViewModel.HighlightEditUploadViewModel GetInternetHighlightsEdit(long id);
        IList<AdminHomepageViewModel.HighlightViewModel> GetInternetHighlights(out int totalCount);
        IList<HomepageViewModel.ResearchHighlightViewModel> GetInternetHighlightsCarousel();
        IPagedList<AdminHomepageViewModel.HighlightViewModel> GetInternetHighlightsPaged(int currentIndexPage, int pageSize, out int totalCount);
        AdminHomepageViewModel.HighlightEditUploadViewModel GetIntranetHighlightsEdit(long id);
        IList<AdminHomepageViewModel.HighlightViewModel> GetIntranetHighlights(out int totalCount);
        IList<HomepageViewModel.ResearchHighlightViewModel> GetIntranetHighlightsCarousel();
        IPagedList<AdminHomepageViewModel.HighlightViewModel> GetIntranetHighlightsPaged(int currentIndexPage, int pageSize, out int totalCount);
        Task<ServiceResult> AddHighlight(string filename, string headerContentFullPath, AdminHomepageViewModel.HighlightUploadViewModel uploadViewModel, bool isInternet);
        Task<ServiceResult> UpdateHighlight(string filename, string headerContentFullPath, AdminHomepageViewModel.HighlightEditUploadViewModel uploadViewModel, bool isInternet);
        Task<ServiceResult> UpdateHighlight(AdminHomepageViewModel.HighlightEditUploadViewModel uploadViewModel, bool isInternet);
        Task<ServiceResult> DeleteHighlight(long id, bool isInternet);
    }
}