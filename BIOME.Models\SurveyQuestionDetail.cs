﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
namespace BIOME.Models
{
 public   class SurveyQuestionDetail:Entity<long>,IDescribableEntity 
    {
        public string Description { get; set; }
        public bool isAnswer { get; set; }
        [JsonIgnore]
        public virtual SurveyQuestions SurveyQuestion { get; set; }
        public string Describe()
        {
            return "{ Description : \"" + Description + "\", isAnswer : \"" + isAnswer + "\", SurveyQuestion : \"" + SurveyQuestion?.Id + "}";
        }
    }
}
