namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddLastSuspendDateLastDeActivateDateAccountStatus : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.AspNetUsers", "LastSuspendDate", c => c.DateTimeOffset(nullable: false, precision: 7));
            AddColumn("dbo.AspNetUsers", "LastDeActivateDate", c => c.DateTimeOffset(nullable: false, precision: 7));
            AddColumn("dbo.AspNetUsers", "AccountStatus", c => c.String());
        }
        
        public override void Down()
        {
            DropColumn("dbo.AspNetUsers", "AccountStatus");
            DropColumn("dbo.AspNetUsers", "LastDeActivateDate");
            DropColumn("dbo.AspNetUsers", "LastSuspendDate");
        }
    }
}
