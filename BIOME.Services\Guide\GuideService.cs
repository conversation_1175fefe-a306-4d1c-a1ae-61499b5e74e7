﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BIOME.Models;
using BIOME.ViewModels;

namespace BIOME.Services
{
    public class GuideService : ServiceBase, IGuideService
    {
        #region Constructors
        private readonly ApplicationDbContext dbContext;
        //private readonly UserService userService;

        public GuideService(ApplicationDbContext dbContext)
        {
            this.dbContext = dbContext;
        }
        #endregion

        #region Public Methods

        public bool AddGuideDetailId(long userId, long Id, string Title, string FileName, string Version, string CheckSum, int FileSize, bool IsEnable)
        {
            GuideDetail guideDetail = new GuideDetail();
            guideDetail.Id = Id;
            guideDetail.Title = Title;
            guideDetail.FileName = FileName;
            guideDetail.Version = Version;
            guideDetail.CheckSum = CheckSum;
            guideDetail.FileSize = FileSize;
            guideDetail.IsEnable = IsEnable;
            dbContext.Database.ExecuteSqlCommand(String.Format("DBCC CHECKIDENT ('GuideDetails', RESEED, {0})", Id - 1));
            dbContext.GuideDetails.Add(guideDetail);
            int maxid = Convert.ToInt32(dbContext.GuideDetails.Max(x => x.Id));
            bool ret = false;
            if (dbContext.SaveChanges(false, Convert.ToInt32(userId)) > 0) ret = true;
            return ret;
        }

        public long AddGuideDetail(long userId, long Id, string Title, string FileName, string Version, string CheckSum, int FileSize, bool IsEnable)
        {
            long retId = 0;
            GuideDetail guideDetail = new GuideDetail();
            guideDetail.Id = Id;
            guideDetail.Title = Title;
            guideDetail.FileName = FileName;
            guideDetail.Version = Version;
            guideDetail.CheckSum = CheckSum;
            guideDetail.FileSize = FileSize;
            guideDetail.IsEnable = IsEnable;
            //dbContext.Database.ExecuteSqlCommand(String.Format("DBCC CHECKIDENT ('GuideDetails', RESEED, {0})", Id - 1));
            dbContext.GuideDetails.Add(guideDetail);
            int maxid = Convert.ToInt32(dbContext.GuideDetails.Max(x => x.Id));
            if (dbContext.SaveChanges(false, Convert.ToInt32(userId),maxid) > 0)
                retId = guideDetail.Id;
          
            return retId;

        }

        public bool DelGuideDetail(long userId, long Id)
        {
            GuideDetail guideDetail = new GuideDetail { Id = Id };
            dbContext.GuideDetails.Attach(guideDetail);
            dbContext.GuideDetails.Remove(guideDetail);

            return (dbContext.SaveChanges(false, Convert.ToInt32(userId)) > 0);
        }

        public bool UpdateGuideDetail(long userId, long Id, string Title, bool IsEnable)
        {
            GuideDetail guideDetail = dbContext.GuideDetails.Find(Id);
            dbContext.Entry(guideDetail).State = System.Data.Entity.EntityState.Modified;
            guideDetail.IsEnable = IsEnable;
            guideDetail.Title = Title;
            return (dbContext.SaveChanges(false, Convert.ToInt32(userId)) > 0);
        }

        public bool UpdateGuideDetailFile(long userId, long Id, string FileName, string Version, string CheckSum, int FileSize)
        {
            GuideDetail guideDetail = dbContext.GuideDetails.Find(Id);
            dbContext.Entry(guideDetail).State = System.Data.Entity.EntityState.Modified;
            guideDetail.FileName = FileName;
            guideDetail.Version = Version;
            guideDetail.CheckSum = CheckSum;
            guideDetail.FileSize = FileSize;
            return (dbContext.SaveChanges(false,Convert.ToInt32(userId)) > 0);
        }

        public List<GuideDetail> GetListGuideDetail()
        {
            var query = (from b in dbContext.GuideDetails
                         orderby b.Id ascending
                         select b).ToList();

            return query;
        }

        public List<GuideDetail> GetListGuideDetailEnabled()
        {
            var query = (from b in dbContext.GuideDetails
                         where b.IsEnable
                         orderby b.Id ascending
                         select b).ToList();

            return query;
        }

        public GuideDetail GetGuideDetail(long Id)
        {
            return dbContext.GuideDetails.Find(Id);
        }

        public void IncrementGuideDownload(long Id)
        {
            var guide = dbContext.GuideDetails.Find(Id);
            guide.TotalDownloads += 1;
            dbContext.SaveChanges();
        }

        public async Task<bool> UpdateMasterSpeciesStatusByCategory(long CategoryId)
        {
            //var speciesDetail = dbContext.SurveyMasterSpecies.Find(Id);
            //dbContext.Entry(speciesDetail).State = System.Data.Entity.EntityState.Modified;
            //speciesDetail.Status = status;
            bool ret = false;

            var spec = dbContext.SurveyMasterSpecies.Where(x => x.CategoryId == CategoryId).ToList();
            if(spec.Count > 0)
            {
                spec.ForEach(a => a.IsDeleted = true);
                var result = dbContext.SaveChanges();
                if (result > 0) ret = true;
            }
            else
                ret = true;

            return ret;
        }

        public IQueryable<SurveyMasterSpecies> GetSurveyMasterSpecies()
        {
            return  dbContext.SurveyMasterSpecies;
        }

        public async Task<SurveyMasterSpecies> GetSurveyMasterSpeciesById(long Id)
        {
            return await dbContext.SurveyMasterSpecies.FindAsync(Id);
        }
        public async Task<bool> AddSurveyMasterSpecies(SurveyMasterSpecies species)
        {
            dbContext.SurveyMasterSpecies.Add(species);
            bool ret = false;
            var result = await dbContext.SaveChangesAsync();
            if (result > 0) ret = true;
            return ret;
        }

        public async Task<bool> UpdateSurveyMasterSpecies(SurveyMasterSpecies species)
        {
            bool ret = false;
            SurveyMasterSpecies speciesDetail = dbContext.SurveyMasterSpecies.Find(species.Id);
            if(speciesDetail !=null)
            {
                dbContext.Entry(speciesDetail).State = System.Data.Entity.EntityState.Modified;

                speciesDetail.CommonName = species.CommonName;
                speciesDetail.Description = species.Description;
                speciesDetail.Grouping = species.Grouping;
                speciesDetail.FamilySuborder = species.FamilySuborder;
                speciesDetail.CategoryId = species.CategoryId;
                speciesDetail.Attributes = species.Attributes;
                speciesDetail.Others = species.Others;
                speciesDetail.ImagePath = species.ImagePath;
                speciesDetail.Photo1 = species.Photo1;
                speciesDetail.Photo2 = species.Photo2;
                speciesDetail.Photo3 = species.Photo3;
                speciesDetail.Photo4 = species.Photo4;
                speciesDetail.Species = species.Species;
                speciesDetail.IsDeleted = species.IsDeleted;
                speciesDetail.UpdatedAt = DateTime.Now;

               
                var result = await dbContext.SaveChangesAsync();
                if (result > 0) ret = true;
            }
          
            return ret;
        }

        

        #endregion

        #region Private Methods

        #endregion
    }
}
