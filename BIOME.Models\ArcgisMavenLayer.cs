﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ArcgisMavenLayer
    {
        public long ObjectID { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public string ServiceUrl { get; set; }
        public string LayerExtent { get; set; }
        public string CreatedBy { get; set; }
        public long? CreatedDate { get; set; }
        public string ModifiedBy { get; set; }
        public long? ModifiedDate { get; set; }
        public long? LayerID { get; set; }
        public string FeatureTable { get; set; }
        public long? LayerGroupID { get; set; }
        public string Alias { get; set; }
        public bool IsSystemLayer { get; set; }
        public long? LayerOrder { get; set; }
        public double? Opacity { get; set; }        
    }
}
