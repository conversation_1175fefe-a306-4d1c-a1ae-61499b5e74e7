﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class SightingInappropriate : Entity<long>,IDescribableEntity
    {
        public long AtSightingId { get; set; }
        [ForeignKey("AtSightingId")]
        public virtual SightingDetail AtSighting { get; set; }

        public long ByUserId { get; set; }

        public string Describe()
        {
            return "{ AtSightingId : \"" + AtSightingId + "\", ByUserId : \"" + ByUserId   + "}";
        }

        public SightingInappropriate()
        {

        }
    }
}
