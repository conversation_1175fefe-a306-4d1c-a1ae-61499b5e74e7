﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIOME.Models
{
    public class UserInfoBadge : Entity<long>
    {
        public long UserId { get; set; }
        public long BadgeId { get; set; }
        [ForeignKey("BadgeId")]
        public virtual BadgeDetail Badge { get; set; }
        public DateTimeOffset ReadedAt { get; set; }

        public UserInfoBadge()
        {

        }
    }
}
