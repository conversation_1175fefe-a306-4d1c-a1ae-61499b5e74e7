﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Constants
{
    public class Account
    {
        public class LoginType
        {
            public const string External = "external";
            public const string External_ADFS = "externalADFS";

            public const int LoginType_BIOME_Account = 1;
            public const int LoginType_AD = 2;
            public const int LoginType_ADFS = 3;
            public const int LoginType_Facebook = 4;
        }

        

        public class TokenPurpose
        {
            public const string ReactivateAccount = "ReactivateAccount";
        }

        public class Password
        {
            public const string RegexTester = @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{12,}";
            public const int PasswordHistoryLimit = 3;
        }
        public class Name
        {
            public const string RegexValidName = @"^[a-zA-Z\s.\-\',]*$";
            //public const int MaxCharLimit = 256;
            public const int MaxCharLimit = 66;
            public const int MaxCharLimitFirstName = 33;
            public const int MaxCharLimitLastName = 33;
        }

        public class Email
        {
            //public const string RegexValidEmail = @"^(?=.{1,320}$)([^@\s]{1,64})@([^\s@]+){1,255}$";
            public const string RegexValidEmail = @"^(?=.{1,320}$)([\w-\.]{1,64})@([\w-]+\.)+[\w-]{2,4}$";
            public const string RegexValidEmailLocalPart = @"^(?=.{1,64}$)([\w-\.]{1,64})$";
            public const int MaxCharLimit = 320;
            public const int LocalMaxCharLimit = 64;
        }

        public class UserGroup
        {
            public const string Public = "Public";
            public const string NParks = "National Parks Board (NPARKS)";
            public const string NBC = "National Biodiversity Centre";
        }

        public class AccountStatus
        {
            public const string ACTIVE = "ACTIVE";
            public const string SUSPENDED = "SUSPENDED";
            public const string DEACTIVATED = "DEACTIVATED";
        }

        public class API
        {
            public class AccountStatus
            {

                public const int UserNotFound = -700;
                public const int UserNotFoundOrInactive = -321;
                public const int InvalidLogin = -323;
                public const int AccountExpired = -200;
                public const int Unverified = -300;
                public const int Locked = -400;
                public const int PendingApproval = -500;
                public const int PasswordExpired = -600;
                public const int FacebookUserInvalid = -10000;
                public const int NotEmailConfirmed = -1008;
                public const int AccountWithoutRole = -1109;
            }

            public const int InvalidRequest = -777;
            // for auto logout in mobile apps
            public const int InvalidToken = -888;
            // new code for unauthenticated
            public const int UnAuthorized = -403;
        }

        public const int ExpireTimeSpanInMinutes = 15; //15Minutes
        public const int Identity_TokenLifeSpanInMinutes = 45; //
        
        public const int MobileUserSessionExpireResponseCode = -999; 
    }
    public static class IdentityExtensions
    {

        public static string GetADFS_UPN(this System.Security.Principal.IIdentity identity)
        {
            var claim = ((ClaimsIdentity)identity).FindFirst(ClaimTypes.Upn);
            // Test for null to avoid issues during local testing
            return (claim != null) ? claim.Value : string.Empty;
        }
        public static string GetADFS_UserName(this System.Security.Principal.IIdentity identity)
        {
            var claim = ((ClaimsIdentity)identity).FindFirst(ClaimTypes.NameIdentifier);
            // Test for null to avoid issues during local testing
            return (claim != null) ? claim.Value : string.Empty;
        }
        public static string GetADFS_Email(this System.Security.Principal.IIdentity identity)
        {
            var claim = ((ClaimsIdentity)identity).FindFirst(ClaimTypes.Email);
            // Test for null to avoid issues during local testing
            return (claim != null) ? claim.Value : string.Empty;
        }
        public static bool IsADFS_Autheticated(this System.Security.Principal.IIdentity identity)
        {
            return !string.IsNullOrEmpty(GetADFS_UPN(identity));
        }
    }
}
