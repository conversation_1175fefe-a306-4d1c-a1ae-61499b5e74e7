﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchType : Entity<long>, ISoftDelete, IDescribableEntity
    {
        public bool IsDeleted { get; set; }

        public bool IsEnable { get; set; }
        public string Name { get; set; }
        public string Describe()
        {
            return "{ Name : \"" + Name + "\", IsEnable : \"" + IsEnable + "\", IsDeleted : \"" + IsDeleted + "}";
        }
    }
}
