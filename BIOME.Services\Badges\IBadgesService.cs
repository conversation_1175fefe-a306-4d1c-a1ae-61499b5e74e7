﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BIOME.Models;
using BIOME.ViewModels;

namespace BIOME.Services
{
    public interface IBadgesService
    {
        bool IncreaseUserScore(long userId, string badgeCategory);
        bool SetUserScore(long userId, string badgeCategory, int score);

        List<BadgeDetail> GetAllBadgeList();
        List<UserInfoBadge> GetUserBadgeList(long userId);
        List<long> GetUserBadgeUserIDList();
        List<UserInfoScore> GetUserScoreList(long userId);
        BadgeDetail GetBadgeDetail(long badgeId);
        UserInfoBadge GetUserBadge(long userId, long badgeId);
        UserInfoScore GetUserScore(long userId, string badgeCategory);
        bool UpdateUserHasNewBadge(long userId, bool HasNewBadge);

        //Admin Report
        List<ReportViewModel.GroupNameNumber> GetBadgeUserNumberList();
    }
}
