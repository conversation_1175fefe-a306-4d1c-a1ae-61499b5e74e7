﻿using System;
using BIOME.ViewModels;
using BIOME.Models;
using System.Collections.Generic;
using System.Net.Mail;
using System.Threading.Tasks;

namespace BIOME.Services
{
    public interface IEmailService
    {
        bool SendResetPassword(string title, string recipient, string resetPasswordLink, string contactUsLink);
        bool SendPasswordResetSuccess(string title, string recipient, string contactUsLink);
        bool SendActivateAccount(string title, string recipient, string activateAccountLink, string contactUsLink);
        bool SendActivateAccountWithTempPassword(string title, string recipient, string tempPassword, string activateAccountLink, string contactUsLink);
        bool SendAccountLoginOTP(string title, string recipient, string optCode, string optExpiryDate, string contactUsLink);
        bool SendPasswordExpiry(string title, string recipient, string passwordExpiryDate, string passwordResetLink, string contactUsLink);
        bool SendPasswordExpiryNotification(string title, string recipient, string passwordExpiryDate, string passwordResetLink, string contactUsLink);
        bool SendAccountExpiry(string title, string recipient, string accountExpiryDate, string accountResetLink, string contactUsLink);
        bool SendAccountExpiryFromScheduler(string title, string recipient, string accountExpiryDate, string accountResetLink, string contactUsLink); //CR3&CR4 Phase1
        bool SendAccountExpiryNotification(string title, string recipient, string accountExpiryDate, string accountResetLink, string contactUsUrl);
        //bool SendAccountSuppendOrDeAactivate(string recipient, string UserID, string userName, string roles, string Datesuppend, string DateDeactive, string Status, bool isTransferred);
        //bool SendAccountChangeSummary(string recipient, string list_updated_users, string list_failed_toupdate_users, string list_users_not_exist, string ace_file_record_count,string errorSummary); //Account Log Audit CR
        bool SendAccountResetSuccess(string title, string recipient, string loginLink, string contactUsLink);
        bool SendNewCommentSighting(string recipient, string personName, string fullName, string profileUrl, string sightingIdString, string detailLink, string comment, string contactUsLink);
        bool SendSightingVerified(string recipient, string personName, string sightingTitle, string sightingIdString, string detailLink, string contactUsLink);
        bool SendChangeSightingName(string recipient, string personName, string sightingTitle, string sightingIdString, string detailLink, string contactUsLink);
        bool SendInappropriateSightingWarning(string recipient, string personName, string sighting_date_stamp, string sighting_time_stamp, string sightingTitle, string sightingDetailLink, string contactUsLink);
        bool SendInappropriateSightingWarningAdmin(string recipient, string personName, string sighting_date_stamp, string sighting_time_stamp, string sightingTitle, string sightingDetailLink, string authorName, string authorProfileLink, string flaggerName, string flaggerProfileLink, string adminEditLink, string contactUsLink);
        bool SendAccountBlacklisted(string recipient, string personName, string mySightingLink, string contactUsLink);
        bool SendNewFollower(string title, string recipient, int numberOfFollowers, string profileLink, string contactUsLink);
        bool SendRequestProject(string recipient, string personName, string projectTitle, string detailLink, string adminProjectLink, string applicantFullname, string contactUsLink);
        bool SendApproveMembership(string recipient, string personName, string status, string projectTitle, string projectIdString, string detailLink, string ownerFullname, string ownerEmail, string contactUsLink);
        bool SendJoinProject(string recipient, string personName, string projectTitle, string projectIdString, string detailLink, string profileLink, string applicantFullname, string contactUsLink);
        bool SendSuspendProject(string recipient, string personName, string projectIdString, string projectTitle, string projectStatus, string detailLink, string myProjectsLink, string contactUsLink);
        bool SendApprovalProject(string recipient, string personName, string projectIdString, string projectTitle, string projectStatus, string detailLink, string myProjectsLink, string contactUsLink);
        bool SendReinstateProject(string recipient, string personName, string projectIdString, string projectTitle, string projectStatus, string detailLink, string myProjectsLink, string contactUsLink);

        //bool SendReactivateAccount(string title, string recipient, string reactivateAccountLink);

        bool SendPermitApplicationNotificationToSiteManager(string title, string recipient, string applicationType, string permitNumber, string researchTitle, string applicantName, string applicationUrl, string contactUsUrl);
        bool SendPermitApplicationNotificationToPermitManagerOrSiteManager(string title, string recipient, string applicationType, string applicationId, string applicationStatus, string researchTitle, string applicationUrl, string contactUsUrl);
        bool SendPermitApplicationNotificationToApplicant(string title, string recipient, string applicationType, string applicationId, string applicationStatus, string researchTitle, string applicationUrl, string contactUsUrl);
        bool SendPermitApplicationNotificationToPermitManager(string title, string recipient, string applicationType, string applicationId, string applicationSummary, string researchTitle, string applicantName, string applicationUrl, string contactUsUrl, string attachmentFilePath, string miscAttachment);
        bool SendPermitApplicationSiteApprovedToPermitManager(string title, string recipient, string applicationType, string applicationId, string researchTitle, string siteManagerName, string nameOfSites, string applicationUrl, string contactUsUrl);
        bool SendPermitApplicationApprovedToApplicant(string title, string recipient, string researchTitle, string applicationId, string permitUrl, string attachmentFilePath, string passFilePath, string applicationUrl, string contactUsUrl);
        bool SendPermitApplicationAmendedToApplicant(string title, string recipient, string researchTitle, string applicationId, string permitUrl, string attachmentFilePath, string passFilePath, string applicationUrl, string contactUsUrl, string applicationType);
        bool SendReminderToApplicant(string title, string recipient, string researchTitle, string applicationId, string permitExpiryDate, string permitUrl, string contactUsUrl);
        bool SendSightingCensored(string recipient, string personName, string sightingTitle, string sightingIdString, string detailUrl, string contactUsUrl);
        
        bool SendProjectInviteMember(string recipient, string personName, string projectTitle, string projectIdString, string detailUrl, string joinUrl, string contactUsUrl,long recipientId, long audit_userId);
        bool SendSurveyInviteMember(string recipient, string personName, string surveyTitle, string surveyIdString, string detailUrl, string joinUrl, string contactUsUrl, long recipientId, long audit_userId);
        bool SendSiteVisitNotificationToSiteManager(string researchPermitId, string researchPermitTitle, string title, string recipient, string permitApplicationUrl, string applicantName, string siteVisitDetail, string siteVistiUrl, string contactUsUrl);
        //bool TestEmail();
        Task SendEmailAsync(EmailTemplateViewModel emailTemplateVM);
        Task SendEmail(EmailTemplateViewModel emailTemplateVM);
        Task SendEmailviaSendGridAPI(EmailTemplateViewModel emailTemplateVM); //GCC BIOME internet
        void SendTestingEmail(string to, string subject);
        Task SendTestingEmailSend(string to, string subject);
        bool SendDiscussionNotificationEmail(string title, string recipient, string newQuestionLink, string newQuestion, string nameOfUserWhoPosted, string permitApplicationUrl, string contactUsUrl, string researchPermitTitle, string permiteApplicationId,bool isApplicant,List<BIOME.ViewModels.DiscussionForumViewModel.DiscussionForumAttachDocumentModel> attachDocumentVMList);
        bool SendDiscussionDelete_NotificationEmail(string title, string recipient, string newQuestionLink, string newQuestion, string nameOfUserWhoDeleted, string permitApplicationUrl, string contactUsUrl, string researchPermitTitle, string permiteApplicationId); //CR3&CR4 Phase1
        //bool SendDiscussionAnswerNotificationEmail(string title, string recipient, string newAnswerLink, string newAnswer);
        //bool SendDiscussionReminderEmail(string title, string recipient, string questionLink, string question);
        bool SendDiscussionForumInvitationToPI(string title, string recipient, string signupUrl, string researchPermitTitle, string permitApplicationUrl); //CR3&CR4 Phase1
        bool SendAssignMainApplicantSubmitted(string title, string recipient, string previous_applicant_name, string previous_applicant_email, string researchPermitTitle, string permitApplicationUrl); //CR3&CR4 Phase1
        //bool SendFeedbackEmail(string[] recipients, string user, string userEmail, string category, string content);
        Task<bool> SendFeedbackEmail(string[] recipients, string user, string userEmail, string category, string content);
        EmailTemplate GetEmailTemplate(string TemplateName);
        List<EmailTemplateViewModel.EmailTemplateEditViewModel> GetEmailTemplateList();
        bool UpdateEmailTemplate(EmailTemplateViewModel.EmailTemplateEditViewModel emailTemplateEditViewModel, int userId);
        bool SendEmailTemplate(string TemplateName, Dictionary<string, string> Fields, string To, ICollection<Attachment> attachments = null, string cc = "");
        List<string> GetFieldName(string emailBody);
        string ConvertInternetURL(string urlString);
        string ConvertIntranetURL(string urlString);
        bool SendUploadResourcePermission(string recipient, string FristName, string NameOfRequester, string url_pub_my_profile_page, string AccountUrl);
        bool SendUploadResourcePermissionApprove(string recipient, string personName);
        bool SendEmailToAdminForResourceUpload(string recipient, string personName, string MetaTitle, string submittedBy, string ResourceDocUrl, string url_resource_approval,string uploader_profile_link, string uploader_email);
        bool SendEmailResourceUploadApprovalStatusChanged(string recipient, string personName, string status, string MetaTitle, string ResourceDocUrl);
        bool SendEmailToSiteManagerForResourceUpload(string recipient, string personName, string MetaTitle, string submittedBy, string ResourceDocUrl, string uploader_profile_link, string research_applicaton_link, string permit_title);
        bool SendEmailToPermitManagerForResourceUpload(string recipient, string personName, string MetaTitle, string submittedBy, string ResourceDocUrl, string uploader_profile_link, string research_applicaton_link, string permit_title);
        bool SendEmailReminderToSiteManagerForPermitApplication(string recipient, string personName, string NameOfApplicant, string url_pub_my_profile_page, string applicationUrl, string dateSubmiited, string applicationtitle,string contactUsUrl);
        bool SendEmailReminderToSiteManagerForPermitApplicationPending(string recipient, string personName, string NameOfApplicant, string url_pub_my_profile_page, string applicationUrl, string dateSubmiited, string applicationtitle, string contactUsUrl);
        bool SendDownloadResourcePermission(string recipient, string cc, string personName, string NameOfRequester, string url_pub_my_profile_page, string MetaTitle, string ResourceDocUrl, string AccountPermissionUrl
            , long recipientId, long audit_userId,long permissionId, long CCId);
        bool SendDownloadResourcePermissionStatus(string recipient, string cc, string personName, string status, string MetaTitle, string ResourceDocUrl);
        bool SendEmailReminderToPermitManagerForPermitApplication(string recipient, string personName, string NameOfApplication, string Date, string EmailTemplate, long recipientId, long permitId, string contactUsUrl);
        bool CustomSendEmail(EmailTemplateViewModel emailTemplateVM);
        bool SendRequestSurvey(string recipient, string personName, string surveyTitle, string detailUrl, string adminSurveyUrl, string applicantFullname, string contactUsUrl);
        bool SendApproveOrRejectSurvey(string recipient, string personName, string surveyTitle, string surveyIdString, string surveyStatus, string detailUrl, string adminSurveyUrl, string contactUsUrl);

        bool SendApproveOrRejectAmendmentSurveyToMember(string recipient, string personName, string surveyTitle, string surveyIdString, string surveyStatus, string contactUsUrl,string survey_requestor_name, string survey_requestor_emaill, string survey_admin_list);
        bool SendAmendmentPendingSurveyToMember(string recipient, string personName, string surveyTitle, string surveyIdString, string contactUsUrl, string survey_requestor_name, string survey_requestor_email, string survey_admin_list);

        bool SendAmendmentPendingSurveyToSystemAdmin(string recipient, string personName, string surveyTitle, string survey_requestor_name, string survey_requestor_email, string amendment_submitted_by_name, string amendment_submitted_by_email, string contactUsUrl);
        bool SendAdminEditSurvey(string recipient, string personName, string surveyTitle, string detailUrl, string adminSurveyUrl, string contactUsUrl, bool isOwner, string edited_by_sysadmin_name, string edited_by_sysadmin_email);

        bool AddEmailOTP(long userId, string email, string code, DateTime createdDatetime);
        bool IsValidEmailOTP(long userId, string code);
        bool SendAuditLogMonthlyReport(string title, string startDate, string endDate, string recipient, string reportPath, string reportName, string cc, string startMonthYear, string sysadmin_list);
        bool Send_E_AcknowledgementForPermitApplication(string title, string recipient, string main_applicant, string main_applicant_email, string research_permit_title, string sign_link, string sign_link_expire_date, string AcknowledgeSignLinkCode);
        bool Send_To_MainApplicant_E_AcknowledgementSignedByAllMembers(string title, string recipient, string research_permit_title,string applicationType);
        bool SendAssignedSurveyOwner(string recipient, string personName, string surveyTitle, string contactUsUrl);

        bool Send_USER_ACCOUNT_DISABLEMENT_CAM(string recipient, string staff_name, string list_updated_users, string errorSummary); //CAM API
        bool Send_USER_ACCOUNT_REMOVAL_CAM(string recipient, string staff_name, string list_updated_users, string errorSummary); //CAM API

    }
}