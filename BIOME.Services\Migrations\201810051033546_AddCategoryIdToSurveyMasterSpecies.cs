namespace BIOME.Services
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddCategoryIdToSurveyMasterSpecies : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.SurveyMasterSpecies", "CategoryId", c => c.Long());
            CreateIndex("dbo.SurveyMasterSpecies", "CategoryId");
            AddForeignKey("dbo.SurveyMasterSpecies", "CategoryId", "dbo.GuideDetails", "Id");
            DropColumn("dbo.SurveyMasterSpecies", "Category");
            DropColumn("dbo.SurveyMasterSpecies", "CategoryName");
        }
        
        public override void Down()
        {
            AddColumn("dbo.SurveyMasterSpecies", "CategoryName", c => c.String());
            AddColumn("dbo.SurveyMasterSpecies", "Category", c => c.String());
            DropForeignKey("dbo.SurveyMasterSpecies", "CategoryId", "dbo.GuideDetails");
            DropIndex("dbo.SurveyMasterSpecies", new[] { "CategoryId" });
            DropColumn("dbo.SurveyMasterSpecies", "CategoryId");
        }
    }
}
