{"compilerOptions": {"target": "ES5", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": false, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": false, "noEmit": true, "declaration": false, "outDir": "./Scripts/compiled", "rootDir": "./<PERSON><PERSON><PERSON>"}, "include": ["Scripts/**/*.ts", "Scripts/**/*.d.ts"], "exclude": ["node_modules", "Scripts/compiled", "Scripts/**/*.js", "Content/**/*.js"]}