﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class MaintenanceNotice:Entity<long>,IDescribableEntity
    {
        public bool status { get; set; }
        public DateTimeOffset startFrom { get; set; }
        public DateTimeOffset endAt { get; set; }
        public string noticeTitle { get; set; }
        public string noticeMessage { get; set; }
        public string Describe()
        {
            return "{ status : \"" + status + "\", startFrom : \"" + startFrom + "\", endAt : \"" + endAt + "\", noticeTitle : \"" + noticeTitle
                + "\", noticeMessage : \"" + noticeMessage  + "}";
        }
    }
}
