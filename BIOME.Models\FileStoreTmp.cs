﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class FileStoreTmp : Entity<long>
    {

        [Required]
        public string FileName { get; set; }
        [Required]
        public byte[] Data { get; set; }
        [Required]
        public long ObjectID { get; set; }
        public bool hasDownloadedToInternetApp { get; set; }
        public bool hasDownloadedToIntranetApp { get; set; }
    }
}
