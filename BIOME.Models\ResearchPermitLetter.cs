﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchPermitLetter : Entity<long>, IDescribableEntity 
    {
        public string PermitNumber { get; set; }
        public string Date { get; set; }
        public string ReceiverName { get; set; }
        public string ReceiverAddress { get; set; }
        public string TitleName { get; set; }
        public string Part1 { get; set; }
        public string Part2 { get; set; }
        public string LetterFileName { get; set; }
        public string AttachmentFileNames
        {
            get
            {
                return string.Join(",", attachmentsFileNames);
            }
            set
            {
                if (value == null)
                {
                    attachmentsFileNames = new List<string>();
                    return;
                }
                attachmentsFileNames = value.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList();
            }
        }
        public string Describe()
        {
            return "{ PermitNumber : \"" + PermitNumber + "\", Date : \"" + Date + "\", ReceiverName : \"" + ReceiverName
                + "\", ReceiverAddress : \"" + ReceiverAddress + "\", TitleName : \"" + TitleName
                + "\", Part1 : \"" + Part1 + "\", Part2 : \"" + Part2 + "\", LetterFileName : \"" + LetterFileName + "\", AttachmentFileNames : \"" + AttachmentFileNames
                + "}";
        }

        private List<string> attachmentsFileNames = new List<string>();
        [NotMapped]
        public List<string> AttachmentsFileNames
        {
            get
            {
                return attachmentsFileNames;
            }
            set
            {
                attachmentsFileNames = value;
            }
        }
        [JsonIgnore]
        public virtual ICollection<ResearchPermitStatus> Status { get; set; }
        public virtual ApplicationUser ActionByPermitManager { get; set; }
        [JsonIgnore]
        public virtual ResearchPermitApplication AtResearchPermitApplication { get; set; }

        public ResearchPermitLetter()
        {
            Status = new List<ResearchPermitStatus>();
        }
    }
}
