// <auto-generated />
namespace BIOME.Services
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class softdeletetoresourcemeta : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(softdeletetoresourcemeta));
        
        string IMigrationMetadata.Id
        {
            get { return "201701100231565_softdelete to resource meta"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
