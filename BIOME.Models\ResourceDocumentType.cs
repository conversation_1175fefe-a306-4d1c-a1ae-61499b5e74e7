﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResourceDocumentType : Entity<long>,IDescribableEntity
    {
        public string DocumentTypeName { get; set; }
        public bool IsEnable { get; set; }

        public string Describe()
        {
            return "{ DocumentTypeName : \"" + DocumentTypeName + "\", IsEnable : \"" + IsEnable  + "}";
        }

        [JsonIgnore]
        public virtual ICollection<ResourceDocument> ResourceDocuments { get; set; }

        public ResourceDocumentType()
        {
            ResourceDocuments = new List<ResourceDocument>();
        }
    }
}
