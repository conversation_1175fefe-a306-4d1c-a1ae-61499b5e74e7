﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BIOME.Models;
using BIOME.ViewModels;

namespace BIOME.Services
{
    public interface IAdvanceSearchService
    {
        string ElasticsearchServer { get; }
        IEnumerable<SightingDetail> SearchSightingsList(string queryString);
        List<SightingDetail> SearchSightingsList(AdvanceSearchViewModel.AdvanceQueryViewModel advanceQueryViewModel);

        List<SightingDetail> SearchProjectList(AdvanceSearchViewModel.AdvanceQueryViewModel advanceQueryViewModel);

        List<ResourceDocument> SearchResourceDocumentList(AdvanceSearchViewModel.AdvanceQueryViewModel advanceQueryViewModel,long groupId , bool skipGroupFilter);

        List<ApplicationStatusViewModel.ResearchApplicationES> SearchResearchApplicationList(AdvanceSearchViewModel.AdvanceQueryViewModel advanceQueryViewModel);

        bool CanElasticsearchServerConnect();
        string GetElasticSearchIndices();

    }
}
