﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResourceStructureTemplateColumn : Entity<long>, IDescribableEntity
    {
        public string ColumnName { get; set; }
        public virtual ResourceStructureTemplate AtResourceStructureTemplate { get; set; }
        public string Describe()
        {
            return "{ ColumnName : \"" + ColumnName  
                  + "}";
        }
    }
}
