﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Constants
{
    public static class Research
    {
        public static class Permit
        {
            public static class Number
            {
                public const string Prefix = @"NP/RP";
                public const string RegexTester = @"NP\/RP(\d{2,2})-(\d{3,3})(-(\d+)([a-z]*)|([a-z]*))";
                public const string OriginalNoRegexTester = @"NP\/RP(\d{2,2})-(\d{3,3})";
                public const string RenewAmendTester = @"(\d+)([a-z]*)";
                public const string SuffixReject = "R";
            }

            public static class ApplicationSubMenu
            {
                public const string Terms = "Terms & Conditions";
                public const string Online = "Online Application";
                public const string FAQ = "FAQ/Help";
                public const string Contact = "Contact Information";
            }

            public static class Status
            {
                public const string Draft = "Draft";
                public const string InProgress = "InProgress";
                public const string Approved = "Approved";
                public const string Rejected = "Rejected";
                public const string Original = "Original";
                public const string Issued = "Issued";
                public const string UserAmended = "UserAmended";
                public const string ManagerAmended = "ManagerAmended";
                public const string Discard = "Discard";

                public const string Expired = "Expired";
                public const string Pending_eSign = "Pending e-Sign";  //Frr display only. Actual permit status is Draft.If any member haven't done e-sign, status will be display as "Pending e-Sign". eSign CR. 2022
            }
            public static DateTime CR3_EffectedDate = new DateTime(2020, 11, 13, 0, 0, 0); //Added for CR3. Research Permit Application before CR3_EffectedDate will still use Interim report linked to permit applicatin. Otherwise, use reports linked to ResearchPermitApplication_Id
        }

        public static class ApplicationStatus
        {

            public static class ApplicationStatusSubBreadcrumb
            {
                public const string applicationHistoryList = "Application History List";
                public const string applicationList = "Application Listing";
                public const string applicationDetails = "Application Details";
                public const string discussionForum = "Discussion Forum";
                public const string viewPass = "View Pass";
                public const string viewLetter = "View Letter"; 
            }

            public static class DiscussionForum
            {

                public const int DefaultPageSize = 3;
            }
        }

        public const int TeamMemberMaxFileSizeKB = 200; //in KB
        public const string TeamMemberMaxFileSizeError = "Invalid image file size. File size exceeds maximum allowed file size of 200KB.";

        public const int ACK_Sign_Link_Valid_Day = 15; //eSign CR. 2022.
    }
}
