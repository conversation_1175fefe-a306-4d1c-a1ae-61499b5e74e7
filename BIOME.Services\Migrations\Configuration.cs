﻿using AutoMapper;
using BIOME.Constants;
using BIOME.Models;
using BIOME.Services;
using CsQuery.ExtensionMethods.Internal;
using CsvHelper;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using Microsoft.AspNet.Identity.Owin;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.Entity.Migrations;
using System.IO;
using System.Linq;
using System.Reflection;
using Utilities.Helpers;
using static BIOME.Constants.Configuration.Server;

namespace BIOME.Services
{
    internal sealed class Configuration : DbMigrationsConfiguration<BIOME.Services.ApplicationDbContext>
    {
        public Configuration()
        {
            AutomaticMigrationsEnabled = true;
            //AutomaticMigrationDataLossAllowed = true;
            AutomaticMigrationDataLossAllowed = false;
            ContextKey = "BIOME.Services.ApplicationDbContext";
        }

        protected override void Seed(BIOME.Services.ApplicationDbContext context)
        {
            // Password migration
            var oldHasher = new BIOMEPasswordHasher();
            var newHasher = new BIOMEPasswordHasher();

            // Get all users
            var users = context.Users.ToList();
            
            foreach (var user in users)
            {
                try
                {
                    if (string.IsNullOrEmpty(user.PasswordHash)) continue;

                    // Skip if Key or IV is missing
                    if (string.IsNullOrEmpty(user.Key) || string.IsNullOrEmpty(user.IV)) continue;

                    // Set the dynamic password cipher values from the user's stored values
                    DynamicPasswordCipher.IV = user.IV;
                    DynamicPasswordCipher.Key = user.Key;

                    // Decrypt the old password using the user-specific key and IV
                    string decryptedPassword = oldHasher.Decrypt(
                        ByteHelper.ConvertHexStringToByteArray(user.PasswordHash),
                        ByteHelper.ConvertHexStringToByteArray(user.Key),
                        ByteHelper.ConvertHexStringToByteArray(user.IV)
                    );

                    // Hash the password with the new PBKDF2 method
                    string newHashedPassword = newHasher.HashPassword(decryptedPassword);

                    // Update the user's password hash
                    user.PasswordHash = newHashedPassword;
                    
                    // Clear the encryption key and IV as they're no longer needed
                    user.Key = null;
                    user.IV = null;
                    
                    context.Entry(user).State = EntityState.Modified;
                }
                catch (Exception ex)
                {
                    // Log the error but continue with other users
                    // We don't want to expose which user caused an error in logs
                    Console.WriteLine($"Error migrating password for a user: {ex.Message}");
                }
            }

            // Save all changes
            context.SaveChanges();

            // Continue with existing seed operations
            ConfigurationService.Instance.ConfigMapping();

            //  This method will be called after migrating to the latest version.

            //  You can use the DbSet<T>.AddOrUpdate() helper extension method 
            //  to avoid creating duplicate seed data. E.g.
            //
            //    context.People.AddOrUpdate(
            //      p => p.FullName,
            //      new Person { FullName = "Andrew Peters" },
            //      new Person { FullName = "Brice Lambson" },
            //      new Person { FullName = "Rowan Miller" }
            //    );
            //

            try
            {

               /* try {

                        string SQLuserEmailSizeChange = @"
                        IF NOT EXISTS (
                            SELECT 1
                            FROM INFORMATION_SCHEMA.COLUMNS
                            WHERE TABLE_NAME = 'AspNetUsers'
                            AND COLUMN_NAME = 'Email'
                            AND CHARACTER_MAXIMUM_LENGTH = 320
                        )
                        BEGIN
                            ALTER TABLE dbo.AspNetUsers
                            ALTER COLUMN Email NVARCHAR(320);
                        END";
                        context.Database.ExecuteSqlCommand(SQLuserEmailSizeChange);
                }
                catch (Exception)
                {
                    //ignore as no action required.
                }*/
               

                var userManager = new ApplicationUserManager(new BIOMEUserStore(context), new IdentityFactoryOptions<ApplicationUserManager>()
                {
                    DataProtectionProvider = new Microsoft.Owin.Security.DataProtection.DpapiDataProtectionProvider("BIOME")
                });

                var roleManager = new ApplicationRoleManager(new RoleStore<BIOMERole, long, BIOMEUserRole>(context));

                var migrationService = new MigrationService(userManager, roleManager, context);
                migrationService.AddRole(UserRoles.SystemAdmin);
                migrationService.AddRole(UserRoles.PermitManager);
                migrationService.AddRole(UserRoles.SiteManager);
                migrationService.AddRole(UserRoles.Expert);
                migrationService.AddRole(UserRoles.ResourceUploader);
                migrationService.AddRole(UserRoles.ProjectAdmin);
                migrationService.AddRole(UserRoles.ProjectMember);
                migrationService.AddRole(UserRoles.Public);
                //migrationService.AddSystemAdmin("", ""); //Only use this to add admin users for the first time.

#if (DEBUG || STAGING)
                //migrationService.AddSystemAdmin("", "");//Only use this to add admin users for the first time.

                migrationService.AddUsers("<EMAIL>", "Password1!", UserRoles.PermitManager);
                migrationService.AddUsers("<EMAIL>", "Password1!", UserRoles.SiteManager);
                migrationService.AddUsers("<EMAIL>", "Password1!", UserRoles.Expert);
                migrationService.AddUsers("<EMAIL>", "Password1!", UserRoles.ResourceUploader);
                migrationService.AddUsers("<EMAIL>", "Password1!", UserRoles.ProjectAdmin);
                migrationService.AddUsers("<EMAIL>", "Password1!", UserRoles.ProjectMember);
                migrationService.AddUsers("<EMAIL>", "Password1!", UserRoles.Public);
#endif
                var devInfo = new DeveloperInfo()
                {
                    Description = "IDA BIOME Mobile Backend",
                    DeveloperKey = "de66790361bd2c46e2ee74cca4c0b6f1ff3288a96d880174aecfe0d54f559dd2",
                    DeveloperSalt = "ea244cddff4e66117be98d9b015a8657d22dbda7155878f39427135d178f3f36",
                    IsActive = true
                };
                migrationService.AddDeveloperInfo(devInfo);

                string binPath = Path.GetDirectoryName((new System.Uri(Assembly.GetExecutingAssembly().CodeBase)).LocalPath);
                var path = Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "bin");

                List<MainGroup> mainGroups = new List<MainGroup>();
                //using (var sr = new StreamReader(Path.Combine(binPath, @"Group.csv")))
                //{
                //    var csv = new CsvReader(sr);
                //    while (csv.Read())
                //    {
                //        var mainGroup = new MainGroup();
                //        mainGroup.Name = csv.GetField<string>(0);

                //        migrationService.AddMainGroup(mainGroup);
                //    }
                //}

#if (DEBUG || STAGING)
                if (context.LayerGroup.Where(lg => lg.GroupName == "BIOME_RESOURCE_DOCUMENT").Count() == 0)
                {
                    using (var sr = new StreamReader(Path.Combine(binPath, @"ResourceDocument.csv")))
                    {
                        var csv = new CsvReader(sr);
                        while (csv.Read())
                        {
                            MapLayerGroup layerGroup = new MapLayerGroup()
                            {
                                LayerName = csv.GetField<string>(1),
                                GroupName = csv.GetField<string>(2),
                                HumanLayerName = csv.GetField<string>(3),
                                GeometryType = csv.GetField<string>(4)
                            };

                            migrationService.AddLayerGroup(layerGroup);
                        }
                    }
                }

                if (context.LayerGroup.Where(lg => lg.GroupName != "BIOME_RESOURCE_DOCUMENT").Count() == 0)
                {
                    using (var sr = new StreamReader(Path.Combine(binPath, @"Maven.csv")))
                    {
                        var csv = new CsvReader(sr);
                        while (csv.Read())
                        {
                            MapLayerGroup layerGroup = new MapLayerGroup()
                            {
                                LayerName = csv.GetField<string>(1),
                                GroupName = csv.GetField<string>(2),
                                HumanLayerName = csv.GetField<string>(3),
                                GeometryType = csv.GetField<string>(4)
                            };

                            migrationService.AddLayerGroup(layerGroup);
                        }
                    }
                }
#endif

                if (context.BadgeDetails.Count() == 0)
                {
                    //Fix for first add giving wrong id
                    var temp = new BadgeDetail();
                    context.BadgeDetails.Add(temp);
                    context.SaveChanges();
                    context.BadgeDetails.Remove(temp);
                    context.SaveChanges();

                    using (var sr = new StreamReader(Path.Combine(binPath, @"Biome_Badges.csv")))
                    {
                        var csv = new CsvReader(sr);
                        while (csv.Read())
                        {
                            BadgeDetail badgeDetail = new BadgeDetail();
                            badgeDetail.Id = csv.GetField<int>(0);
                            badgeDetail.Title = csv.GetField<string>(2);
                            badgeDetail.Description = csv.GetField<string>(3);
                            badgeDetail.BadgeCategory = csv.GetField<string>(4);
                            badgeDetail.TargetScore = csv.GetField<int>(5);
                            badgeDetail.FileName = csv.GetField<string>(1) + ".png";
                            badgeDetail.IsActive = (csv.GetField<int>(7) > 0);
                            badgeDetail.IsHidden = (csv.GetField<int>(11) > 0);

                            migrationService.AddBadge(badgeDetail);
                        }
                    }
                }

                if (context.GuideDetails.Count() == 0)
                {
                    //Fix for first add giving wrong id
                    var temp = new GuideDetail();
                    context.GuideDetails.Add(temp);
                    context.SaveChanges();
                    context.GuideDetails.Remove(temp);
                    context.SaveChanges();

                    using (var sr = new StreamReader(Path.Combine(binPath, @"Guide.csv")))
                    {
                        var csv = new CsvReader(sr);
                        while (csv.Read())
                        {
                            GuideDetail guideDetail = new GuideDetail();
                            guideDetail.Id = csv.GetField<int>(0);
                            guideDetail.Title = csv.GetField<string>(1);
                            guideDetail.CheckSum = csv.GetField<string>(2);
                            guideDetail.FileName = csv.GetField<string>(3);
                            guideDetail.Version = csv.GetField<string>(4);
                            guideDetail.FileSize = csv.GetField<int>(7);
                            guideDetail.IsEnable = true;

                            migrationService.AddGuide(guideDetail);
                        }
                    }
                }


                using (var sr = new StreamReader(Path.Combine(binPath, @"EmailTemplate.csv")))
                {
                    var csv = new CsvReader(sr);
                    while (csv.Read())
                    {
                        EmailTemplate emailTemplate = new EmailTemplate();
                        emailTemplate.Id = csv.GetField<int>(0);
                        emailTemplate.TemplateName = csv.GetField<string>(1);
                        emailTemplate.TemplateName = emailTemplate.TemplateName.Trim();
                        emailTemplate.Subject = csv.GetField<string>(2);
                        emailTemplate.Subject = emailTemplate.Subject.Replace("&lt;", "<").Replace("&gt;", ">").Trim();
                        emailTemplate.EmailBody = csv.GetField<string>(3);
                        emailTemplate.EmailBody = emailTemplate.EmailBody.Replace("&lt;", "<").Replace("&gt;", ">").Trim();

                        string[] templateToBeIgnore = { "ACCOUNT_STATUS_CHANGE_SUMMARY", "ACCOUNT_STATUS_CHANGE", "ACCOUNT_STATUS_CHANGE_EXIT" };

                        if (!templateToBeIgnore.Contains(emailTemplate.TemplateName)) {

                            migrationService.AddEmailTemplate(emailTemplate);
                        }

                        

                        /*if (!emailTemplate.TemplateName.Contains("[New"))
                        {
                            if (!emailTemplate.TemplateName.Equals("NEW_ACCOUNT_ACTIVATION_WITH_TEMP_PASSWORD", StringComparison.OrdinalIgnoreCase))
                            {
                                if (emailTemplate.TemplateName.Equals("JOIN_PROJECT_SENT_TO_PROJECT_ADMIN", StringComparison.OrdinalIgnoreCase))
                                {
                                    migrationService.UpdateEmailTemplateName("JOIN_PROJECT_SENT_TO_PROJECT_ADMIN", "JOIN_PROJECT_SENT_TO_PROJECT_REQUESTOR");
                                }
                                else
                                {
                                    migrationService.AddEmailTemplate(emailTemplate);
                                }
                                
                            }
                            
                        }*/
                    }
                }

                //new document type

                if (context.ResourceDocumentTypes.Where(t => t.DocumentTypeName == Resource.ReourceDocumentType.PermitReport).Count() == 0)
                {
                    context.ResourceDocumentTypes.Add(new ResourceDocumentType() { DocumentTypeName = Resource.ReourceDocumentType.PermitReport, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now, IsEnable = true });
                }

                //using (var sr = new StreamReader(Path.Combine(binPath, @"Category.csv")))
                //{
                //    var csv = new CsvReader(sr);
                //    while (csv.Read())
                //    {
                //        ConfSightingCategory confSightingCategory = new ConfSightingCategory();
                //        confSightingCategory.Id = csv.GetField<int>(0);
                //        confSightingCategory.CategoryName = csv.GetField<string>(1);
                //        confSightingCategory.Description = csv.GetField<string>(2);
                //        confSightingCategory.ParentId = csv.GetField<int>(3);
                //        confSightingCategory.SetDefault = csv.GetField<string>(4);
                //        confSightingCategory.TopParentId = csv.GetField<int>(5);
                //        confSightingCategory.BasicCategoryId = 0;
                //        string fileBasicCategoryId = csv.GetField<string>(6);
                //        if (fileBasicCategoryId == "2") confSightingCategory.BasicCategoryId = 2;
                //        else if (fileBasicCategoryId == "3") confSightingCategory.BasicCategoryId = 3;
                //        else if (fileBasicCategoryId == "-1") confSightingCategory.BasicCategoryId = -1;
                //        confSightingCategory.UserId = csv.GetField<int>(7);
                //        confSightingCategory.ExpertEmail = csv.GetField<string>(8);
                //        if (confSightingCategory.ExpertEmail == "NULL") confSightingCategory.ExpertEmail = "";
                //        confSightingCategory.CreatedBy = csv.GetField<string>(9);
                //        confSightingCategory.ModifiedBy = csv.GetField<string>(11);
                //        if (confSightingCategory.ModifiedBy == "NULL") confSightingCategory.ModifiedBy = "";

                //        migrationService.AddSightingCategoryInit(confSightingCategory);
                //    }
                //}

                //var checkNewGroupData = context.Groups.Find(9)?.Name == "NATIONAL PARKS BOARD (NPARKS)";
                //if (!checkNewGroupData)
                //{
                //    //context.Database.ExecuteSqlCommand("DELETE FROM [dbo].[Groups]");
                //}

                if (context.Groups.Count() == 0)
                {
                    //Fix for first add giving wrong id
                    var temp = new MainGroup();
                    temp.IsDeleted = false;
                    temp.Name = "123";
                    context.Groups.Add(temp);
                    context.SaveChanges();
                    temp = (MainGroup)context.Groups.Find(1);
                    context.Groups.Remove(temp);
                    context.SaveChanges(true);

                    using (var sr = new StreamReader(Path.Combine(binPath, @"UserGroup_New.csv")))
                    {
                        var csv = new CsvReader(sr, new CsvHelper.Configuration.CsvConfiguration() { HasHeaderRecord = false });
                        while (csv.Read())
                        {
                            var discriminator = csv.GetField<string>(8);
                            if (discriminator == nameof(MainGroup))
                            {
                                var mainGroup = new MainGroup();
                                mainGroup.Id = csv.GetField<long>(0);
                                mainGroup.IsDeleted = csv.GetField<bool>(1);
                                mainGroup.Name = csv.GetField<string>(2);
                                mainGroup.CreatedAt = csv.GetField<DateTimeOffset>(3);
                                mainGroup.UpdatedAt = csv.GetField<DateTimeOffset>(4);
                                mainGroup.Domain = csv.GetField<string>(5);

                                context.Database.ExecuteSqlCommand(String.Format("DBCC CHECKIDENT ('Groups', RESEED, {0})", mainGroup.Id - 1));
                                context.Groups.AddOrUpdate(
                                    x => x.Id,
                                    mainGroup
                                );
                                context.SaveChanges();
                            }
                            else if (discriminator == nameof(SubGroup))
                            {

                                var subGroup = new SubGroup();
                                subGroup.Id = csv.GetField<long>(0);
                                subGroup.IsDeleted = csv.GetField<bool>(1);
                                subGroup.Name = csv.GetField<string>(2);
                                subGroup.CreatedAt = csv.GetField<DateTimeOffset>(3);
                                subGroup.UpdatedAt = csv.GetField<DateTimeOffset>(4);
                                subGroup.MainGroupId = csv.GetField<long>(6);

                                var parentGroup = (MainGroup)context.Groups.Find(subGroup.MainGroupId);

                                if (parentGroup != null)
                                {
                                    subGroup.ParentGroup = parentGroup;
                                    context.Database.ExecuteSqlCommand(String.Format("DBCC CHECKIDENT ('Groups', RESEED, {0})", subGroup.Id - 1));
                                    context.Groups.AddOrUpdate(
                                        x => x.Id,
                                        subGroup
                                    );
                                    context.SaveChanges();
                                }
                            }
                            else if (discriminator == nameof(SubSubGroup))
                            {
                                var subSubGroup = new SubSubGroup();
                                subSubGroup.Id = csv.GetField<long>(0);
                                subSubGroup.IsDeleted = csv.GetField<bool>(1);
                                subSubGroup.Name = csv.GetField<string>(2);
                                subSubGroup.CreatedAt = csv.GetField<DateTimeOffset>(3);
                                subSubGroup.UpdatedAt = csv.GetField<DateTimeOffset>(4);
                                subSubGroup.SubGroupId = csv.GetField<long>(7);

                                var parentGroup = (SubGroup)context.Groups.Find(subSubGroup.SubGroupId);

                                if (parentGroup != null)
                                {
                                    subSubGroup.ParentGroup = parentGroup;
                                    context.Database.ExecuteSqlCommand(String.Format("DBCC CHECKIDENT ('Groups', RESEED, {0})", subSubGroup.Id - 1));
                                    context.Groups.AddOrUpdate(
                                        x => x.Id,
                                        subSubGroup
                                    );
                                    context.SaveChanges();
                                }
                            }
                        }
                        csv.Dispose();
                    }
                }

                //patch FirstTimeApproved for Survey CR 2023 item 4.
                //begin
                /*int[] status = { 30, 10 };
                var surveys = context.Survey.Where(t => t.SurveyStatus.HasValue && status.Contains(t.SurveyStatus.Value) && t.FirstTimeApproved==false ).ToList();
                if(surveys.Any() )
                {
                    surveys.ForEach(t => t.FirstTimeApproved = true);
                    context.SaveChanges();
                }*/
                //end


                //patch to fix NPARKS-52
                //var permitToPatch = context.ResearchPermitApplications.Where(t => t.ResearchTypeId!=null && t.Id== 40538 && t.SpecimenSpeciesName==null).ToList();
                /*try
                {
                    var permitToPatch = context.ResearchPermitApplications.Where(t => t.ResearchTypeId != null && t.SpecimenSpeciesName == null).ToList();
                    if (permitToPatch.Any())
                    {
                        foreach (var permit in permitToPatch)
                        {
                            if (permit.AtResearchApplication != null)
                            {
                                permit.SpecimenSpeciesName = permit.AtResearchApplication.SpecimenSpeciesName;
                            }

                        }
                        context.SaveChanges();
                    }
                }
                catch
                {
                    //don't do any. This patch code will be removed once done.
                }*/

                //var users = context.Users;
                //var publicGroup = (MainGroup)context.Groups.FirstOrDefault(g => g.Name == "PUBLIC");
                //foreach (var user in users)
                //{
                //    user.Group = publicGroup;
                //}
                //context.SaveChanges();

                //using (var sr = new StreamReader(Path.Combine(binPath, @"Group.csv")))
                //{
                //    var csv = new CsvReader(sr);
                //    while (csv.Read())
                //    {
                //        var mainGroup = new MainGroup();
                //        mainGroup.Name = csv.GetField<string>(0);
                //        mainGroup.Domain = csv.GetField<string>(1);

                //        migrationService.AddMainGroup(mainGroup);
                //    }
                //}

                //using (var sr = new StreamReader(Path.Combine(binPath, @"SubGroup.csv")))
                //{
                //    var csv = new CsvReader(sr);
                //    while (csv.Read())
                //    {
                //        var subGroup = new SubGroup();
                //        subGroup.Name = csv.GetField<string>(0);

                //        migrationService.AddSubGroup(subGroup, csv.GetField<string>(1));
                //    }
                //}

                //using (var sr = new StreamReader(Path.Combine(binPath, @"SubSubGroup.csv")))
                //{
                //    var csv = new CsvReader(sr);
                //    while (csv.Read())
                //    {
                //        var subSubGroup = new SubSubGroup();
                //        subSubGroup.Name = csv.GetField<string>(0);

                //        migrationService.AddSubSubGroup(subSubGroup, csv.GetField<string>(1));
                //    }
                //}

                //patch for affected applications due to NPARKS-102
                //Begin
                /*var permitToBePatched = context.ResearchPermitApplications
                    .Where(t => t.Status.Any(s => s.Id == 2) && t.FieldSurveyTeamMembers.Any(f => f.AcknowledgeSignStatus >= 2 && f.AcknowledgeSignStatus != 3))
                    .Select(t => t).ToList();


                var statusesToRemove = context.ResearchPermitStatuses.Where(t => t.Id == 2).Take(1).SingleOrDefault();
                if (statusesToRemove != null && permitToBePatched.Any())
                {
                    foreach (ResearchPermitApplication permitApplication in permitToBePatched)
                    {
                        permitApplication.Status.Remove(statusesToRemove);
                    }
                    context.SaveChanges();
                }*/
                //End

                //SGDRM CR migration. Run one time only
                //BEGIN
                /*try
                {
                    //Migrate First and Last Name into PersonName field.
                    var email_template = context.EmailTemplates.Where(t => t.EmailBody.Contains("<first_name>")).ToList();
                    if (email_template.Any())
                    {
                        foreach (var et in email_template)
                        {
                            et.EmailBody = et.EmailBody.Replace("<first_name>", "<person_name>");

                        }
                        context.SaveChanges();
                    }

                    //Migrate First and Last Name into PersonName field.
                    *//*var userToMigrate = context.Users.Where(t => t.PersonName == null).ToList();
                    if (userToMigrate.Any())
                    {
                        foreach (var user in userToMigrate)
                        {
                            var namesPart = new List<string>();
                            if (!string.IsNullOrEmpty(user.FirstName))
                            {
                                namesPart.Add(user.FirstName);
                            }
                            if (!string.IsNullOrEmpty(user.LastName))
                            {
                                namesPart.Add(user.LastName);
                            }

                            user.PersonName = string.Join(" ", namesPart);

                        }
                        context.SaveChanges();
                    }*//*
                }
                catch
                {
                    //don't do any. This patch code will be removed once done.
                }*/


                //END

                string CurrentYear = DateTime.Now.Year.ToString();

                var parameters = new SystemParameters()
                {
                    AccountInactivePeriod = 90, //3 * 30
                    PasswordExpiryPeriod = 90, //3 * 30
                    AuditLogsStoragePeriod = 90, //3 * 30
                    SightingsMinVotesQualify = 4,
                    SightingsMinPercentAgree = 50.0f,
                    SightingsFeaturedPeriodDays = 30,
                    SightingsFeaturedLikeNum = 4,
                    PaginationPageSize = 10,
                    LatestVerCodeiOS = 44,    //44 is current latet version. 25-Aug-2022
                    LatestVerCodeAndroid = 2217, //2217 is current latet version. 25-Aug-2022
                };
                migrationService.AddFirstSystemParameters(parameters);

                var header = new PageHeader()
                {
                    NParkLogoName = "Corporate-logo.webp",
                    TitleLogoName = "logo.webp",
                    GovLogoName = "govt-logo.jpg",
                    BackgroundImgName = "headerbg.webp"
                };
                migrationService.AddPageHeader(header);

                var footer = new PageFooter()
                {
                    Copyright = "© " + CurrentYear + " National Parks Board.",
                    PrivacyLink = "https://www.nparks.gov.sg/privacy-statement",
                    TermsLink = "https://www.nparks.gov.sg/terms-of-use",
                    FacebookLink = "https://www.facebook.com/",
                    InstagramLink = "https://www.instagram.com/",
                    TwitterLink = "https://twitter.com/"
                };
                migrationService.AddPageFooter(footer);

                var contact = new HomepageContact()
                {
                    GeneralName = "Koh Choh Shin (Mr)",
                    GeneralContact = "65-64651677",
                    GeneralEmail = "<EMAIL>",
                    TechName = "Technical Specialist",
                    TechContact = "65-62587510",
                    TechEmail = ConfigurationManager.AppSettings["SupportEmail"] ?? "<EMAIL>"
                };
                migrationService.AddHomepageContact(contact);

                var appInfo = new HomepageAppInfo()
                {
                    LogoImgName = "nparks.webp",
                    Description = "Seen an interesting animal or plant? You can now share it using the SGBioAtlas app! The SGBioAtlas is a citizen science-based app for you to share your biodiversity sightings. It uses crowd-sourced information to map the distribution of flora and fauna throughout Singapore.",
                    PlayStoreLink = "https://play.google.com/store/apps/details?id=sg.gov.nparks.BiodiversityApp&hl=en",
                    AppStoreLink = "https://itunes.apple.com/sg/app/sgbioatlas/id975080923?mt=8"
                };
                migrationService.AddHomepageAppInfo(appInfo);

                migrationService.AddResearchPermitStatus(new ResearchPermitStatus(Research.Permit.Status.Draft));
                migrationService.AddResearchPermitStatus(new ResearchPermitStatus(Research.Permit.Status.InProgress));
                migrationService.AddResearchPermitStatus(new ResearchPermitStatus(Research.Permit.Status.Approved));
                migrationService.AddResearchPermitStatus(new ResearchPermitStatus(Research.Permit.Status.Rejected));
                migrationService.AddResearchPermitStatus(new ResearchPermitStatus(Research.Permit.Status.Original));
                migrationService.AddResearchPermitStatus(new ResearchPermitStatus(Research.Permit.Status.Issued));
                migrationService.AddResearchPermitStatus(new ResearchPermitStatus(Research.Permit.Status.UserAmended));
                migrationService.AddResearchPermitStatus(new ResearchPermitStatus(Research.Permit.Status.ManagerAmended));
                migrationService.AddResearchPermitStatus(new ResearchPermitStatus(Research.Permit.Status.Discard));
                

#if DEBUG
                migrationService.AddResearchType(new ResearchType() { Name = "R1" });
                migrationService.AddHabitit(new ConfHabitat() { Name = "Forest" });
                migrationService.AddHabitit(new ConfHabitat() { Name = "Seawall" });
                migrationService.AddHabitit(new ConfHabitat() { Name = "Sea" });
                migrationService.AddHabitit(new ConfHabitat() { Name = "Marine" });
                migrationService.AddHabitit(new ConfHabitat() { Name = "Healing Garden" });
#endif

                

            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
