﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class GuideDetail : Entity<long>,IDescribableEntity
    {
        public string Title { get; set; }
        public string FileName { get; set; }
        public string Version { get; set; }
        public string CheckSum { get; set; }
        public int FileSize { get; set; }
        public bool IsEnable { get; set; }
        [DefaultValue(typeof(long), "0")]
        public long TotalDownloads { get; set; }

        public string Describe()
        {
            return "{ Title : \"" + Title + "\", FileName : \"" + FileName + "\", Version : \"" + Version + "\", CheckSum : \"" + CheckSum
                + "\", FileSize : \"" + FileSize + "\", IsEnable : \"" + IsEnable + "\", TotalDownloads : \"" + TotalDownloads  + "}";
        }
    }
}
