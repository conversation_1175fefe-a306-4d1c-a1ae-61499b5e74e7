// <auto-generated />
namespace BIOME.Services
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class ProfileImageName : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(ProfileImageName));
        
        string IMigrationMetadata.Id
        {
            get { return "201802060912592_ProfileImageName"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
