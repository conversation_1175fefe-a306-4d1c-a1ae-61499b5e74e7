﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIOME.Models
{
    public class ResearchStudyLocation : Entity<long>,IDescribableEntity 
    {
        public int Access { get; set; }
        public string Remarks { get; set; }
        public string FeatureId { get; set; }
        public string Describe()
        {
            return "{ Access : \"" + Access + "\", Remarks : \"" + Remarks + "\", FeatureId : \"" + FeatureId + "}";
        }
        public virtual GISLocation Location { get; set; }

        [JsonIgnore]
        public virtual ResearchApplication ResearchApplication { get; set; }

        public ResearchStudyLocation()
        {
            Access = 2;
        }
    }
}
